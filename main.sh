#!/bin/bash

# Global Docker network configuration
DOCKER_NETWORK="--network my_network"
# Global Rclone remote configuration
RCLONE_REMOTE="Eyunion"

# Source all necessary files
for file in ./*/[^_]*.sh; do
    source "$file"
done

install_all_utilities

RED='\033[0;31m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color
ip_regex='^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$'
declare -a ports_array
declare -a iptable_array
ports_holder=""
wireguard_client_ip=""
interface_file_path="/wg_config/config/wg_confs/wg0.conf"
ip_subnet=""
server_port=""
remote_host=""
server_ip=$(get_public_ip)
provider=""

if command -v ufw >/dev/null 2>&1; then
    if ufw status | grep -q "Status: active"; then
        sudo ufw disable
    fi 
fi

create_menu() {
    local title="$1"
    shift
    local options=("$@")

    # Find the length of the longest option
    local max_length=0
    for option in "${options[@]}"; do
        if [ ${#option} -gt $max_length ]; then
            max_length=${#option}
        fi
    done

    # Add some padding
    local box_width=$((max_length + 6))

    # Create the top of the box
    echo -e "${CYAN}╔$(printf '═%.0s' $(seq 1 $box_width))╗${NC}"
    
    # Print the title
    printf "${CYAN}║${NC} %-${box_width}s ${CYAN}║${NC}\n" "$title"
    
    # Print the separator
    echo -e "${CYAN}╠$(printf '═%.0s' $(seq 1 $box_width))╣${NC}"

    # Print each option
    for option in "${options[@]}"; do
        printf "${CYAN}║${NC} %-${box_width}s ${CYAN}║${NC}\n" "$option"
    done

    # Create the bottom of the box
    echo -e "${CYAN}╚$(printf '═%.0s' $(seq 1 $box_width))╝${NC}"
}

main_menu() {
    while true; do
        options=(
            "01. Docker Menu"
            "02. Rclone Menu"
            "03. Jellyfin Menu"
            "04. Jdownloader Menu"
            "05. Gluetun Menu"
            "06. Vpn Menu"
            "07. Utilities Menu"
            "08. File Operations Menu"
            "09. Certificate Menu"
            "10. Headscale Menu"
            "11. Encrypt Folder"
            "12. CasaOS Menu"
            "13. Database Menu"
            "14. File Server Menu"
            "15. OpenWebUI Menu"
            "16. AdGuard Home Menu"
            "17. SOCKS5 Gluetun Menu"
            "18. ByteStash Menu"
            "19. Exit"
        )

        create_menu "Main Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) docker_menu ;;
            2) rclone_menu ;;
            3) jellyfin_menu ;;
            4) jdownloader_menu ;;
            5) gluetun_menu ;;
            6) vpn_menu ;;
            7) utilities_menu ;;
            8) file_operations_main_menu ;;
            9) certificate_menu ;;
            10) headscale_menu ;;
            11) encrypt_folder ;;
            12) casaos_menu ;;
            13) database_menu ;;
            14) file_server_menu ;;
            15) openweb_ui_menu ;;
            16) adguard_menu ;;
            17) socket5_gluetun_menu ;;
            18) bytestash_menu ;;
            19) echo "Exiting..."; exit 0 ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo  # Add a blank line for better readability
    done
}

main_menu