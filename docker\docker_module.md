# Docker Module

This module provides comprehensive Docker management functionality including installation, container management, image handling, and more.

## Files

### docker_functions.sh
Core Docker utility functions for managing the Docker environment.

**Key Functions:**
- `log_error()`: Logs error messages with red color formatting
- `log_debug()`: Function for debug message logging
- `install_docker()`: Installs Docker and sets up the required environment
- `remove_docker()`: Completely removes Docker from the system
- `restart_unless_stopped()`: Restarts Docker containers unless explicitly stopped
- `show_docker_status()`: Displays the current status of Docker containers
- `clean_docker_system()`: Cleans up Docker system resources (unused containers, images, etc.)
- `rename_container()`: Renames a Docker container
- `remove_container()`: Removes a Docker container

### docker_menu.sh
Provides menu-driven interfaces for Docker management.

**Key Functions:**
- `create_menu()`: Creates a formatted menu display
- `docker_menu()`: Main Docker management menu
- `docker_container_menu()`: Menu for container-specific operations
- `docker_image_menu()`: Menu for image-specific operations
- `handle_custom_image()`: Handles custom Docker image operations
- `push_image()`: Pushes images to Docker registries

### docker_pull_push.sh
Advanced functions for Docker image pulling and pushing with retries, verification, and manifest management.

**Key Functions:**
- `get_valid_digest()`: Gets a valid digest for a Docker image
- `try_digest_method()`: Attempts different methods to get image digests
- `pull_image_with_retry()`: Pulls Docker images with automatic retry
- `check_rate_limits()`: Checks Docker Hub rate limits
- `get_image_digest()`: Gets the digest of a local Docker image
- `get_remote_digest()`: Gets the digest of a remote Docker image
- `process_manifest()`: Processes Docker image manifests
- `needs_update()`: Checks if an image needs to be updated
- `check_docker()`: Verifies Docker is running
- `docker_login()`: Handles Docker registry login
- `verify_push_access()`: Verifies push access to Docker registries
- `push_image()`: Pushes images to Docker registries
- `check_manifest_needs_update()`: Checks if a manifest needs updating
- `create_manifest()`: Creates Docker image manifests
- `update_favorite_images()`: Updates favorite/commonly used images
- `check_image_exists()`: Checks if an image exists
- `docker_pull_push_main()`: Main function for pulling/pushing operations

## Usage

The Docker module provides comprehensive Docker management capabilities with robust error handling and retries. It's designed to simplify Docker operations with user-friendly menus and reliable functionality.

To access Docker functionality:
1. Select "Docker Menu" from the main menu
2. Choose from container, image, or system management options
3. Follow the interactive prompts to perform Docker operations

## Integration with Other Modules

The Docker module is foundational for other modules that rely on containerization. Any functionality that requires Docker containers will depend on this module's functions for proper operation. 