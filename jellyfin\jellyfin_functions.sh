#!/bin/bash

set -e

# Common functions
check_container_exists() {
    docker ps -a --format '{{.Names}}' | grep -q "^$1$"
}

stop_and_remove_container() {
    echo "Stopping and removing existing $1 container..."
    docker stop "$1" && docker rm "$1"
}

create_volumes() {
    echo "Creating new volumes..."
    local volumes=("$@")
    for vol in "${volumes[@]}"; do
        docker volume rm "$vol" 2>/dev/null || true
        docker volume create "$vol"
    done
}

get_rclone_mounts() {
    mount | grep rclone | awk '{print $3}'
}

select_rclone_mounts() {
    local selected_mounts=()
    for mount in "$@"; do
        read -p "Add mount $mount to Jellyfin? (y/n): " add_mount
        if [[ $add_mount =~ ^[Yy]$ ]]; then
            selected_mounts+=("$mount")
            docker_create_command+=("-v" "$mount:/media/$(basename "$mount")")
        fi
    done
}

# Backup function
backup_jellyfin() {
    local container_name="${1:-jellyfin}"
    if check_container_exists "$container_name"; then
        docker stop "$container_name"
        
        local timestamp=$(date +"%Y%m%d_%H%M%S")
        local backup_file="jellyfin-volumes-backup_${timestamp}.tar"
        local backup_dir="$RCLONE_REMOTEBackups/Jellyfin Backup/"
        
        docker run --rm --volumes-from "$container_name" -v "$(pwd):/backup" ubuntu tar cvf "/backup/$backup_file" /cache /config
        
        docker start "$container_name"
        
        echo "Executing rclone command: rclone mkdir \"$RCLONE_REMOTE:/Backups/Jellyfin Backup/\""
        rclone mkdir "$backup_dir"
        echo "Executing rclone command: rclone move \"$backup_file\" \"$RCLONE_REMOTE:/Backups/Jellyfin Backup/\" --progress --transfers 4 --checkers 8"
        rclone move "$backup_file" "$backup_dir" --progress --transfers 4 --checkers 8
    else
        echo "Container $container_name does not exist."
    fi
}

# Install function
install_jellyfin() {
    install_docker

    echo "Current rclone mounts:"
    get_rclone_mounts

    read -p "Do you want to change the rclone mounts? (y/n): " change_mounts
    [[ $change_mounts =~ ^[Yy]$ ]] && auto_mount

    local container_name="${1:-jellyfin}"
    local image_name="fazee6/jellyfin:latest"

    check_container_exists "$container_name" && stop_and_remove_container "$container_name"

    create_volumes "jellyfin-config" "jellyfin-cache"

    local docker_create_command=("docker" "create" "--name" "$container_name" "--user" "1000:1000" "--net=host" 
                                 "-v" "jellyfin-config:/config" "-v" "jellyfin-cache:/cache")

    echo "Available rclone mounts:"
    mapfile -t rclone_mounts < <(get_rclone_mounts)
    select_rclone_mounts "${rclone_mounts[@]}"

    docker_create_command+=("--restart=unless-stopped" "$image_name")

    echo "Docker create command: ${docker_create_command[*]}"
    "${docker_create_command[@]}"

    docker start "$container_name"
}

# Restore function
restore_jellyfin_backup() {
    install_docker

    echo "Current rclone mounts:"
    get_rclone_mounts

    read -p "Are these mounts okay or do you want to edit them? (ok/edit): " change_mounts
    [[ $change_mounts == "edit" ]] && auto_mount

    mapfile -t mounted_remotes < <(get_rclone_mounts)

    echo "Select the remote to download the backup from:"
    select backup_remote in "${mounted_remotes[@]}"; do
        [[ -n $backup_remote ]] && break
        echo "Invalid selection. Please try again."
    done

    local backup_path="${backup_remote}/Backups/Jellyfin Backup/"
    echo "Looking for backups in $backup_path..."
    [[ ! -d "$backup_path" ]] && { echo "Backup directory not found: $backup_path"; exit 1; }

    mapfile -t backup_files < <(ls -t "$backup_path" | head -n 4)
    [[ ${#backup_files[@]} -eq 0 ]] && { echo "No backup found in $backup_path"; exit 1; }

    echo "Select the backup file to restore:"
    select backup_filename in "${backup_files[@]}"; do
        [[ -n $backup_filename ]] && break
        echo "Invalid selection. Please try again."
    done

    echo "Copying backup file to current directory..."
    echo "Executing rclone command: rclone copy --progress --checkers 4 --transfers 4 \"$RCLONE_REMOTE:/Backups/Jellyfin Backup/${backup_filename}\" ."
    rclone copy --progress --checkers 4 --transfers 4 "${backup_path}${backup_filename}" .

    local container_name="${1:-jellyfin}"
    local image_name="fazee6/jellyfin:latest"

    check_container_exists "$container_name" && stop_and_remove_container "$container_name"

    create_volumes "jellyfin-config" "jellyfin-cache"

    local docker_create_command=("docker" "create" "--name" "$container_name" "--user" "1000:1000" "--net=host" 
                                 "-v" "jellyfin-config:/config" "-v" "jellyfin-cache:/cache")

    echo "Select rclone mounts to add to Jellyfin:"
    select_rclone_mounts "${mounted_remotes[@]}"

    docker_create_command+=("--restart=unless-stopped" "$image_name")

    echo "Pulling Jellyfin image..."
    docker pull "$image_name"

    echo "Docker create command: ${docker_create_command[*]}"
    "${docker_create_command[@]}"

    echo "Restoring data from $backup_filename..."
    docker run --rm --volumes-from "$container_name" -v "$(pwd):/backup" ubuntu tar xvf "/backup/$backup_filename" -C /

    docker start "$container_name"

    echo "Deleting local backup file..."
    rm "$backup_filename" || echo "Failed to delete local backup file (may not exist)"
    echo "Jellyfin restoration complete!"
}