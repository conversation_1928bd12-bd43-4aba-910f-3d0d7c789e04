#!/bin/bash

# This script manages systemd-resolved configuration to free up or restore port 53
# for applications like AdGuard Home on Ubuntu 22.04.

# --- Function to fix port conflict ---
fix_adguard_port_conflict() {
    echo "--- Configuring systemd-resolved to free up port 53 ---"

    # Check if running as root
    if [ "$(id -u)" -ne 0 ]; then
      echo "This function must be run as root. Please use sudo." >&2
      return 1
    fi

    # Step 1: Create the configuration directory if it doesn't exist
    echo "[1/5] Creating /etc/systemd/resolved.conf.d directory..."
    mkdir -p /etc/systemd/resolved.conf.d
    if [ $? -ne 0 ]; then
        echo "Error creating directory. Aborting." >&2
        return 1
    fi

    # Step 2: Create the adguardhome.conf file
    echo "[2/5] Creating /etc/systemd/resolved.conf.d/adguardhome.conf..."
    cat > /etc/systemd/resolved.conf.d/adguardhome.conf <<EOF
[Resolve]
DNS=127.0.0.1
DNSStubListener=no
EOF
    if [ $? -ne 0 ]; then
        echo "Error creating adguardhome.conf. Aborting." >&2
        # Attempt cleanup
        rm -f /etc/systemd/resolved.conf.d/adguardhome.conf
        return 1
    fi

    # Step 3: Backup existing /etc/resolv.conf (if it's not already a symlink to the target)
    RESOLV_CONF="/etc/resolv.conf"
    TARGET_LINK="/run/systemd/resolve/resolv.conf"
    BACKUP_FILE="/etc/resolv.conf.backup_$(date +%Y%m%d%H%M%S)" # Unique backup

    echo "[3/5] Checking /etc/resolv.conf..."
    if [ -L "$RESOLV_CONF" ] && [ "$(readlink -f "$RESOLV_CONF")" = "$TARGET_LINK" ]; then
        echo "/etc/resolv.conf is already linked correctly. Skipping backup and link creation."
    elif [ -e "$RESOLV_CONF" ]; then # Check if it exists (file or link)
        echo "Backing up existing $RESOLV_CONF to $BACKUP_FILE..."
        # Use cp and then rm for atomicity, or mv if confident
        cp -a "$RESOLV_CONF" "$BACKUP_FILE"
        if [ $? -ne 0 ]; then
            echo "Error backing up $RESOLV_CONF. Aborting." >&2
            # Attempt cleanup
             rm -f /etc/systemd/resolved.conf.d/adguardhome.conf
            return 1
        fi
        rm -f "$RESOLV_CONF" # Remove original/symlink after backup
        # Step 4: Create the symbolic link
        echo "[4/5] Creating symbolic link for /etc/resolv.conf..."
        ln -s "$TARGET_LINK" "$RESOLV_CONF"
        if [ $? -ne 0 ]; then
            echo "Error creating symbolic link. Attempting to restore backup." >&2
            mv "$BACKUP_FILE" "$RESOLV_CONF" # Try to restore
            # Attempt cleanup
            rm -f /etc/systemd/resolved.conf.d/adguardhome.conf
            return 1
        fi
    else
         # If /etc/resolv.conf doesn't exist
         echo "$RESOLV_CONF does not exist."
         echo "[4/5] Creating symbolic link for /etc/resolv.conf..."
         ln -s "$TARGET_LINK" "$RESOLV_CONF"
         if [ $? -ne 0 ]; then
            echo "Error creating symbolic link. Aborting." >&2
            # Attempt cleanup
            rm -f /etc/systemd/resolved.conf.d/adguardhome.conf
            return 1
         fi
    fi


    # Step 5: Reload or restart systemd-resolved service
    echo "[5/5] Reloading systemd-resolved service..."
    systemctl reload-or-restart systemd-resolved
    if [ $? -ne 0 ]; then
        echo "Error reloading systemd-resolved. Manual check needed." >&2
        # Consider the operation potentially successful but requires manual verification
        echo "Configuration might be partially applied. Please check 'systemctl status systemd-resolved' and '$RESOLV_CONF'." >&2
        return 1 # Indicate an issue occurred
    fi

    echo "--- Configuration complete ---"
    echo "systemd-resolved has been configured. Port 53 on 127.0.0.1 should now be available."
    return 0
}

# --- Function to revert port conflict fix ---
revert_adguard_port_fix() {
    echo "--- Reverting systemd-resolved configuration ---"

    # Check if running as root
    if [ "$(id -u)" -ne 0 ]; then
      echo "This function must be run as root. Please use sudo." >&2
      return 1
    fi

    # Step 1: Remove the custom configuration file
    CONF_FILE="/etc/systemd/resolved.conf.d/adguardhome.conf"
    echo "[1/3] Removing custom configuration file $CONF_FILE..."
    if [ -f "$CONF_FILE" ]; then
        rm "$CONF_FILE"
        if [ $? -ne 0 ]; then
            echo "Warning: Could not remove $CONF_FILE. Manual check might be needed." >&2
        else
            echo "Removed $CONF_FILE."
        fi
    else
        echo "$CONF_FILE does not exist. Skipping removal."
    fi

    # Step 2: Restore the original /etc/resolv.conf
    # Find the most recent backup
    RESOLV_CONF="/etc/resolv.conf"
    TARGET_LINK="/run/systemd/resolve/resolv.conf"
    DEFAULT_STUB_LINK="/run/systemd/resolve/stub-resolv.conf"
    BACKUP_PATTERN="/etc/resolv.conf.backup_*"
    LATEST_BACKUP=$(ls -t $BACKUP_PATTERN 2>/dev/null | head -n 1)

    echo "[2/3] Restoring /etc/resolv.conf..."
    # Check if the current resolv.conf is the symlink we created
    if [ -L "$RESOLV_CONF" ] && [ "$(readlink -f "$RESOLV_CONF")" = "$TARGET_LINK" ]; then
        echo "$RESOLV_CONF is the symlink created by the 'fix' function."
        rm "$RESOLV_CONF" # Remove the symlink first
        if [ -n "$LATEST_BACKUP" ] && [ -f "$LATEST_BACKUP" ]; then
            echo "Restoring $RESOLV_CONF from latest backup: $LATEST_BACKUP..."
            cp -a "$LATEST_BACKUP" "$RESOLV_CONF"
            if [ $? -ne 0 ]; then
                echo "Error restoring $RESOLV_CONF from backup $LATEST_BACKUP." >&2
                echo "Attempting to link to default stub resolver: $DEFAULT_STUB_LINK"
                ln -sf "$DEFAULT_STUB_LINK" "$RESOLV_CONF"
            else
                echo "Restored $RESOLV_CONF from $LATEST_BACKUP successfully."
                # Optional: remove the backup file after successful restore
                # rm "$LATEST_BACKUP"
            fi
        else
            echo "Warning: No backup file matching '$BACKUP_PATTERN' found." >&2
            echo "Linking $RESOLV_CONF to default stub resolver: $DEFAULT_STUB_LINK"
            ln -sf "$DEFAULT_STUB_LINK" "$RESOLV_CONF"
            echo "If this is not correct for your original setup, please restore manually."
        fi
    elif [ -f "$RESOLV_CONF" ] && [ ! -L "$RESOLV_CONF" ]; then
         echo "$RESOLV_CONF exists and is a regular file. Assuming it was already restored or manually changed. Skipping restore."
    elif [ ! -e "$RESOLV_CONF" ]; then
         echo "$RESOLV_CONF does not exist. Linking to default stub resolver: $DEFAULT_STUB_LINK"
         ln -sf "$DEFAULT_STUB_LINK" "$RESOLV_CONF"
    else
         echo "$RESOLV_CONF exists but is not the expected symlink or a regular file. Skipping restore. Manual check advised."
    fi


    # Step 3: Reload or restart systemd-resolved service to re-enable stub listener
    echo "[3/3] Reloading systemd-resolved service..."
    systemctl reload-or-restart systemd-resolved
    if [ $? -ne 0 ]; then
        echo "Error reloading systemd-resolved. Please check the service status manually." >&2
        return 1 # Indicate an issue occurred
    fi

    echo "--- Revert complete ---"
    echo "Attempted to restore systemd-resolved configuration."
    echo "The stub listener (on **********:53) should be active again if the revert was successful."
    return 0
}