#!/bin/bash

get_next_server_number() {
    local prefix=$1
    local counter=1
    while true; do
        if ! docker ps -a --format '{{.Names}}' | grep -q "^${prefix}_${counter}$"; then
            echo "$counter"
            return
        fi
        ((counter++))
    done
}

get_architecture_image() {
    # Check CPU architecture
    local arch=$(uname -m)
    if [[ "$arch" == "aarch64" ]] || [[ "$arch" == "arm"* ]]; then
        echo "fazee6/file-server:arm"
    else
        echo "fazee6/file-server:x86"
    fi
}

create_file_server() {
    # Get the next available server number
    local server_number=$(get_next_server_number "file_server")
    local container_name="file_server_${server_number}"
    local docker_image=$(get_architecture_image)

    # Get port from user
    while true; do
        read -rp "$(echo -e ${YELLOW}"Enter the port number for the file server: "${NC})" port
        if [[ "$port" =~ ^[0-9]+$ ]] && [ "$port" -ge 1 ] && [ "$port" -le 65535 ]; then
            if ! check_port_in_use "$port"; then
                break
            else
                echo -e "${RED}Port $port is already in use. Please choose a different port.${NC}"
            fi
        else
            echo -e "${RED}Please enter a valid port number (1-65535).${NC}"
        fi
    done

    # Get username
    read -rp "$(echo -e ${YELLOW}"Enter username for the file server: "${NC})" username
    while [[ -z "$username" ]]; do
        echo -e "${RED}Username cannot be empty.${NC}"
        read -rp "$(echo -e ${YELLOW}"Enter username for the file server: "${NC})" username
    done

    # Get password
    read -rp "$(echo -e ${YELLOW}"Enter password for the file server: "${NC})" password
    while [[ -z "$password" ]]; do
        echo -e "${RED}Password cannot be empty.${NC}"
        read -rp "$(echo -e ${YELLOW}"Enter password for the file server: "${NC})" password
    done

    # Get mount path
    read -rp "$(echo -e ${YELLOW}"Enter the path to mount (default: /): "${NC})" mount_path
    mount_path=${mount_path:-/}

    # Verify if the path exists
    if [ ! -d "$mount_path" ]; then
        echo -e "${RED}The specified path does not exist.${NC}"
        return
    fi

    echo -e "${GREEN}Creating file server with the following configuration:${NC}"
    echo -e "Container Name: ${CYAN}$container_name${NC}"
    echo -e "Port: ${CYAN}$port${NC}"
    echo -e "Username: ${CYAN}$username${NC}"
    echo -e "Mount Path: ${CYAN}$mount_path${NC}"
    echo -e "Docker Image: ${CYAN}$docker_image${NC}"

    install_docker

    read -rp "$(echo -e ${YELLOW}"Do you want to proceed? (y/n): "${NC})" confirm
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        docker run -d \
            --name="$container_name" \
            --network=my_network \
            -p "$port:$port" \
            -e "FILE_SERVER_PORT=$port" \
            -e "FILE_SERVER_USER=$username" \
            -e "FILE_SERVER_PASS=$password" \
            -v "$mount_path:/mnt/host" \
            "$docker_image"

        if [ $? -eq 0 ]; then
            echo -e "${GREEN}File server created successfully!${NC}"
            echo -e "Access it at ${CYAN}http://localhost:$port${NC}"
        else
            echo -e "${RED}Failed to create file server.${NC}"
        fi
    else
        echo -e "${YELLOW}Operation cancelled.${NC}"
    fi
}

check_port_in_use() {
    local port=$1
    if lsof -i :"$port" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

create_ftp_server() {
    # Get the next available server number
    local server_number=$(get_next_server_number "ftpserver")
    local container_name="ftpserver_${server_number}"
    local default_path="/root"
    local public_ip=$(get_public_ip)

    # Get port from user
    while true; do
        read -rp "$(echo -e ${YELLOW}"Enter the port number for the FTP server (will be mapped to port 21): "${NC})" port
        if [[ "$port" =~ ^[0-9]+$ ]] && [ "$port" -ge 1 ] && [ "$port" -le 65535 ]; then
            if ! check_port_in_use "$port"; then
                break
            else
                echo -e "${RED}Port $port is already in use. Please choose a different port.${NC}"
            fi
        else
            echo -e "${RED}Please enter a valid port number (1-65535).${NC}"
        fi
    done

    # Confirm or change public IP
    echo -e "${CYAN}Detected public IP: $public_ip${NC}"
    read -rp "$(echo -e ${YELLOW}"Do you want to use a different IP? (y/n): "${NC})" change_ip
    if [[ "$change_ip" =~ ^[Yy]$ ]]; then
        read -rp "$(echo -e ${YELLOW}"Enter the public IP to use: "${NC})" public_ip
    fi

    # Ask about credentials
    read -rp "$(echo -e ${YELLOW}"Do you want to use default credentials (ftpuser/ftppass123)? (y/n): "${NC})" use_default
    if [[ "$use_default" =~ ^[Yy]$ ]]; then
        username="ftpuser"
        password="ftppass123"
    else
        read -rp "$(echo -e ${YELLOW}"Enter FTP username: "${NC})" username
        while [[ -z "$username" ]]; do
            echo -e "${RED}Username cannot be empty.${NC}"
            read -rp "$(echo -e ${YELLOW}"Enter FTP username: "${NC})" username
        done

        read -rp "$(echo -e ${YELLOW}"Enter FTP password: "${NC})" password
        while [[ -z "$password" ]]; do
            echo -e "${RED}Password cannot be empty.${NC}"
            read -rp "$(echo -e ${YELLOW}"Enter FTP password: "${NC})" password
        done
    fi

    # Get mount path
    read -rp "$(echo -e ${YELLOW}"Enter the path to mount (default: $default_path): "${NC})" mount_path
    mount_path=${mount_path:-$default_path}

    # Verify if the path exists
    if [ ! -d "$mount_path" ]; then
        echo -e "${RED}The specified path does not exist.${NC}"
        return
    fi

    echo -e "${GREEN}Creating FTP server with the following configuration:${NC}"
    echo -e "Container Name: ${CYAN}$container_name${NC}"
    echo -e "Port: ${CYAN}$port${NC} (mapped to 21)"
    echo -e "Public IP: ${CYAN}$public_ip${NC}"
    echo -e "Username: ${CYAN}$username${NC}"
    echo -e "Mount Path: ${CYAN}$mount_path${NC}"

    install_docker

    read -rp "$(echo -e ${YELLOW}"Do you want to proceed? (y/n): "${NC})" confirm
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        if [[ -z "$last_passive_port" ]]; then
            last_passive_port=30000
        fi
        local passive_port_start=$last_passive_port
        local passive_port_end=$((last_passive_port + 9))
        local passive_port_range="$passive_port_start-$passive_port_end"

        docker run -d \
            --name="$container_name" \
            --network=my_network \
            -p "$port:21" \
            -p "$passive_port_range":"$passive_port_range" \
            -e "PUBLICHOST=$public_ip" \
            -e "FTP_USER_NAME=$username" \
            -e "FTP_USER_PASS=$password" \
            -e "FTP_USER_HOME=/ftp/data" \
            -v "$mount_path:/ftp/data" \
            stilliard/pure-ftpd:hardened
        
        last_passive_port=$((last_passive_port + 10))

        if [ $? -eq 0 ]; then
            echo -e "${GREEN}FTP server created successfully!${NC}"
            echo -e "Connect using: ${CYAN}ftp://$public_ip:$port${NC}"
            echo -e "Username: ${CYAN}$username${NC}"
            echo -e "Password: ${CYAN}$password${NC}"
        else
            echo -e "${RED}Failed to create FTP server.${NC}"
        fi
    else
        echo -e "${YELLOW}Operation cancelled.${NC}"
    fi
}


remove_file_server() {
    local containers=$(docker ps -a --format '{{.Names}}' | grep "^file_server_")
    if [[ -z "$containers" ]]; then
        echo -e "${YELLOW}No running or stopped file servers found.${NC}"
        return
    fi

    echo -e "${CYAN}Available File Servers:${NC}"
    echo "$containers" | awk '{print NR ". " $0}'

    read -rp "$(echo -e ${YELLOW}"Enter the number of the file server to remove (or 'c' to cancel): "${NC})" choice
    if [[ "$choice" =~ ^[Cc]$ ]]; then
        echo -e "${YELLOW}Operation cancelled.${NC}"
        return
    fi

    local selected_container=$(echo "$containers" | sed -n "${choice}p")
    if [[ -z "$selected_container" ]]; then
        echo -e "${RED}Invalid choice.${NC}"
        return
    fi

    echo -e "${YELLOW}Removing file server: ${RED}$selected_container${NC}"
    docker rm -f "$selected_container"

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}File server removed successfully!${NC}"
    else
        echo -e "${RED}Failed to remove file server.${NC}"
    fi
}

remove_ftp_server() {
    local containers=$(docker ps -a --format '{{.Names}}' | grep "^ftpserver_")
    if [[ -z "$containers" ]]; then
        echo -e "${YELLOW}No running or stopped FTP servers found.${NC}"
        return
    fi

    echo -e "${CYAN}Available FTP Servers:${NC}"
    echo "$containers" | awk '{print NR ". " $0}'

    read -rp "$(echo -e ${YELLOW}"Enter the number of the FTP server to remove (or 'c' to cancel): "${NC})" choice
    if [[ "$choice" =~ ^[Cc]$ ]]; then
        echo -e "${YELLOW}Operation cancelled.${NC}"
        return
    fi

    local selected_container=$(echo "$containers" | sed -n "${choice}p")
    if [[ -z "$selected_container" ]]; then
        echo -e "${RED}Invalid choice.${NC}"
        return
    fi

    echo -e "${YELLOW}Removing FTP server: ${RED}$selected_container${NC}"
    docker rm -f "$selected_container"

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}FTP server removed successfully!${NC}"
    else
        echo -e "${RED}Failed to remove FTP server.${NC}"
    fi
}
