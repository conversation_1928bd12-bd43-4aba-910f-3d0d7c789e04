#!/bin/bash

# Source the functions
source "$(dirname "${BASH_SOURCE[0]}")/bytestash_functions.sh"
source "$(dirname "${BASH_SOURCE[0]}")/bytestash_config_functions.sh"
source "$(dirname "${BASH_SOURCE[0]}")/bytestash_container_functions.sh"

# ByteStash Menu
# Interactive menu for ByteStash code snippet storage management

# Function to display ByteStash menu
bytestash_menu() {
    while true; do
        local options=(
            "01. Install ByteStash"
            "02. Start ByteStash"
            "03. Stop ByteStash"
            "04. Restart ByteStash"
            "05. Show Status"
            "06. View Logs"
            "07. Update ByteStash"
            "08. ═══════════════════════════════════"
            "09. Backup ByteStash Data"
            "10. Restore ByteStash Data"
            "11. Schedule Auto Backup"
            "12. ═══════════════════════════════════"
            "13. Container Management"
            "14. Configuration Settings"
            "15. ═══════════════════════════════════"
            "16. Return to Main Menu"
            "17. Exit"
        )

        create_menu "ByteStash Code Snippet Storage" "${options[@]}"

        local choice
        read -rp "$(echo -e ${YELLOW}"Enter your choice (1-17): "${NC})" choice

        case $choice in
            1) install_bytestash ;;
            2) start_bytestash ;;
            3) stop_bytestash ;;
            4) restart_bytestash ;;
            5) show_bytestash_status ;;
            6) view_bytestash_logs ;;
            7) update_bytestash ;;
            8) echo -e "${CYAN}═══════════════════════════════════${NC}" ;;
            9) backup_bytestash ;;
            10) restore_bytestash ;;
            11) schedule_bytestash_backup ;;
            12) echo -e "${CYAN}═══════════════════════════════════${NC}" ;;
            13) bytestash_container_management ;;
            14) bytestash_configuration ;;
            15) echo -e "${CYAN}═══════════════════════════════════${NC}" ;;
            16) return ;;
            17) exit 0 ;;
            *) echo -e "${RED}Invalid choice. Please enter a number between 1 and 17.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done
}

# Function to view ByteStash logs
view_bytestash_logs() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║         ByteStash Logs               ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    if ! docker ps --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${RED}✗ ByteStash container is not running${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}Showing last 50 lines of logs (Press Ctrl+C to exit):${NC}"
    echo -e "${CYAN}════════════════════════════════════════════════════${NC}"
    
    docker logs -f --tail 50 "${BYTESTASH_CONTAINER_NAME}"
}

# Function to update ByteStash
update_bytestash() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║        Updating ByteStash            ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    echo -e "${YELLOW}⚠ This will update ByteStash to the latest version${NC}"
    echo -e "${YELLOW}Continue? [y/N]:${NC}"
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Update cancelled${NC}"
        return 1
    fi
    
    # Stop container if running
    if docker ps --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${YELLOW}Stopping ByteStash container...${NC}"
        stop_bytestash
    fi
    
    # Pull latest image
    echo -e "${YELLOW}Pulling latest ByteStash image...${NC}"
    if docker pull "${BYTESTASH_IMAGE}"; then
        echo -e "${GREEN}✓ Latest image pulled successfully${NC}"
    else
        echo -e "${RED}✗ Failed to pull latest image${NC}"
        return 1
    fi
    
    # Remove old container
    if docker ps -a --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${YELLOW}Removing old container...${NC}"
        docker rm "${BYTESTASH_CONTAINER_NAME}"
    fi
    
    # Reinstall with new image
    echo -e "${YELLOW}Creating new container with updated image...${NC}"
    install_bytestash
    
    echo -e "${GREEN}✓ ByteStash updated successfully!${NC}"
}

# Function for container management
bytestash_container_management() {
    while true; do
        local options=(
            "01. View Container Details"
            "02. Container Resource Usage"
            "03. Remove Container"
            "04. Recreate Container"
            "05. Export Container"
            "06. Container Network Info"
            "07. Return to ByteStash Menu"
        )

        create_menu "ByteStash Container Management" "${options[@]}"

        local choice
        read -rp "$(echo -e ${YELLOW}"Enter your choice (1-7): "${NC})" choice

        case $choice in
            1) show_container_details ;;
            2) show_container_resources ;;
            3) remove_bytestash_container ;;
            4) recreate_bytestash_container ;;
            5) export_bytestash_container ;;
            6) show_container_network ;;
            7) return ;;
            *) echo -e "${RED}Invalid choice. Please enter a number between 1 and 7.${NC}" ;;
        esac

        echo
    done
}



# Function for configuration settings
bytestash_configuration() {
    while true; do
        local options=(
            "01. View Current Configuration"
            "02. Change Port"
            "03. Change Data Directory"
            "04. Environment Variables"
            "05. Backup Configuration"
            "06. Return to ByteStash Menu"
        )

        create_menu "ByteStash Configuration" "${options[@]}"

        local choice
        read -rp "$(echo -e ${YELLOW}"Enter your choice (1-6): "${NC})" choice

        case $choice in
            1) show_current_config ;;
            2) change_bytestash_port ;;
            3) change_data_directory ;;
            4) manage_environment_variables ;;
            5) backup_configuration ;;
            6) return ;;
            *) echo -e "${RED}Invalid choice. Please enter a number between 1 and 6.${NC}" ;;
        esac

        echo
    done
}

# Function to show current configuration
show_current_config() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Current Configuration           ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    echo -e "${GREEN}Container Name:${NC} ${BYTESTASH_CONTAINER_NAME}"
    echo -e "${GREEN}Image:${NC} ${BYTESTASH_IMAGE}"
    echo -e "${GREEN}Port:${NC} ${BYTESTASH_PORT}"
    echo -e "${GREEN}Data Directory:${NC} ${BYTESTASH_DATA_DIR}"
    echo -e "${GREEN}Backup Remote:${NC} ${BYTESTASH_BACKUP_REMOTE}"
    
    if docker ps -a --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${GREEN}Environment Variables:${NC}"
        docker inspect "${BYTESTASH_CONTAINER_NAME}" --format '{{range .Config.Env}}{{println .}}{{end}}' | grep -E '^(BASE_PATH|JWT_SECRET|TOKEN_EXPIRY|ALLOW_NEW_ACCOUNTS|DEBUG|DISABLE_ACCOUNTS)'
    fi
}

# Function to schedule auto backup
schedule_bytestash_backup() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Schedule Auto Backup            ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    echo -e "${YELLOW}Select backup frequency:${NC}"
    echo -e "${CYAN}1.${NC} Daily at 2 AM"
    echo -e "${CYAN}2.${NC} Weekly (Sunday at 2 AM)"
    echo -e "${CYAN}3.${NC} Monthly (1st day at 2 AM)"
    echo -e "${CYAN}4.${NC} Custom schedule"
    echo -e "${CYAN}5.${NC} Remove scheduled backup"
    
    read -rp "$(echo -e ${YELLOW}"Enter your choice (1-5): "${NC})" schedule_choice
    
    local cron_schedule
    case $schedule_choice in
        1) cron_schedule="0 2 * * *" ;;
        2) cron_schedule="0 2 * * 0" ;;
        3) cron_schedule="0 2 1 * *" ;;
        4) 
            echo -e "${YELLOW}Enter cron schedule (e.g., '0 2 * * *' for daily at 2 AM):${NC}"
            read -r cron_schedule
            ;;
        5)
            # Remove existing cron job
            (crontab -l 2>/dev/null | grep -v "bytestash_backup") | crontab -
            echo -e "${GREEN}✓ Scheduled backup removed${NC}"
            return 0
            ;;
        *)
            echo -e "${RED}Invalid choice${NC}"
            return 1
            ;;
    esac
    
    # Create backup script path
    local backup_script="/usr/local/bin/bytestash_backup.sh"
    
    # Create backup script
    sudo tee "$backup_script" > /dev/null << EOF
#!/bin/bash
cd "$(pwd)"
source "./bytestash/bytestash_functions.sh"
source "./bytestash/bytestash_config_functions.sh"
backup_bytestash
EOF
    
    sudo chmod +x "$backup_script"
    
    # Add to crontab
    (crontab -l 2>/dev/null | grep -v "bytestash_backup"; echo "$cron_schedule $backup_script") | crontab -
    
    echo -e "${GREEN}✓ Backup scheduled successfully${NC}"
    echo -e "${GREEN}Schedule: $cron_schedule${NC}"
}