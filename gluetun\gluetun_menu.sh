gluetun_menu() {
    options=(
        "01. Add New Gluetun Container with PIA Config"
        "02. Add New Gluetun Container with FastestVPN Config"
        "03. Add New Gluetun Container with Custom Config"
        "04. Remove Gluetun Container"
        "05. Generate PIA Wireguard Config"
        "06. Return to Main Menu"
        "07. Exit"
    )

    while true; do
        create_menu "Gluetun Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) install_gluetun_pia ;;
            2) install_gluetun_fastestvpn ;;
            3) install_gluetun_custom ;;
            4) remove_gluetun ;;
            5) generate_pia_wireguard_file ;;
            6) return ;;
            7) exit ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done
}