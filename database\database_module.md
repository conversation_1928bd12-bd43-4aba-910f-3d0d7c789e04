# Database Module

This module provides functionality for setting up and managing database servers, primarily MySQL/MariaDB databases in Docker containers.

## Files

### database_functions.sh
Core functions for database server management.

**Key Functions:**
- `get_next_mysql_name()`: Gets the next available name for a MySQL container
- `check_mysql_port_available()`: Checks if a specific port is available for MySQL
- `get_next_mysql_port()`: Gets the next available port for MySQL
- `create_mysql_container()`: Creates and configures a MySQL/MariaDB container
- `list_mysql_containers()`: Lists all MySQL containers
- `remove_mysql_container()`: Removes a MySQL container

### database_menu.sh
Provides menu interface for database operations.

**Key Functions:**
- `database_menu()`: Main menu for database operations

## Usage

The Database module provides functionality for setting up and managing database servers for application data storage.

To access Database functionality:
1. Select "Database Menu" from the main menu
2. Choose from installation, configuration, or removal options
3. Follow the interactive prompts to configure your database server

## Integration with Other Modules

The Database module integrates with:
- Docker module for container management
- Various other modules may use the created databases for data storage 