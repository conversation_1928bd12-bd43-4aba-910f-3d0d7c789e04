print_xray_client_id() {
    ID=$(cat /etc/xray/config.json | jq -r '.inbounds[0].settings.clients[0].id')
    echo "$ID"
}

install_xray() {
    install_docker

    echo -e "${RED}╔═══════════════════════════════╗${NC}"
    echo -e "${RED}║     Installing Xray VPN       ║${NC}"
    echo -e "${RED}╚═══════════════════════════════╝${NC}"
    echo ""

    echo -e "${YELLOW}Pulling image from Docker Hub...${NC}"
    docker pull fazee6/xray:latest

    DIRECTORY="/etc/xray"
    FILE="/etc/xray/config.json"
    
    while true; do
        read -rp "Enter port to use with Xray server: " xray_port_input
        get_valid_port "$xray_port_input"
        if [ $? -eq 0 ]; then
            break
        fi
    done

    read -rp "Enter the client ID (press Enter for default): " CLIENT_ID
    DEFAULT_CLIENT_ID="b831381d-6224-4d43-ad4f-8cda48b30811"
    CLIENT_ID="${CLIENT_ID:-$DEFAULT_CLIENT_ID}"

    CONFIG_CONTENT=$(cat <<EOF
{
    "log": {
        "loglevel": "warning"
    },
    "inbounds": [
        {
            "port": "$xray_port_input",
            "protocol": "vmess",
            "settings": {
                "clients": [
                    {
                        "id": "$CLIENT_ID",
                        "level": 1,
                        "alterId": 0
                    }
                ],
                "disableInsecureEncryption": true
            },
            "streamSettings": {
                "network": "tcp",
                "security": "none",
                "tcpSettings": {
                    "header": {
                        "type": "none"
                    }
                }
            },
            "sniffing": {
                "enabled": true,
                "destOverride": ["http", "tls"]
            }
        }
    ],
    "outbounds": [
        {
            "protocol": "freedom",
            "settings": {
                "domainStrategy": "UseIP"
            }
        },
        {
            "tag": "blocked",
            "protocol": "blackhole",
            "settings": {}
        }
    ],
    "routing": {
        "domainStrategy": "IPIfNonMatch",
        "rules": [
            {
                "type": "field",
                "protocol": ["bittorrent"],
                "outboundTag": "blocked"
            }
        ]
    }
}
EOF
)

    if [ ! -d "$DIRECTORY" ]; then
        sudo mkdir -p "$DIRECTORY"
    fi

    echo "$CONFIG_CONTENT" | sudo tee "$FILE" > /dev/null

    stop_and_remove_container "xray"

    docker run -d --name xray \
        -v /etc/xray:/etc/xray \
        -p "$xray_port_input":"$xray_port_input" \
        $DOCKER_NETWORK \
        fazee6/xray:latest

    docker update --restart unless-stopped xray

    xray_client_id=$(print_xray_client_id)
    server_ip=$(get_public_ip)

    echo ""
    local max_length=$(( ${#server_ip} > ${#xray_port_input} ? ${#server_ip} : ${#xray_port_input} ))
    max_length=$(( max_length > ${#xray_client_id} ? max_length : ${#xray_client_id} ))
    local box_width=$((max_length + 15))

    echo -e "${BLUE}┏$(printf '━%.0s' $(seq 1 $box_width))┓${NC}"
    echo -e "${BLUE}┃        Xray Server Details          ┃${NC}"
    echo -e "${BLUE}┣$(printf '━%.0s' $(seq 1 $box_width))┫${NC}"
    printf "${BLUE}┃${NC} Server IP:  %-${max_length}s ${BLUE}┃${NC}\n" "$server_ip"
    printf "${BLUE}┃${NC} Port:       %-${max_length}s ${BLUE}┃${NC}\n" "$xray_port_input"
    printf "${BLUE}┃${NC} Client ID:  %-${max_length}s ${BLUE}┃${NC}\n" "$xray_client_id"
    echo -e "${BLUE}┗$(printf '━%.0s' $(seq 1 $box_width))┛${NC}"
    echo ""

    echo -e "${GREEN}Generating Xray client configuration...${NC}"
    local vmess_link="vmess://$(generate_xray_config "$server_ip" "$xray_port_input" "$xray_client_id")"
    
    echo -e "${BLUE}┏$(printf '━%.0s' $(seq 1 $box_width))┓${NC}"
    echo -e "${BLUE}┃      Xray Client Configuration      ┃${NC}"
    echo -e "${BLUE}┣$(printf '━%.0s' $(seq 1 $box_width))┫${NC}"
    echo -e "${BLUE}┃${NC} VMess Link:                      ${BLUE}┃${NC}"
    echo "$vmess_link"
    echo ""
}

# Add this new function to generate the Xray client config
generate_xray_config() {
    local server_ip="$1"
    local port="$2"
    local client_id="$3"

    local config=$(cat <<EOF
{
    "v": "2",
    "ps": "Xray VPN",
    "add": "${server_ip}",
    "port": ${port},
    "id": "${client_id}",
    "aid": 0,
    "net": "tcp",
    "type": "none",
    "host": "",
    "path": "",
    "tls": "none",
    "sni": "",
    "fp": ""
}
EOF
)
    # Convert to base64 and remove newlines
    echo "$config" | base64 | tr -d '\n'
}