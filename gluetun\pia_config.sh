create_pia_generator_file() {

    echo "Generating configuration to show all PIA regions..."

cat <<EOF > generate-config.py
from piawg import piawg
from getpass import getpass
from datetime import datetime
import requests
import json
import sys

pia = piawg()

def get_available_regions():
    # Use newest version of PIA serverlist (v6)
    r = requests.get('https://serverlist.piaservers.net/vpninfo/servers/v6')
    data = json.loads(r.text.splitlines()[0])
    
    # Get all regions that support WireGuard
    all_regions = []
    for region in data['regions']:
        # Check if the region has WireGuard servers
        if 'wg' in region['servers']:
            all_regions.append(region['name'])
    
    # Sort regions alphabetically
    all_regions.sort()
    
    return all_regions

# Get all available regions
regions = get_available_regions()
print("\nAvailable PIA regions:")
for i, region in enumerate(regions, 1):
    print(f"{i}) {region}")

# Let user select a region by number
while True:
    try:
        choice = int(input("\nEnter the number of the region you want to select: "))
        if 1 <= choice <= len(regions):
            selected_region = regions[choice-1]
            break
        else:
            print(f"Please enter a number between 1 and {len(regions)}")
    except ValueError:
        print("Please enter a valid number")

pia.set_region(selected_region)
print(f"\nSelected region: {selected_region}")

# Generate public and private key pair
pia.generate_keys()

# Get token
while True:
  username = "p1991057"
  password = "dhgm7MojYv"
  if pia.get_token(username, password):
    print("Login successful!")
    break
  else:
    print("Error logging in, please try again...")

# Add key
status, response = pia.addkey()
if status:
  print("Added key to server!")
else:
  print("Error adding key to server")
  print(response)

# Build config
timestamp = int(datetime.now().timestamp())
location = pia.region.replace(' ', '-')
config_file = 'PIA-{}-{}.conf'.format(location, timestamp)
print("Saving configuration file {}".format(config_file))
with open(config_file, 'w') as file:
  file.write('[Interface]\n')
  file.write('Address = {}\n'.format(pia.connection['peer_ip']))
  file.write('PrivateKey = {}\n'.format(pia.privatekey))
  file.write('DNS = {},{}\n\n'.format(pia.connection['dns_servers'][0], pia.connection['dns_servers'][1]))
  file.write('[Peer]\n')
  file.write('PublicKey = {}\n'.format(pia.connection['server_key']))
  file.write('Endpoint = {}:1337\n'.format(pia.connection['server_ip']))
  file.write('AllowedIPs = 0.0.0.0/0\n')
  file.write('PersistentKeepalive = 25\n')
EOF
}

check_pia() {
    cd /root
    # Check if required packages are installed (e.g., git)
    if ! command -v git &> /dev/null; then
        echo "Git is not installed. Installing..."
        DEBIAN_FRONTEND=noninteractive sudo apt-get install -y git >/dev/null 2>&1
    fi

    # Check if the pia-wg repository is already cloned
    if [ ! -d "pia-wg" ]; then
        echo "Cloning pia-wg repository..."
        git clone https://github.com/hsand/pia-wg.git

        # Install project dependencies and WireGuard (if not present)
        echo "Installing project dependencies and WireGuard..."
        DEBIAN_FRONTEND=noninteractive sudo apt-get install -y python3-venv wireguard openresolv >/dev/null 2>&1
    else
        echo "pia-wg repository already exists."
    fi

    cd pia-wg
    create_pia_generator_file

    # Check if the virtual environment directory already exists
    if [ ! -d "venv" ]; then 
        echo "Creating virtual environment..."
        python3 -m venv venv

        echo "Activating virtual environment..."
        source venv/bin/activate 
    else
        echo "Virtual environment already exists. Activating..."
        source venv/bin/activate
    fi

    pip install -r requirements.txt
}

generate_pia_wireguard_file() {
    check_pia

    venv_dir="pia-wg/venv"  # Path to the virtual environment within pia-wg

    if [ -d "pia-wg" ]; then
        echo "Entering pia-wg directory..."
        cd pia-wg
    fi
    
    # Delete any existing PIA config files
    echo "Cleaning up existing PIA config files..."
    find . -type f -name "PIA-*-*.conf" -delete
    
    python3 generate-config.py

    # Wait briefly for the file to be created
    sleep 1

    # Find the most recently created config file
    config_file=$(find . -maxdepth 1 -name "PIA-*.conf" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -f2- -d" ")
    
    if [ -z "$config_file" ]; then
        echo "Error: No configuration file was generated"
        exit 1
    else
        echo "Successfully created configuration file: ${config_file#./}"
        # No need to copy since we're already in the right directory
        echo "Configuration file is ready in /root/pia-wg/"
    fi

    deactivate
    
    # Return success
    return 0
}