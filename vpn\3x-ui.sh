#!/bin/bash

install_3x_ui() {

    install_docker
    echo -e "${GRE<PERSON>}Installing 3x-ui panel...${NC}"
    
    # Create directory if it doesn't exist
    if [ ! -d "/opt/3x-uidb" ]; then
        echo -e "${YELLOW}Creating directory /opt/3x-uidb${NC}"
        sudo mkdir -p /opt/3x-uidb
    fi

    # Check if container already exists
    if docker ps -a | grep -q "3x-ui"; then
        echo -e "${RED}3x-ui container already exists. Please remove it first.${NC}"
        return 1
    fi

    # Run the container
    echo -e "${GREEN}Starting 3x-ui container...${NC}"
    docker run -itd \
        -e XRAY_VMESS_AEAD_FORCED=false \
        -v /opt/3x-uidb/:/etc/x-ui/ \
        --network=host \
        --restart=unless-stopped \
        --name 3x-ui \
        ghcr.io/mhsanaei/3x-ui:latest

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}3x-ui panel has been successfully installed!${NC}"
        echo -e "${YELLOW}You can access the panel at: ${NC}"
        echo -e "${CYAN}http://$(curl -s ifconfig.me):2053${NC}"
        echo -e "${YELLOW}Default credentials:${NC}"
        echo -e "${CYAN}Username: admin${NC}"
        echo -e "${CYAN}Password: admin${NC}"
        echo -e "${YELLOW}Please change your credentials after first login!${NC}"
    else
        echo -e "${RED}Failed to install 3x-ui panel${NC}"
    fi
}

remove_3x_ui() {

    install_docker
    echo -e "${YELLOW}Removing 3x-ui panel...${NC}"
    
    # Check if container exists
    if ! docker ps -a | grep -q "3x-ui"; then
        echo -e "${RED}3x-ui container not found.${NC}"
        return 1
    fi

    # Stop and remove the container
    docker stop 3x-ui
    docker rm 3x-ui

    # Ask if user wants to remove the data directory
    read -rp "$(echo -e ${YELLOW}"Do you want to remove the data directory (/opt/3x-uidb)? [y/N]: "${NC})" remove_data
    if [[ $remove_data =~ ^[Yy]$ ]]; then
        sudo rm -rf /opt/3x-uidb
        echo -e "${GREEN}Data directory removed.${NC}"
    fi

    echo -e "${GREEN}3x-ui panel has been successfully removed!${NC}"
}
