#!/bin/bash

# Folder Operations Functions
# Contains functions for renaming, searching, copying, moving, and deleting folders.

# Function to bulk rename folders
bulk_rename_folders() {
    echo -e "${YELLOW}Enter the path to work in:${NC}"
    read -r work_path
    work_path="${work_path%/}"  # Remove trailing slash if any

    if [ ! -d "$work_path" ]; then
        echo -e "${RED}Invalid path: Directory does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo -e "${YELLOW}Choose rename operation:${NC}"
    echo "1. Add sequence number"
    echo "2. Remove pattern"
    read -r operation_choice

    case $operation_choice in
        1)  # Sequence number functionality
            echo -e "${YELLOW}Enter folder pattern (e.g., test*):${NC}"
            read -r pattern
            echo -e "${YELLOW}Enter replacement pattern (use # for number sequence):${NC}"
            read -r replacement

            count=1
            find "$work_path" -maxdepth 1 -type d -name "$pattern" | while read -r folder; do
                # Skip the work_path itself if it matches the pattern
                if [ "$folder" = "$work_path" ]; then
                    continue
                fi
                new_name="$(dirname "$folder")/$(echo "$replacement" | sed "s/#/$count/g")"
                if [ -e "$new_name" ]; then
                    echo -e "${RED}Error: $new_name already exists${NC}"
                else
                    mv "$folder" "$new_name"
                    echo -e "${GREEN}Renamed: $folder -> $new_name${NC}"
                    ((count++))
                fi
            done
            ;;
        2)  # Pattern removal functionality
            echo -e "${YELLOW}Enter folder pattern (e.g., test*):${NC}"
            read -r pattern
            echo -e "${YELLOW}Enter the pattern to remove from folder names:${NC}"
            read -r remove_pattern

            find "$work_path" -maxdepth 1 -type d -name "$pattern" | while read -r folder; do
                 # Skip the work_path itself if it matches the pattern
                if [ "$folder" = "$work_path" ]; then
                    continue
                fi
                dir_path=$(dirname "$folder")
                base_name=$(basename "$folder")
                new_name="$dir_path/$(echo "$base_name" | sed "s/$remove_pattern//g")"

                if [ "$folder" = "$new_name" ]; then
                    echo -e "${YELLOW}No change needed for: $folder${NC}"
                elif [ -e "$new_name" ]; then
                    echo -e "${RED}Error: $new_name already exists${NC}"
                else
                    mv "$folder" "$new_name"
                    echo -e "${GREEN}Renamed: $folder -> $new_name${NC}"
                fi
            done
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    read -p "Press Enter to continue..."
}

# Function to search folders
search_folders() {
    echo -e "${YELLOW}Enter the path to search in:${NC}"
    read -r search_path
    search_path_handled=$(handle_path "$search_path") # Use handle_path for find command

    # Validate the original path before handling
    if ! validate_path "$search_path"; then
         read -p "Press Enter to continue..."
         return
    fi

    if [ ! -d "$search_path" ]; then
        echo -e "${RED}Invalid path: Directory does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo -e "${YELLOW}Choose search criteria:${NC}"
    echo "1. By name pattern"
    echo "2. By complex pattern"
    read -r search_choice

    case $search_choice in
        1)
            echo -e "${YELLOW}Enter folder name pattern (e.g., test*):${NC}"
            read -r pattern
            search_pattern="$pattern"
            ;;
        2)
            echo -e "${YELLOW}Enter complex pattern (e.g., *2023* or *_backup*):${NC}"
            read -r pattern
            search_pattern="$pattern"
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            return
            ;;
    esac

    echo -e "${GREEN}Found folders:${NC}"
    # Use the handled path for the find command
    found_folders=$(eval find ${search_path_handled} -maxdepth 1 -type d -name \"$search_pattern\")

    # Filter out the base search path itself from the results
    filtered_folders=$(echo "$found_folders" | grep -v "^${search_path}$")


    if [ -z "$filtered_folders" ]; then
        echo -e "${YELLOW}No folders found matching the pattern (excluding the search path itself)${NC}"
    else
        echo "$filtered_folders"
        echo -e "\n${YELLOW}Total folders found: $(echo "$filtered_folders" | wc -l)${NC}"
    fi
    read -p "Press Enter to continue..."
}

# Function to copy folders
copy_folders() {
    echo -e "${YELLOW}Enter the source path:${NC}"
    read -r source_path
    source_path="${source_path%/}"

    if [ ! -d "$source_path" ]; then
        echo -e "${RED}Invalid source path: Directory does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo -e "${YELLOW}Enter folder pattern to copy (e.g., test*):${NC}"
    read -r pattern
    echo -e "${YELLOW}Enter destination directory:${NC}"
    read -r dest_dir
    dest_dir="${dest_dir%/}"

    if [ ! -d "$dest_dir" ]; then
        echo -e "${YELLOW}Directory doesn't exist. Create it? (y/n)${NC}"
        read -r create_dir
        if [ "$create_dir" = "y" ]; then
            mkdir -p "$dest_dir"
        else
            return
        fi
    fi

    # Get absolute paths for comparison
    abs_source=$(readlink -f "$source_path")
    abs_dest=$(readlink -f "$dest_dir")

    # Show folders that will be copied
    echo -e "${GREEN}Folders to be copied:${NC}"
    # Exclude destination directory if it's inside source, only search top level
    if [[ "$abs_dest" == "$abs_source"* ]]; then
        found_folders=$(find "$source_path" -maxdepth 1 -type d -name "$pattern" -not -path "$dest_dir/*" -not -path "$dest_dir" -not -path "$source_path")
    else
        found_folders=$(find "$source_path" -maxdepth 1 -type d -name "$pattern" -not -path "$source_path")
    fi

    if [ -z "$found_folders" ]; then
        echo -e "${YELLOW}No folders found matching the pattern${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo "$found_folders"
    echo -e "\n${YELLOW}Proceed with copy? (y/n)${NC}"
    read -r confirm

    if [ "$confirm" = "y" ]; then
        while IFS= read -r folder; do
            cp -r "$folder" "$dest_dir/"
            echo -e "${GREEN}Copied: $folder -> $dest_dir/${NC}"
        done <<< "$found_folders"
        echo -e "${GREEN}Folders copied successfully${NC}"
    else
        echo -e "${BLUE}Operation cancelled${NC}"
    fi
    read -p "Press Enter to continue..."
}

# Function to move folders
move_folders() {
    echo -e "${YELLOW}Enter the source path:${NC}"
    read -r source_path
    source_path="${source_path%/}"

    if [ ! -d "$source_path" ]; then
        echo -e "${RED}Invalid source path: Directory does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo -e "${YELLOW}Enter folder pattern to move (e.g., test*):${NC}"
    read -r pattern
    echo -e "${YELLOW}Enter destination directory:${NC}"
    read -r dest_dir
    dest_dir="${dest_dir%/}"

    if [ ! -d "$dest_dir" ]; then
        echo -e "${YELLOW}Directory doesn't exist. Create it? (y/n)${NC}"
        read -r create_dir
        if [ "$create_dir" = "y" ]; then
            mkdir -p "$dest_dir"
        else
            return
        fi
    fi

    # Get absolute paths for comparison
    abs_source=$(readlink -f "$source_path")
    abs_dest=$(readlink -f "$dest_dir")

    # Show folders that will be moved
    echo -e "${GREEN}Folders to be moved:${NC}"
    # Exclude destination directory if it's inside source, only search top level
    if [[ "$abs_dest" == "$abs_source"* ]]; then
        found_folders=$(find "$source_path" -maxdepth 1 -type d -name "$pattern" -not -path "$dest_dir/*" -not -path "$dest_dir" -not -path "$source_path")
    else
        found_folders=$(find "$source_path" -maxdepth 1 -type d -name "$pattern" -not -path "$source_path")
    fi

    if [ -z "$found_folders" ]; then
        echo -e "${YELLOW}No folders found matching the pattern${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo "$found_folders"
    echo -e "\n${YELLOW}Proceed with move? (y/n)${NC}"
    read -r confirm

    if [ "$confirm" = "y" ]; then
        while IFS= read -r folder; do
            mv "$folder" "$dest_dir/"
            echo -e "${GREEN}Moved: $folder -> $dest_dir/${NC}"
        done <<< "$found_folders"
        echo -e "${GREEN}Folders moved successfully${NC}"
    else
        echo -e "${BLUE}Operation cancelled${NC}"
    fi
    read -p "Press Enter to continue..."
}

# Function to delete folders
delete_folders() {
    echo -e "${YELLOW}Enter the path to search in:${NC}"
    read -r search_path
    search_path="${search_path%/}"

    if [ ! -d "$search_path" ]; then
        echo -e "${RED}Invalid path: Directory does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    # Show only first level folders in the path
    echo -e "${GREEN}Available folders in $search_path:${NC}"
    find "$search_path" -mindepth 1 -maxdepth 1 -type d | sed "s|$search_path/||"

    echo -e "\n${YELLOW}Enter folder pattern to delete (e.g., test*):${NC}"
    read -r pattern

    # Show folders that will be deleted (only top level)
    echo -e "${GREEN}Folders to be deleted:${NC}"
    found_folders=$(find "$search_path" -maxdepth 1 -type d -name "$pattern" -not -path "$search_path")

    if [ -z "$found_folders" ]; then
        echo -e "${YELLOW}No folders found matching the pattern${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo "$found_folders"
    echo -e "\n${YELLOW}Total folders found: $(echo "$found_folders" | wc -l)${NC}"

    # Double confirmation for deletion
    echo -e "${RED}Warning: This will permanently delete these folders and ALL their contents!${NC}"
    echo -e "${YELLOW}Type 'yes' to confirm deletion:${NC}"
    read -r confirm

    if [ "$confirm" = "yes" ]; then
        echo -e "${RED}Are you absolutely sure? Type 'confirm' to proceed:${NC}"
        read -r final_confirm

        if [ "$final_confirm" = "confirm" ]; then
            echo "$found_folders" | while read -r folder; do
                rm -rf "$folder"
                echo -e "${GREEN}Deleted: $folder${NC}"
            done
            echo -e "${GREEN}Folders deleted successfully${NC}"
        else
            echo -e "${BLUE}Operation cancelled${NC}"
        fi
    else
        echo -e "${BLUE}Operation cancelled${NC}"
    fi
    read -p "Press Enter to continue..."
}

# Function to delete empty folders
delete_empty_folders() {
    echo -e "${YELLOW}Enter the path to search for empty folders:${NC}"
    read -r search_path
    search_path="${search_path%/}"

    if [ ! -d "$search_path" ]; then
        echo -e "${RED}Invalid path: Directory does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    # Find empty directories
    echo -e "${GREEN}Found empty folders:${NC}"
    # Using find with -empty to find empty directories and -not -path to exclude the root directory
    empty_folders=$(find "$search_path" -type d -empty -not -path "$search_path")

    if [ -z "$empty_folders" ]; then
        echo -e "${YELLOW}No empty folders found${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    # Display found empty folders
    echo "$empty_folders"
    echo -e "\n${YELLOW}Total empty folders found: $(echo "$empty_folders" | wc -l)${NC}"

    # Confirm deletion
    echo -e "${RED}Warning: Are you sure you want to delete these empty folders?${NC}"
    echo -e "${YELLOW}Type 'yes' to confirm deletion:${NC}"
    read -r confirm

    if [ "$confirm" = "yes" ]; then
        # Sort folders by depth (longest path first) to delete nested empty folders correctly
        echo "$empty_folders" | awk '{ print length, $0 }' | sort -nr | cut -d" " -f2- | while IFS= read -r folder; do
            # Double-check if folder is still empty and exists
            if [ -d "$folder" ] && [ -z "$(ls -A "$folder")" ]; then
                rmdir "$folder"
                echo -e "${GREEN}Deleted: $folder${NC}"
            elif [ -d "$folder" ]; then
                 echo -e "${YELLOW}Skipped (not empty): $folder${NC}"
            # else # Folder might have been deleted as part of a parent deletion
            #    echo -e "${YELLOW}Skipped (already deleted): $folder${NC}"
            fi
        done
        echo -e "${GREEN}Empty folders deletion completed${NC}"
    else
        echo -e "${BLUE}Operation cancelled${NC}"
    fi
    read -p "Press Enter to continue..."
}