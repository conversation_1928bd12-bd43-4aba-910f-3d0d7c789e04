# CasaOS Module

This module provides comprehensive functionality for managing CasaOS, a home cloud system that allows easy deployment and management of Docker applications.

## Files

### casaos_core_functions.sh
Core functions for basic CasaOS installation, checks, container management, and app backup.

**Key Functions:**
- **Installation and Checks:**
  - `check_casaos_installed()`: Checks if CasaOS is installed.
  - `install_casaos()`: Installs CasaOS and optionally triggers config restore.
  - `get_casaos_apps()`: Gets list of app directories in `/DATA/AppData/`.
- **Container Management:**
  - `container_exists()`: Checks if a Docker container exists.
  - `ensure_container_ready()`: Waits for a container to be inspectable.
  - `stop_container()`: Stops a specified container safely.
  - `manage_container()`: Starts or stops a container.
- **App Backup:**
  - `backup_single_app()`: Backs up a single CasaOS app's data (stops, compresses, uploads, starts).
  - `casaos_backup_menu()`: Menu for selecting apps to back up.

### casaos_backup_restore_functions.sh
Functions focused on app data backup/restore operations, permissions, and status checks.

**Key Functions:**
- **Permissions and Copying:**
  - `fix_app_permissions()`: Sets appropriate file permissions for app data (special handling for Jellyfin).
  - `copy_with_progress()`: Copies files/directories with a progress bar (using `pv` or `rsync`).
- **Restore Operations:**
  - `casaos_restore_menu()`: Menu for selecting app backups to restore. Handles app installation if missing.
- **Status Checks:**
  - `is_app_installed()`: Checks if a specific CasaOS app is listed via CLI.
  - `wait_for_container_status()`: Waits for a container to reach 'running' or 'removed' state.
  - `wait_for_app_uninstalled()`: Waits for an app to disappear from the CasaOS CLI list after uninstallation.
  - `wait_for_app_in_list()`: Waits for an app to appear in the CasaOS CLI list after installation.

### casaos_app_management_functions.sh
Functions for installing, uninstalling, and listing CasaOS applications via the CLI, including menu interfaces.

**Key Functions:**
- **App Installation/Uninstallation:**
  - `uninstall_casaos_app()`: Uninstalls a specified CasaOS app using the CLI.
  - `install_casaos_app()`: Installs a CasaOS app from a temporary YAML file.
  - `ensure_yaml_files()`: Downloads required app YAML files (e.g., Jellyfin, Nginx Proxy Manager) from rclone remote if missing locally.
  - `list_installed_apps()`: Lists currently installed apps via the CLI.
- **Menus:**
  - `casaos_install_apps_menu()`: Menu for selecting available apps to install.
  - `casaos_uninstall_menu()`: Menu for selecting installed apps to uninstall.
- **CasaOS Uninstallation:**
  - `uninstall_casaos()`: Uninstalls CasaOS itself after removing all apps.

### casaos_config_service_functions.sh
Functions dedicated to managing the CasaOS system configuration backup/restore and controlling related systemd services.

**Key Functions:**
- **Configuration Backup/Restore:**
  - `backup_casaos_config()`: Backs up the `/etc/casaos` directory.
  - `restore_casaos_config()`: Restores the `/etc/casaos` directory from a backup, managing service stops/starts.
- **Service Management:**
  - `start_casaos_service()`: Starts a specified CasaOS-related systemd service with status checks and waits.
  - `check_casaos_service()`: Checks if the main CasaOS service is operational by querying the app list.

### casaos_menu.sh
Provides the main menu interface for all CasaOS operations. Sources the four function files (`casaos_core_functions.sh`, `casaos_backup_restore_functions.sh`, `casaos_app_management_functions.sh`, `casaos_config_service_functions.sh`).

**Key Functions:**
- `casaos_menu()`: Main menu displaying options for CasaOS management.

## Usage

The CasaOS module provides comprehensive functionality for managing a home cloud system with various applications.

To access CasaOS functionality:
1. Select "CasaOS Menu" from the main menu.
2. Choose from installation, app management (install/uninstall), app data backup/restore, or system configuration backup/restore options.
3. Follow the interactive prompts to manage your CasaOS environment.

## Integration with Other Modules

The CasaOS module integrates with:
- Docker module (implicitly via `docker` commands).
- File Operations module (potentially for `pv`, `rsync` if not system default).
- Utilities module (assumed for `create_menu`, `manage_package`).
- Rclone module (assumed for `install_rclone`, `rclone` commands).