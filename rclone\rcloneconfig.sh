#!/bin/bash

create_rclone_config() {
    local directory="/root/.config/rclone"
    local config_file="$directory/rclone.conf"

    # Delete existing config if it exists
    if [ -f "$config_file" ]; then
        #echo -e "${YELLOW}Removing existing rclone config...${NC}"
        rm -f "$config_file"
    fi

    #echo -e "${YELLOW}Creating new rclone config...${NC}"
    
    # Create the config content
    cat > "$config_file" << 'EOL'
[Gdrive1]
type = drive
scope = drive
token = {"access_token":"*********************************************************************************************************************************************************************************************************************************","token_type":"Bearer","refresh_token":"1//09iFAOD2c2JVbCgYIARAAGAkSNwF-L9Ir-v1AHMLxyb8LXOEtQGXmBwS2NdxSs7Mv41DALrMx-t7LiX7kvdnIPkoOPDu6PhJjKLg","expiry":"2024-09-26T21:11:31.8908568+03:00"}
team_drive = 

[Gdrive2]
type = drive
scope = drive
token = {"access_token":"*********************************************************************************************************************************************************************************************************************************","token_type":"Bearer","refresh_token":"1//09KYASR2HtTvyCgYIARAAGAkSNwF-L9IrgnSly3hY-JMnDAMQtCsyfqqp5S9XS0W7TrAEHutFEVRO0kLZBEjmk7QxWX9HRWLh1lg","expiry":"2024-09-26T21:58:20.0006756+03:00"}
team_drive = 

[Gunion]
type = union
upstreams = Gdrive1: Gdrive2:
action_policy = epmfs
create_policy = mfs
search_policy = ff

[Egunion]
type = crypt
remote = Gunion:
password = 4wZxMVe_cQYOQ9no1fm6_k1X7NxdjBa4ZOBjULUo

[Gdrive3]
type = drive
scope = drive
token = {"access_token":"*********************************************************************************************************************************************************************************************************************************","token_type":"Bearer","refresh_token":"1//09AcDkED7IQWbCgYIARAAGAkSNwF-L9Ir2UuQQsyXZQ_l82dbP76-gYAEqcbYVOQhL8X5nlvnBBuu71m5XTjjihLwdOFPptFtfLU","expiry":"2025-03-05T21:45:24.1201362+03:00"}
team_drive = 

[Idrive]
type = s3
provider = IDrive
access_key_id = FxV0MZqlGOQwmlW8NKmx
secret_access_key = YSqkMvFYJZjDZquJEVcJwrObWqtN0JvDutbMHaf5
endpoint = g5g7.par.idrivee2-44.com

[Gdrive4]
type = drive
scope = drive
token = {"access_token":"*********************************************************************************************************************************************************************************************************************************","token_type":"Bearer","refresh_token":"1//03dxqly9v68m5CgYIARAAGAMSNwF-L9IrXkNjFrlkJpb8KYnoSdA9V0ODu_41JSWsanYNnpcc70zEbtUR5Rcao50NEejWVc9kNnY","expiry":"2025-03-05T21:45:24.1449498+03:00"}
team_drive = 

[GdriveP1]
type = drive
token = {"access_token":"*********************************************************************************************************************************************************************************************************************************","token_type":"Bearer","refresh_token":"1//03NLJkzyktssoCgYIARAAGAMSNwF-L9Iro4uxcc64k1hzA4PZKr_vFhkJDWBv0MhgyYDtO8yr9W1Uvl4fflRCe8RBa22Txzg6EXw","expiry":"2025-04-06T11:31:08.0374827+03:00"}
team_drive = 

[EgdriveP1]
type = crypt
remote = GdriveP1:Crypt
password = BdxYXarUWmvYJKdSYGG4janeIrcnExOpAk63S8B6

[Gunion34P1]
type = union
upstreams = Gdrive3: Gdrive4: GdriveP1:
action_policy = epmfs
create_policy = mfs
search_policy = ff

[OneDrive]
type = onedrive
token = {"access_token":"eyJ0eXAiOiJKV1QiLCJub25jZSI6InRDemNkTnBiZEpScmpvbEZOUmZWU3h0UUdkalhJZVA5cVFSWmlRVWhOMmsiLCJhbGciOiJSUzI1NiIsIng1dCI6IkpETmFfNGk0cjdGZ2lnTDNzSElsSTN4Vi1JVSIsImtpZCI6IkpETmFfNGk0cjdGZ2lnTDNzSElsSTN4Vi1JVSJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.g5LXf629N5wTsAIOVUZFi7F6mq5k-0GdKYlRoFpDYX-uPx7lh2vsePSP6hOzx3RZUdf36ZcBSZtWj0gkAA2Th7CmQY5x8XM7RxKBnXdiGU5cFzvNLvh2cvjzQWP2Px6lAlz1Lfh9-vmtnFPSkY9Y5Ttf7EvMtyqZeLmw5jg3tFHSzlOIyZ25TF8_O6Kmc2loWCzTXgOO76rmJlQMzTSAGlR4AElK7hkOi9wnKTgx3W84TVZUB5hzAT4Cyz2mJ_MCSa5t3Z2n91vvOaOKCVFhJGZYSzmEzcSKjFpl6bZUgUU6nl0oa6CWswDDzp4o3awQCoaWAW7XxehATpJ-I3TkoA","token_type":"Bearer","refresh_token":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","expiry":"2025-03-23T17:39:40.3295586+03:00"}
drive_id = b!D3OBAibD0U-7NtxS4Ui5inizBL-kLvZMuMXAP6H9CzMMQdf5Q50uRJZGRpQxvR65
drive_type = business

[EoneDrive]
type = crypt
remote = OneDrive:
password = e71yUXvsEdY4L2FpFuZDfi0dzKjErvenDA
password2 = FWKHoN_3HPK4E96J2LtPE3SRZcj0pgyz__MP

[Yan_Fazee]
type = yandex
token = {"access_token":"y0_AgAAAAA_ndkWAALSwgAAAAEHBpcNAADNHFo6nGVMMaXgFRnUzdJd2G_khw","token_type":"OAuth","refresh_token":"1:3Q05xHjMCtvGaPDj:7-fu0HWFucCKTlwnUsF-lta3IL0Ffp02kt7xMH38HlHwnC5arpijkj1WF-JoHSOCKznq4uuEZrvcaUbAHA:hAzUuuxUHJRu186Bo-AhAA","expiry":"2026-03-26T23:02:29.0597193+03:00"}

[Yan1]
type = yandex
client_id = e003bc344a954af8af13e31ceeaf2d20
client_secret = 248b50eaa8a3465f9ad5c217f0f1e5bb
token = {"access_token":"y0__xDE1ouGCBj0tDYg-srW0hJYNPaRyPtKytqQ3X8F41UDTi_WbQ","token_type":"OAuth","refresh_token":"1:3H5kveTjIg1OGMLW:DqQOBWC2dXrwECT_wV_46cFr-komMMiUuAj1hykIyUTNY0DrQcoMMR-nLa9pGmK5A8rBKtc:wkrClxAtqrX4mVnwLLVN5g","expiry":"2026-03-26T23:20:43.9095626+03:00"}

[Yan2]
type = yandex
token = {"access_token":"y0__xCBo4yGCBjCpQsg96jY0hLuJ8cOYFNQe9NxNQWSWVar4h9kLw","token_type":"OAuth","refresh_token":"1:ela_4Gqd5unJWnR5:P0Gonl8Pp-YrIA9RKFkvd3Hc6v4m4GQNxdUeeeIOmr8cZc6N1V73q1QVp2URpQuzaBeBunI:2HquCZwtRcEvGlvO45UBNA","expiry":"2026-03-26T23:43:35.3149253+03:00"}

[Yan3]
type = yandex
token = {"access_token":"y0__xCs0K6JCBjCpQsg2eWK4RJmk6S-r39rXY4mMuN50fpOFuOW4w","token_type":"OAuth","refresh_token":"1:AAA:1:u1BjxzGaxrB4npbr:ENd96v3L6-6Mzll8PS8rRDcI-rJQ2G7uz2nH9jkIA-LOnsTrx5yiwu32jZIWKI25vgCQA7A:ecllAYtnxPpeKUdoPpulVw","expiry":"2026-04-08T02:35:16.3717087+03:00"}

[Yan4]
type = yandex
token = {"access_token":"y0__xDw4q6JCBjCpQsgsf6L4RJ3hOrHgeDYjjkRAs18Zo0ADz937w","token_type":"OAuth","refresh_token":"1:AAA:1:BqASjug97JQ-a-i2:Tc5TyIp1Qr6xjJletW1uIflcZI0lKGBgWo4-Hj4miAS-KIW5ERPIpzRDxk_ENcaMx2YvgBA:y1APlSygNmjEZIV9yDRlvA","expiry":"2026-04-08T02:50:11.1030022+03:00"}

[Yan5]
type = yandex
token = {"access_token":"y0__xDy77CJCBjCpQsg6IeW4RJE8vIfXUb2iKeBPQb2hYT99QFoxA","token_type":"OAuth","refresh_token":"1:AAA:1:sD-tMVR5MOQ_sWCo:WISF8jPZ1jSeIrdEspOPI4Fdmh9nXqetsVWj4bhl8A2AS9N4FwjcxOb5GPMY_7B80pTm1WA:unW76h_X9W4RLHtiQWVi0Q","expiry":"2026-04-08T06:50:44.3541763+03:00"}

[Yan6]
type = yandex
token = {"access_token":"y0__xCUh7GJCBjCpQsgjumW4RIebK3YIkD_KRwTWz-R4BWiW6eqgg","token_type":"OAuth","refresh_token":"1:AAA:1:EOYUuuFHK-ro-AXb:VBPpN173h7MZSOtcyvIs5QoztSltSyQzLR0c9rCEQfjV-X3GLQCe440NogCYhrsawlnLmY0:6loVcX3yBmEW7GLEtvyJ0g","expiry":"2026-04-08T07:03:15.1990308+03:00"}

[Yan7]
type = yandex
token = {"access_token":"y0_AgAAAAA_ndkWAALSwgAAAAEHBpcNAADNHFo6nGVMMaXgFRnUzdJd2G_khw","token_type":"OAuth","refresh_token":"1:AAA:1:xU_bbrTXJkzLEJA-:1bnVYa9S-N4nQQgkZJxzOM_VRlDEEDcFhHovrKNOAtnyukr7YKAdbyg0KJosSDQh_j3Qe7Ah2ottRa_A3g:7ZtSZozmEC8WgUBkf1ZFoQ","expiry":"2026-03-26T23:02:28.224222+03:00"}

[Yunion]
type = union
upstreams = Yan1: Yan2: Yan3: Yan4: Yan5: Yan6: Yan7: Yan_Fazee:
create_policy = epmfs
action_policy = epall
search_policy = ff

[Eyunion]
type = crypt
remote = Yunion:
password = w9f4szAMkFb_2dBKIEs73hzJHZ0b7dtqHQNLV_EnvW4tjwGa6xg

EOL

    chmod 600 "$config_file"
    # echo -e "${GREEN}Rclone config file created successfully.${NC}"
} 