install_shadowsocks(){
    echo -e "${RED}╔═══════════════════════════════╗${NC}"
    echo -e "${RED}║   Installing Shadowsocks      ║${NC}"
    echo -e "${RED}╚═══════════════════════════════╝${NC}"
    echo ""

    install_docker

    echo -e "${YELLOW}Pulling image from Docker Hub...${NC}"
    docker pull fazee6/shadowsocks-libev:latest

    echo "Checking if there is already a container with the name ss-libev...."
    stop_and_remove_container "ss-libev"

    gdown "1bVLarsw-_Qg4LGpcOvisXQTgxEQBz6w3" --output shadowsocks_password.txt

    ss_password=$(<"shadowsocks_password.txt")

    sudo rm shadowsocks_password.txt

    echo ""

    while true; do
        read -rp "Enter port for shadowsocks server (Example: 10400): " shadowsocks_port
        get_valid_port "$shadowsocks_port"
        if [ $? -eq 0 ]; then
            break
        fi
    done

    while true; do
        read -rp "Do you want to enable V2Ray plugin? (y/n): " enable_v2ray
        case $enable_v2ray in
            [Yy]*)
                while true; do
                    read -rp "Enter WebSocket path (Example: /ws): " ws_path
                    if [[ $ws_path =~ ^/[a-zA-Z0-9_-]+$ ]]; then
                        break
                    else
                        echo "Invalid path. Must start with / and contain only letters, numbers, underscores, or hyphens."
                    fi
                done
                
                docker run -d -p $shadowsocks_port:8388 \
                    -e PASSWORD=$ss_password \
                    -e METHOD=chacha20-ietf-poly1305 \
                    -e PLUGIN="v2ray-plugin" \
                    -e PLUGIN_OPTS="server;path=$ws_path" \
                    --name ss-libev \
                    $DOCKER_NETWORK \
                    fazee6/shadowsocks-libev
                break
                ;;
            [Nn]*)
                docker run -d -p $shadowsocks_port:8388 \
                    -e PASSWORD=$ss_password \
                    -e METHOD=chacha20-ietf-poly1305 \
                    --name ss-libev \
                    $DOCKER_NETWORK \
                    fazee6/shadowsocks-libev
                break
                ;;
            *)
                echo "Please answer y or n."
                ;;
        esac
    done

    docker update --restart unless-stopped ss-libev

    server_ip=$(get_public_ip)

    echo ""
    local max_length=$(( ${#server_ip} > ${#shadowsocks_port} ? ${#server_ip} : ${#shadowsocks_port} ))
    max_length=$(( max_length > ${#ss_password} ? max_length : ${#ss_password} ))
    local box_width=$((max_length + 15))

    echo -e "${CYAN}╔$(printf '═%.0s' $(seq 1 $box_width))╗${NC}"
    echo -e "${CYAN}║     Shadowsocks Server Details     ║${NC}"
    echo -e "${CYAN}╠$(printf '═%.0s' $(seq 1 $box_width))╣${NC}"
    printf "${CYAN}║${NC} Server IP:  %-${max_length}s ${CYAN}║${NC}\n" "$server_ip"
    printf "${CYAN}║${NC} Port:       %-${max_length}s ${CYAN}║${NC}\n" "$shadowsocks_port"
    printf "${CYAN}║${NC} Password:   %-${max_length}s ${CYAN}║${NC}\n" "$ss_password"
    if [[ $enable_v2ray =~ ^[Yy]$ ]]; then
        printf "${CYAN}║${NC} Plugin:      %-${max_length}s ${CYAN}║${NC}\n" "v2ray-plugin"
        printf "${CYAN}║${NC} WS Path:     %-${max_length}s ${CYAN}║${NC}\n" "$ws_path"
    fi
    echo -e "${CYAN}╚$(printf '═%.0s' $(seq 1 $box_width))╝${NC}"
    echo ""
}