# File Server Module

This module provides functionality for setting up and managing file servers, including both HTTP/WebDAV file servers and FTP servers using Docker containers.

## Files

### file_server_functions.sh
Core functions for file server management.

**Key Functions:**
- `get_next_server_number()`: Gets the next available number for file server container naming
- `get_architecture_image()`: Gets the correct Docker image for the current system architecture
- `create_file_server()`: Creates and configures an HTTP/WebDAV file server
- `check_port_in_use()`: Checks if a specified port is already in use
- `create_ftp_server()`: Creates and configures an FTP server
- `remove_file_server()`: Removes an HTTP/WebDAV file server
- `remove_ftp_server()`: Removes an FTP server

### file_server_menu.sh
Provides menu interface for file server operations.

**Key Functions:**
- `file_server_menu()`: Main menu for file server operations

## Usage

The File Server module provides functionality for setting up various types of file servers for sharing files over a network.

To access File Server functionality:
1. Select "File Server Menu" from the main menu
2. Choose from HTTP/WebDAV or FTP server setup options
3. Follow the interactive prompts to configure your file server

## Integration with Other Modules

The File Server module integrates with:
- Docker module for container management
- File Operations module for managing shared directories
- Certificate module for secure HTTPS connections (for WebDAV) 