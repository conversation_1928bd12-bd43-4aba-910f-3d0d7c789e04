# Function to install rclone
install_rclone() {
    if ! command -v rclone &> /dev/null; then
        echo "Installing rclone..."
        curl -fsSL https://rclone.org/install.sh | sudo bash
        echo "Rclone installed successfully."
        create_rclone_config
    else
        echo "Rclone is already installed."
        create_rclone_config
    fi
}

# Function to sync files between source and destination
rclone_sync() {
    local source=$1
    local dest=$2
    
    if [ -z "$source" ] || [ -z "$dest" ]; then
        echo "Error: Source and destination paths are required."
        return 1
    fi
    
    echo "Syncing files from $source to $dest..."
    echo "Executing rclone command: rclone sync \"$source\" \"$dest\" --progress"
    rclone sync "$source" "$dest" --progress
    echo "Sync completed."
}

# Function to cleanup after mount operations
mount_cleanup() {
    echo "Cleaning up mount points..."
    umount -f /mnt/rclone/* 2>/dev/null || true
    rm -rf /mnt/rclone/*
    echo "Cleanup completed."
}

