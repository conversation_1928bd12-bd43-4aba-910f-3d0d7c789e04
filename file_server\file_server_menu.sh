#!/bin/bash

file_server_menu() {
    while true; do
        options=(
            "1. Create/Run File Server"
            "2. Create/Run FTP Server"
            "3. Remove File Server"
            "4. Remove FTP Server"
            "5. Back to Main Menu"
        )

        create_menu "File Server Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) create_file_server ;;
            2) create_ftp_server ;;
            3) remove_file_server ;;
            4) remove_ftp_server ;;
            5) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo
    done
}
