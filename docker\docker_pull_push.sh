#!/bin/bash

# Colors for better output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to get and validate image digest with caching and optimization
get_valid_digest() {
    local image=$1
    local tag=$2
    local max_attempts=3
    local attempt=1
    local digest=""
    local cache_file="/tmp/docker_digest_method_${image//\//_}"
    local backoff_time=2

    # Try cached successful method first
    if [ -f "$cache_file" ]; then
        local method=$(cat "$cache_file")
        digest=$(try_digest_method "$method" "$image" "$tag")
        if [ -n "$digest" ] && [ ${#digest} -eq 12 ]; then
            echo "$digest"
            return 0
        fi
    fi

    while [ $attempt -le $max_attempts ]; do
        # Try each method in order of typical efficiency
        for method in "manifest" "inspect" "images"; do
            digest=$(try_digest_method "$method" "$image" "$tag")
            if [ -n "$digest" ] && [ ${#digest} -eq 12 ]; then
                echo "$method" > "$cache_file"
                echo "$digest"
                return 0
            fi
        done
        
        attempt=$((attempt + 1))
        backoff_time=$((backoff_time * 2))
        sleep $backoff_time
    done
    
    return 1
}

# Helper function to try different digest methods
try_digest_method() {
    local method=$1
    local image=$2
    local tag=$3
    local digest=""

    case "$method" in
        "manifest")
            export DOCKER_CLI_EXPERIMENTAL=enabled
            digest=$(docker manifest inspect "${image}:${tag}" 2>/dev/null | \
                    jq -r '.config.digest' 2>/dev/null | cut -d':' -f2 | cut -c1-12)
            ;;
        "inspect")
            digest=$(docker inspect "${image}:${tag}" 2>/dev/null | grep -m1 '"Id":' | sed 's/.*sha256://;s/".*$//' | cut -c1-12)
            ;;
        "images")
            digest=$(docker images --no-trunc --quiet "${image}:${tag}" 2>/dev/null | cut -c1-12)
            ;;
    esac

    echo "$digest"
}

# Function to pull image with exponential backoff and better error handling
pull_image_with_retry() {
    local image=$1
    local platform=$2
    local max_retries=3
    local retry=0
    local backoff_time=2
    local temp_log="/tmp/docker_pull_${image//\//_}.log"
    
    while [ $retry -lt $max_retries ]; do
        echo -e "${YELLOW}Pulling ${image}:latest for platform ${platform}${NC}"
        
        # Check for rate limiting before pulling
        if ! check_rate_limits "$image"; then
            echo -e "${RED}Rate limit exceeded. Waiting for reset...${NC}"
            sleep 60
            continue
        fi
        
        if docker pull --platform $platform "$image:latest" 2>"$temp_log"; then
            echo -e "${GREEN}Successfully pulled image${NC}"
            if docker inspect "${image}:latest" >/dev/null 2>&1; then
                rm -f "$temp_log"
                return 0
            else
                echo -e "${RED}Image pulled but verification failed${NC}"
            fi
        else
            # Parse error message for specific handling
            if grep -q "toomanyrequests" "$temp_log"; then
                echo -e "${YELLOW}Rate limit hit, waiting for reset...${NC}"
                sleep 60
                continue
            fi
        fi
        
        retry=$((retry + 1))
        if [ $retry -lt $max_retries ]; then
            echo -e "${YELLOW}Pull failed, retrying ($retry of $max_retries)...${NC}"
            backoff_time=$((backoff_time * 2))
            sleep $backoff_time
        fi
    done
    
    echo -e "${RED}Failed to pull image after ${max_retries} attempts${NC}"
    echo -e "${RED}Debug information:${NC}"
    cat "$temp_log"
    docker images | grep "$image" || true
    rm -f "$temp_log"
    return 1
}

# Function to check Docker Hub rate limits
check_rate_limits() {
    local image=$1
    local registry=$(echo "$image" | cut -d'/' -f1)
    
    # Only check rate limits for Docker Hub
    if [[ "$registry" == "docker.io" || "$registry" == "index.docker.io" ]]; then
        local remaining=$(curl -s --head "https://registry-1.docker.io/v2/ratelimitpreview/test/manifests/latest" | grep -i 'ratelimit-remaining' | cut -d: -f2 | tr -d ' \r')
        
        if [ -n "$remaining" ] && [ "$remaining" -eq 0 ]; then
            return 1
        fi
    fi
    
    return 0
}

# Function to get image digest (12 characters)
get_image_digest() {
    local image=$1
    local tag=$2
    # Get full digest and take first 12 characters
    docker inspect "${image}:${tag}" 2>/dev/null | grep -m1 '"Id":' | cut -d'"' -f4 | cut -c1-12
}

# Function to get remote digest without pulling
get_remote_digest() {
    local image=$1
    local platform=$2
    local arch="${platform#*/}"  # Remove 'linux/' prefix
    
    echo -e "${YELLOW}Getting digest for ${image} (${arch})...${NC}"
    
    # Parse registry and image details
    local registry=$(echo "$image" | cut -d'/' -f1)
    local repo_path=$(echo "$image" | cut -d'/' -f2-)
    
    # Handle different registries
    case "$registry" in
        "docker.io" | "index.docker.io")
            # Docker Hub handling
            local repo_owner=$(echo "$repo_path" | cut -d'/' -f1)
            local repo_name=$(echo "$repo_path" | cut -d'/' -f2-)
            
            # If no owner specified, assume library
            if [ "$repo_name" = "" ]; then
                repo_owner="library"
                repo_name="$repo_path"
            fi
            
            # Get token first
            local token=$(curl -s "https://auth.docker.io/token?service=registry.docker.io&scope=repository:${repo_owner}/${repo_name}:pull" | jq -r .token)
            
            if [ -n "$token" ] && [ "$token" != "null" ]; then
                # Get manifest list
                local manifest_list=$(curl -s -H "Authorization: Bearer $token" \
                    -H "Accept: application/vnd.docker.distribution.manifest.list.v2+json" \
                    "https://registry-1.docker.io/v2/${repo_owner}/${repo_name}/manifests/latest")
                
                # Process manifest
                process_manifest "$manifest_list" "$arch"
                return $?
            fi
            ;;
            
        "lscr.io")
            # LinuxServer.io registry handling
            export DOCKER_CLI_EXPERIMENTAL=enabled
            local manifest_inspect=$(docker manifest inspect "$image:latest" 2>/dev/null)
            
            if [ -n "$manifest_inspect" ]; then
                local digest=$(echo "$manifest_inspect" | \
                    jq -r --arg arch "$arch" '.manifests[] | select(.platform.architecture == $arch) | .digest' | \
                    cut -d':' -f2 | cut -c1-12)
                
                if [ -n "$digest" ] && [ "$digest" != "null" ]; then
                    echo -e "${GREEN}Found ${arch} digest: ${digest}${NC}"
                    echo "$digest"
                    return 0
                fi
            fi
            ;;
            
        *)
            # Default handling for other registries
            export DOCKER_CLI_EXPERIMENTAL=enabled
            local manifest_inspect=$(docker manifest inspect "$image:latest" 2>/dev/null)
            
            if [ -n "$manifest_inspect" ]; then
                local digest=$(echo "$manifest_inspect" | \
                    jq -r --arg arch "$arch" '.manifests[] | select(.platform.architecture == $arch) | .digest' | \
                    cut -d':' -f2 | cut -c1-12)
                
                if [ -n "$digest" ] && [ "$digest" != "null" ]; then
                    echo -e "${GREEN}Found ${arch} digest: ${digest}${NC}"
                    echo "$digest"
                    return 0
                fi
            fi
            ;;
    esac
    
    echo -e "${RED}Failed to get digest for ${arch}${NC}"
    return 1
}

# Helper function to process manifest and extract digest
process_manifest() {
    local manifest_list=$1
    local arch=$2
    
    # Get architecture-specific manifest digest
    local manifest_digest=$(echo "$manifest_list" | \
        jq -r --arg arch "$arch" '.manifests[] | select(.platform.architecture == $arch) | .digest')
    
    if [ -n "$manifest_digest" ] && [ "$manifest_digest" != "null" ]; then
        # Get the actual image manifest
        local image_manifest=$(curl -s -H "Authorization: Bearer $token" \
            -H "Accept: application/vnd.docker.distribution.manifest.v2+json" \
            "https://registry-1.docker.io/v2/${repo_owner}/${repo_name}/manifests/${manifest_digest}")
        
        # Get config digest
        local config_digest=$(echo "$image_manifest" | jq -r '.config.digest')
        
        if [ -n "$config_digest" ] && [ "$config_digest" != "null" ]; then
            # Extract just the hash part and first 12 characters
            local short_digest=$(echo "$config_digest" | sed 's/^sha256://g' | cut -c1-12)
            echo -e "${GREEN}Found ${arch} digest: ${short_digest}${NC}"
            echo "${short_digest}"
            return 0
        fi
    fi
    
    return 1
}

# Function to check if image needs update
needs_update() {
    local source_image=$1
    local source_tag=$2
    local target_image=$3
    local arch=$4
    
    # Get remote digest without pulling
    local remote_digest=$(get_remote_digest "$source_image" "$arch")
    local local_digest=$(get_image_digest "$target_image" "latest")
    
    if [ -z "$local_digest" ]; then
        echo -e "${YELLOW}No existing image found in target repository${NC}"
        return 0
    fi
    
    if [ -z "$remote_digest" ]; then
        echo -e "${YELLOW}Unable to get remote digest, will try pulling${NC}"
        return 0
    fi
    
    if [ "$remote_digest" != "$local_digest" ]; then
        echo -e "${YELLOW}New version detected (different digest)${NC}"
        echo -e "Remote: $remote_digest"
        echo -e "Local:  $local_digest"
        return 0
    else
        echo -e "${GREEN}Image is up to date (digest: $local_digest)${NC}"
        return 1
    fi
}

# Function to check if docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        install_docker
    fi
}

# Function to login to Docker Hub
docker_login() {
    echo -e "${YELLOW}Logging in to Docker Hub...${NC}"
    docker login
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to login to Docker Hub${NC}"
        exit 1
    fi
    echo -e "${GREEN}Successfully logged in to Docker Hub${NC}"
}

# Function specifically for ARM64 images
pull_arm64_image() {
    local full_image=$1
    local platform="linux/arm64"
    local username="${USERNAME:-fazee6}"
    
    echo -e "${YELLOW}Checking for ARM64 version of $full_image...${NC}"
    
    # Get remote digest without pulling
    local remote_digest=$(get_remote_digest "$full_image" "$platform")
    if [ -n "$remote_digest" ]; then
        echo -e "${GREEN}Found remote digest: ${remote_digest}${NC}"
        
        # Construct the potential tag
        local potential_tag="${username}/${full_image##*/}:latest-${remote_digest}-arm64"
        
        # Check if image already exists in our registry
        if docker manifest inspect "$potential_tag" >/dev/null 2>&1; then
            echo -e "${YELLOW}Image already exists as ${potential_tag}${NC}"
            export version="latest"
            export pulled_image="$full_image"
            export PUSHED_DIGEST="${remote_digest}"
            return 0
        fi
        
        # Pull the image since it doesn't exist in our registry
        if pull_image_with_retry "$full_image" "$platform"; then
            local pulled_digest=$(get_valid_digest "$full_image" "latest")
            if [ -n "$pulled_digest" ]; then
                export version="latest"
                export pulled_image="$full_image"
                export PUSHED_DIGEST="${pulled_digest}"
                return 0
            fi
        fi
    fi
    
    echo -e "${RED}Failed to process ARM64 image${NC}"
    return 1
}

# Function specifically for AMD64 images
pull_amd64_image() {
    local full_image=$1
    local platform="linux/amd64"
    local username="${USERNAME:-fazee6}"
    
    echo -e "${YELLOW}Checking for AMD64 version of $full_image...${NC}"
    
    # Get remote digest without pulling
    local remote_digest=""
    remote_digest=$(get_remote_digest "$full_image" "$platform" | tail -n1 | tr -d '\n\r')
    
    if [ -n "$remote_digest" ]; then
        # Clean up the digest to ensure no color codes or extra text
        remote_digest=$(echo "$remote_digest" | sed 's/\x1b\[[0-9;]*m//g')
        
        # Construct the potential tag
        local potential_tag="${username}/${full_image##*/}:latest-${remote_digest}-amd64"
        
        # Check if image already exists in our registry
        if docker manifest inspect "$potential_tag" >/dev/null 2>&1; then
            echo -e "${YELLOW}Image ${potential_tag} already exists in registry${NC}"
            export version="latest"
            export pulled_image="$full_image"
            export PUSHED_DIGEST="${remote_digest}"
            return 0
        fi
        
        # Pull the image since it doesn't exist in our registry
        if pull_image_with_retry "$full_image" "$platform"; then
            export version="latest"
            export pulled_image="$full_image"
            export PUSHED_DIGEST="${remote_digest}"
            return 0
        fi
    fi
    
    echo -e "${RED}Failed to process AMD64 image${NC}"
    return 1
}

# Function to verify Docker Hub push access
verify_push_access() {
    local username=$1
    
    echo -e "${YELLOW}Verifying push access for user ${username}...${NC}"
    
    if ! curl -s "https://hub.docker.com/v2/repositories/${username}/" > /dev/null; then
        echo -e "${RED}Error: Unable to access Docker Hub for user ${username}${NC}"
        return 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        echo -e "${RED}Error: Docker is not running or not properly configured${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Push access verified${NC}"
    return 0
}

# Update the push_image function
push_image() {
    local full_image=$1
    local arch=$2
    local version=$3
    local username=$4
    
    image_name=$(echo "$full_image" | awk -F'/' '{print $NF}')
    local digest="${PUSHED_DIGEST}"
    
    if [ -z "$digest" ]; then
        echo -e "${RED}No valid digest available${NC}"
        return 1
    fi
    
    # Construct tag
    local tag="${username}/${image_name}:latest-${digest}-${arch}"
    
    # Check if image already exists
    if docker manifest inspect "${tag}" >/dev/null 2>&1; then
        echo -e "${YELLOW}Image ${tag} already exists in registry, skipping push${NC}"
        return 0
    fi
    
    # Tag and push with retries
    echo -e "${YELLOW}Tagging and pushing ${tag}${NC}"
    if ! docker tag "${full_image}:${version}" "${tag}"; then
        echo -e "${RED}Failed to tag image as ${tag}${NC}"
        return 1
    fi
    
    local max_retries=3
    local retry=0
    
    while [ $retry -lt $max_retries ]; do
        if docker push "${tag}"; then
            echo -e "${GREEN}Successfully pushed ${tag}${NC}"
            return 0
        fi
        
        retry=$((retry + 1))
        if [ $retry -lt $max_retries ]; then
            echo -e "${YELLOW}Push failed, retrying ($retry of $max_retries)...${NC}"
            sleep 3
        fi
    done
    
    echo -e "${RED}Failed to push image after ${max_retries} attempts${NC}"
    return 1
}

# Add new function to check if manifest needs updating
check_manifest_needs_update() {
    local username=$1
    local image_name=$2
    local arm64_digest=$3
    local amd64_digest=$4
    
    export DOCKER_CLI_EXPERIMENTAL=enabled
    local manifest_name="${username}/${image_name}:latest"
    
    # Check if manifest exists
    if ! docker manifest inspect "${manifest_name}" >/dev/null 2>&1; then
        echo -e "${YELLOW}Manifest doesn't exist, needs creation${NC}"
        return 0
    fi
    
    # Get current manifest digests without showing details
    local current_arm64_digest=$(docker manifest inspect "${manifest_name}" | \
        jq -r '.manifests[] | select(.platform.architecture == "arm64") | .digest' | cut -d':' -f2 | cut -c1-12)
    local current_amd64_digest=$(docker manifest inspect "${manifest_name}" | \
        jq -r '.manifests[] | select(.platform.architecture == "amd64") | .digest' | cut -d':' -f2 | cut -c1-12)
    
    echo -e "\n${YELLOW}Digest comparison:${NC}"
    echo -e "ARM64 - Current: $current_arm64_digest, New: $arm64_digest"
    echo -e "AMD64 - Current: $current_amd64_digest, New: $amd64_digest"

    # Only show detailed manifest info if digests are identical
    if [ "$arm64_digest" = "$amd64_digest" ]; then
        echo -e "${YELLOW}Warning: ARM64 and AMD64 digests are identical${NC}"
        echo -e "This is unusual but can happen if the images are exactly the same."
        echo -e "Verifying individual image details..."
        
        echo -e "\n${YELLOW}Current manifest details:${NC}"
        docker manifest inspect "${manifest_name}" | jq '.manifests[]'
        
        # Show details of both architecture images
        local arm64_tag="${username}/${image_name}:latest-${arm64_digest}-arm64"
        local amd64_tag="${username}/${image_name}:latest-${amd64_digest}-amd64"
        
        echo -e "\n${YELLOW}ARM64 image details:${NC}"
        docker inspect "$arm64_tag" 2>/dev/null | jq '.[0].Architecture, .[0].Os'
        
        echo -e "\n${YELLOW}AMD64 image details:${NC}"
        docker inspect "$amd64_tag" 2>/dev/null | jq '.[0].Architecture, .[0].Os'
    fi
    
    if [ "$current_arm64_digest" = "$arm64_digest" ] && [ "$current_amd64_digest" = "$amd64_digest" ]; then
        echo -e "${GREEN}Manifest is up to date${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}Manifest needs updating${NC}"
    return 0
}

# Update create_manifest function
create_manifest() {
    local image_name=$1
    local username=$2
    local version=$3
    local arm64_digest=$4
    local amd64_digest=$5
    
    # Validate inputs
    if [ -z "$arm64_digest" ] || [ -z "$amd64_digest" ]; then
        echo -e "${RED}Error: Missing digests for manifest creation${NC}"
        return 1
    fi
    
    # Check if manifest needs updating
    if ! check_manifest_needs_update "$username" "$image_name" "$arm64_digest" "$amd64_digest"; then
        return 0
    fi
    
    export DOCKER_CLI_EXPERIMENTAL=enabled
    
    # Define manifest names
    local manifest_name="${username}/${image_name}:latest"
    local arm64_tag="${username}/${image_name}:latest-${arm64_digest}-arm64"
    local amd64_tag="${username}/${image_name}:latest-${amd64_digest}-amd64"
    
    # Remove existing manifest if any
    docker manifest rm "${manifest_name}" 2>/dev/null || true
    
    # Create and annotate manifest
    if ! docker manifest create "${manifest_name}" \
        "${arm64_tag}" \
        "${amd64_tag}"; then
        echo -e "${RED}Failed to create manifest${NC}"
        return 1
    fi
    
    docker manifest annotate "${manifest_name}" \
        "${arm64_tag}" --os linux --arch arm64 --variant v8
    
    docker manifest annotate "${manifest_name}" \
        "${amd64_tag}" --os linux --arch amd64
    
    # Push manifest with retries
    local max_retries=3
    local retry=0
    
    while [ $retry -lt $max_retries ]; do
        if docker manifest push --purge "${manifest_name}"; then
            echo -e "${GREEN}Successfully pushed manifest${NC}"
            return 0
        fi
        retry=$((retry + 1))
        if [ $retry -lt $max_retries ]; then
            echo -e "${YELLOW}Manifest push failed, retrying ($retry of $max_retries)...${NC}"
            sleep 3
        fi
    done
    
    echo -e "${RED}Failed to push manifest after ${max_retries} attempts${NC}"
    return 1
}

# Add this new function after the create_manifest function and before docker_pull_push_main

update_favorite_images() {
    local username="fazee6"
    export USERNAME="$username"  # Make username available to other functions
    local favorite_images=(
        "teddysun/xray"
        "lscr.io/linuxserver/wireguard"
        "v2fly/v2fly-core"
        "jellyfin/jellyfin"
        "fazee6/socks5proxy"
        "shadowsocks/shadowsocks-libev"
        "jlesage/jdownloader-2"
        "headscale/headscale"
        "qmcgaw/gluetun"
    )

    echo -e "\n${YELLOW}Checking for updates to favorite images...${NC}"
    
    for image in "${favorite_images[@]}"; do
        echo -e "\n${GREEN}═══════════════════════════════════════════${NC}"
        echo -e "${GREEN}Processing image: $image${NC}"
        echo -e "${GREEN}═══════════════════════════════════════════${NC}"
        
        arm64_pushed=false
        amd64_pushed=false
        last_version=""
        version=""
        pulled_image=""
        arm64_digest=""
        amd64_digest=""
        
        # Try ARM64
        if pull_arm64_image "$image"; then
            push_image "$pulled_image" "arm64" "$version" "$username"
            arm64_pushed=true
            last_version=$version
            arm64_digest=$PUSHED_DIGEST
        fi

        version=""
        pulled_image=""
        
        # Try AMD64
        if pull_amd64_image "$image"; then
            push_image "$pulled_image" "amd64" "$version" "$username"
            amd64_pushed=true
            last_version=$version
            amd64_digest=$PUSHED_DIGEST
        fi

        # Create manifest if both architectures were pushed
        if [ "$arm64_pushed" = true ] && [ "$amd64_pushed" = true ]; then
            export DOCKER_CLI_EXPERIMENTAL=enabled
            create_manifest "${image##*/}" "$username" "$last_version" "$arm64_digest" "$amd64_digest"
        elif [ "$arm64_pushed" = true ] || [ "$amd64_pushed" = true ]; then
            echo -e "${YELLOW}Warning: Only one architecture was processed successfully${NC}"
            echo -e "ARM64: $([ "$arm64_pushed" = true ] && echo "Success" || echo "Failed")"
            echo -e "AMD64: $([ "$amd64_pushed" = true ] && echo "Success" || echo "Failed")"
        else
            echo -e "${RED}Failed to process both architectures${NC}"
        fi
        
        echo -e "${GREEN}───────────────────────────────────────────${NC}"
    done

    echo -e "\n${GREEN}Finished checking all favorite images${NC}"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Add this new function to check if image exists in repository
check_image_exists() {
    local username="$1"
    local image_name="$2"
    local tag="$3"
    
    # Try to pull the image silently to check if it exists
    if docker manifest inspect "$username/$image_name:$tag" >/dev/null 2>&1; then
        return 0  # Image exists
    else
        return 1  # Image doesn't exist
    fi
}

# Main function
docker_pull_push_main() {
    clear
    check_docker
    docker_login

    while true; do
        clear
        echo -e "${YELLOW}Docker Image Operations:${NC}"
        echo "1. Pull and Push Custom Image"
        echo "2. Update Favorite Images"
        echo "3. Return to Previous Menu"
        
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1)
                clear
                username="fazee6"

                if ! verify_push_access "$username"; then
                    echo -e "${RED}Please fix the access issues and try again${NC}"
                    read -n 1 -s -r -p "Press any key to continue..."
                    continue
                fi

                echo -e "${YELLOW}Enter the image name (e.g., jlesage/jdownloader-2 or nginx):${NC}"
                read image_name

                version=""
                pulled_image=""
                full_image_path="$image_name"
                arm64_pushed=false
                amd64_pushed=false
                last_version=""

                echo -e "${YELLOW}Checking for ARM64 version...${NC}"
                if pull_arm64_image "$full_image_path"; then
                    push_image "$pulled_image" "arm64" "$version" "$username"
                    arm64_pushed=true
                    last_version=$version
                else
                    echo -e "${YELLOW}Skipping ARM64 version${NC}"
                fi

                version=""
                pulled_image=""

                echo -e "${YELLOW}Checking for AMD64 version...${NC}"
                if pull_amd64_image "$full_image_path"; then
                    push_image "$pulled_image" "amd64" "$version" "$username"
                    amd64_pushed=true
                    last_version=$version
                else
                    echo -e "${YELLOW}Skipping AMD64 version${NC}"
                fi

                if [ "$arm64_pushed" = true ] && [ "$amd64_pushed" = true ]; then
                    image_name=$(echo "$full_image_path" | awk -F'/' '{print $NF}')
                    export DOCKER_CLI_EXPERIMENTAL=enabled
                    create_manifest "$image_name" "$username" "$last_version" "$PUSHED_DIGEST" "$PUSHED_DIGEST"
                fi

                read -n 1 -s -r -p "Press any key to continue..."
                ;;
            2)
                clear
                update_favorite_images
                ;;
            3)
                return 0
                ;;
            *)
                echo -e "${RED}Invalid choice. Please try again.${NC}"
                sleep 1
                ;;
        esac
    done
}