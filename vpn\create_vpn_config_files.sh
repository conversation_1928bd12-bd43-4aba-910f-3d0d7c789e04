
wg_config_file_creation(){
cat <<EOF | sudo tee /wg_config/config/wg_confs/wg0.conf > /dev/null
[Interface] 
Address = $ip_subnet.1
ListenPort = $server_port  
PrivateKey = gBB2sBUHaAv+CdYBGc4gedhcmLgF89ATNhPRNWO2sEc=

PostUp = iptables -A FORWARD -i %i -j ACCEPT; iptables -A FORWARD -o %i -j ACCEPT; iptables -t nat -A POSTROUTING -o eth+ -j MASQUERADE
PostDown = iptables -D FORWARD -i %i -j ACCEPT; iptables -D FORWARD -o %i -j ACCEPT; iptables -t nat -D POSTROUTING -o eth+ -j MASQUERADE

$iptables_command_for_portrange

[Peer]   
# peer_myPC
PublicKey = zAyFS7OwlegvfCaWP3NAEYwVrJ5/baW5o108JqCBqD0=    
PresharedKey = WhX2rdORPjOb1LO07Gfqr7nNMrKUgjYo44820tOT6hs=
AllowedIPs = $ip_subnet.2/32

[Peer]  
# peer_myPhone
PublicKey = S3jG3IeY46EHK32/8ej/qFJ76bfBLCJITM2YnfLXkS8= 
PresharedKey = ZGczVT8EuOE2/44PY4uapMeYI9I0wcNV0OHYS/7/XQU=    
AllowedIPs = $ip_subnet.3/32

[Peer]
# peer_myExtra1
PublicKey = qQe3ygxu9B8qsqdMJ/b3c57ubUOJVLZr1IGlWl4KQhU=  
PresharedKey = hTg9ZhEUiUmIhA8NFMlwcBRphhEdmL80jYNhsb6xvfQ= 
AllowedIPs = $ip_subnet.4/32   

[Peer]    
# peer_myExtra2 
PublicKey = RhXKM8YzKbeywtw+jYAT7ah7JKUuCx7FQDvr1o8ROwQ=   
PresharedKey = +9jpdNsEP/dC98Y3RHnPieIL/msAoaQFhdSI3Iu3s/8=   
AllowedIPs = $ip_subnet.5/32
EOF

}

wg_config_file_creation_loop(){

cat <<EOF | sudo tee /wg_config/config/wg_confs/wg0.conf > /dev/null
[Interface] 
Address = $ip_subnet.1
ListenPort = $server_port  
PrivateKey = gBB2sBUHaAv+CdYBGc4gedhcmLgF89ATNhPRNWO2sEc=

PostUp = iptables -A FORWARD -i %i -j ACCEPT; iptables -A FORWARD -o %i -j ACCEPT; iptables -t nat -A POSTROUTING -o eth+ -j MASQUERADE
PostDown = iptables -D FORWARD -i %i -j ACCEPT; iptables -D FORWARD -o %i -j ACCEPT; iptables -t nat -D POSTROUTING -o eth+ -j MASQUERADE
EOF

# Add a new line before the loop
echo "" | sudo tee -a /wg_config/config/wg_confs/wg0.conf > /dev/null

# Append values from iptable_array
for value in "${iptable_array[@]}"
do
    echo "$value" | sudo tee -a /wg_config/config/wg_confs/wg0.conf > /dev/null
done

# Add a new line after the loop
echo "" | sudo tee -a /wg_config/config/wg_confs/wg0.conf > /dev/null

# Append the subsequent content
cat <<EOF | sudo tee -a /wg_config/config/wg_confs/wg0.conf > /dev/null
[Peer]   
# peer_myPC
PublicKey = zAyFS7OwlegvfCaWP3NAEYwVrJ5/baW5o108JqCBqD0=    
PresharedKey = WhX2rdORPjOb1LO07Gfqr7nNMrKUgjYo44820tOT6hs=
AllowedIPs = $ip_subnet.2/32

[Peer]  
# peer_myPhone
PublicKey = S3jG3IeY46EHK32/8ej/qFJ76bfBLCJITM2YnfLXkS8= 
PresharedKey = ZGczVT8EuOE2/44PY4uapMeYI9I0wcNV0OHYS/7/XQU=    
AllowedIPs = $ip_subnet.3/32

[Peer]
# peer_myExtra1
PublicKey = qQe3ygxu9B8qsqdMJ/b3c57ubUOJVLZr1IGlWl4KQhU=  
PresharedKey = hTg9ZhEUiUmIhA8NFMlwcBRphhEdmL80jYNhsb6xvfQ= 
AllowedIPs = $ip_subnet.4/32   

[Peer]    
# peer_myExtra2 
PublicKey = RhXKM8YzKbeywtw+jYAT7ah7JKUuCx7FQDvr1o8ROwQ=   
PresharedKey = +9jpdNsEP/dC98Y3RHnPieIL/msAoaQFhdSI3Iu3s/8=   
AllowedIPs = $ip_subnet.5/32
EOF

}

wg_peer_myPC_file_creation(){

cat <<EOF | sudo tee /wg_config/config/peer_myPC/peer_myPC.conf > /dev/null
[Interface]
Address = $ip_subnet.2
PrivateKey = oHlVdEcyXUySohayv8aWw8ha6ChsrpkhK8kkMGoG1k0=
ListenPort = $server_port
DNS = $ip_subnet.1

[Peer]
PublicKey = AxVUI1maDBxOPJOiLBssqorLSw+rj4yj/WQf17yct3k=
PresharedKey = WhX2rdORPjOb1LO07Gfqr7nNMrKUgjYo44820tOT6hs=
Endpoint = $server_ip:$server_port
AllowedIPs = 0.0.0.0/0
EOF

}
wg_peer_myPhone_file_creation(){

cat <<EOF | sudo tee /wg_config/config/peer_myPhone/peer_myPhone.conf > /dev/null
[Interface]
Address = $ip_subnet.3
PrivateKey = cOtaWfd76RpbLMawDzW8EDpFiAp+JgDNXMTv2/c+Flc=
ListenPort = $server_port
DNS = $ip_subnet.1

[Peer]
PublicKey = AxVUI1maDBxOPJOiLBssqorLSw+rj4yj/WQf17yct3k=
PresharedKey = ZGczVT8EuOE2/44PY4uapMeYI9I0wcNV0OHYS/7/XQU=
Endpoint = $server_ip:$server_port
AllowedIPs = 0.0.0.0/0
EOF

}
wg_peer_myExtra1_file_creation(){

cat <<EOF | sudo tee /wg_config/config/peer_myExtra1/peer_myExtra1.conf > /dev/null
[Interface]
Address = $ip_subnet.4
PrivateKey = 4Io9aDEsW3q8QxQr9jgkIHi7OMl5t4qJANldN2b8QW8=
ListenPort = $server_port
DNS = $ip_subnet.1

[Peer]
PublicKey = AxVUI1maDBxOPJOiLBssqorLSw+rj4yj/WQf17yct3k=
PresharedKey = hTg9ZhEUiUmIhA8NFMlwcBRphhEdmL80jYNhsb6xvfQ=
Endpoint = $server_ip:$server_port
AllowedIPs = 0.0.0.0/0
EOF

}

wg_peer_myExtra2_file_creation(){

cat <<EOF | sudo tee /wg_config/config/peer_myExtra2/peer_myExtra2.conf > /dev/null
[Interface]
Address = $ip_subnet.5
PrivateKey = gPaNWLPg6VQdPwzLPlboVmoCH/WI1+69exJyJa7qRmI=
ListenPort = $server_port
DNS = $ip_subnet.1

[Peer]
PublicKey = AxVUI1maDBxOPJOiLBssqorLSw+rj4yj/WQf17yct3k=
PresharedKey = +9jpdNsEP/dC98Y3RHnPieIL/msAoaQFhdSI3Iu3s/8=
Endpoint = $server_ip:$server_port
AllowedIPs = 0.0.0.0/0
EOF

}