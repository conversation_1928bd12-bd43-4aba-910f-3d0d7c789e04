iptable_array=()
install_wireguard() {
    install_docker
    get_ip_subnet

    echo -e "${RED}╔═══════════════════════════════╗${NC}"
    echo -e "${RED}║    Installing WireGuard       ║${NC}"
    echo -e "${RED}╚═══════════════════════════════╝${NC}"
    echo ""

    while true; do
        read -rp "$(echo -e "${YELLOW}Do you want WireGuard with port forwarding? (yes/no): ${NC}")" port_forward_choice
        if [[ "$port_forward_choice" =~ ^(yes|no)$ ]]; then
            break
        else
            echo "Invalid input. Please enter 'yes' or 'no'."
        fi
    done

    if [[ $port_forward_choice == "yes" ]]; then
        read -rp "Enter client IP for port forwarding or press enter for default ip ($ip_subnet.2): " wireguard_client_ip
        if [ -z "$wireguard_client_ip" ]; then
            wireguard_client_ip="$ip_subnet.2" # Set default IP
        fi
            
        ip_regex='^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'

        while ! [[ "$wireguard_client_ip" =~ $ip_regex ]]; do
            echo "Invalid IP address. Please enter a valid IPv4 address."
            read -rp "Enter client IP for port forwarding: " wireguard_client_ip
        done

        read -rp "Do you want to forward default ports (80 443 143 993 110 995 25 465 5000 5001 44444 4040 2001 2002 2003 2004 2005 2006 2007 2008 2009)? [Y/n]: " choice
        if [[ "$choice" =~ ^[Yy]$ || "$choice" == "" ]]; then
            ports_array=(80 443 143 993 110 995 25 465 5000 5001 44444 4040 2001 2002 2003 2004 2005 2006 2007 2008 2009)
        else
            read -rp "Enter the ports to forward (separated by spaces): " custom_ports
            ports_array=($custom_ports)
        fi

        for forward_port_input in "${ports_array[@]}"; do
            if [ $? -eq 0 ]; then
                ports_holder+=" -p $forward_port_input:$forward_port_input"
                line1="PostUp = iptables -t nat -A PREROUTING -p tcp --dport $forward_port_input -j DNAT --to-destination $wireguard_client_ip:$forward_port_input"
                iptable_array+=("$line1")
            fi
        done

        ports_holder="${ports_holder# }"
    fi

    echo -e "${YELLOW}Choose an option:${NC}"
    echo -e "${YELLOW}1.${NC} Automatically find public IP"
    echo -e "${YELLOW}2.${NC} Enter server IP manually"
    while true; do
        read -rp "Enter your choice (1 or 2): " option
        if [[ "$option" =~ ^[12]$ ]]; then
            break
        else
            echo "Invalid input. Please enter '1' or '2'."
        fi
    done

    if [ "$option" == "2" ]; then
        read -rp "Please enter the public IP: " server_ip
        while ! [[ "$server_ip" =~ $ip_regex ]]; do
        echo "Invalid IP address. Please enter a valid IP address."
        read -rp "Please enter the public IP: " server_ip
        done
    fi

    if [ "$option" == "1" ]; then
        server_ip=$(get_public_ip)
    fi

    echo ""
    echo -e "${YELLOW}Choose WireGuard configuration:${NC}"
    echo -e "${YELLOW}1.${NC} Default Configuration"
    echo -e "${YELLOW}2.${NC} Custom Configuration"
    while true; do
        read -rp "Enter your choice (1 or 2): " conf_choice
        if [[ "$conf_choice" =~ ^[12]$ ]]; then
            break
        else
            echo "Invalid input. Please enter '1' or '2'."
        fi
    done

    case $conf_choice in
        1)
            server_port="51820"
            ;;
        2)
            while true; do
                read -rp "Enter port for wireguard server: " server_port_input
                get_valid_port "$server_port_input"
                if [ $? -eq 0 ]; then
                    server_port=$server_port_input
                    break
                fi
            done
            ;;
    esac

    stop_and_remove_container "wireguard"
    if [ $? -eq 0 ]; then
        remove_wg_config_files
    fi

    echo -e "${YELLOW}Pulling image from Docker Hub...${NC}"
    docker pull fazee6/wireguard:latest
    echo ""
    
    if [[ $port_forward_choice == "yes" ]]; then
        docker_command="sudo docker run -d --name=wireguard --cap-add=NET_ADMIN --cap-add=SYS_MODULE -e PUID=1000 -e PGID=1000 -e TZ=Africa/Nairobi -e SERVERURL=$server_ip -e SERVERPORT=$server_port -e PEERS="myPC,myPhone,myExtra1,myExtra2" -e PEERDNS=auto -e INTERNAL_SUBNET=$ip_subnet.0 -e ALLOWEDIPS=0.0.0.0/0 -e PERSISTENTKEEPALIVE_PEERS= -e LOG_CONFS=true -v /wg_config/config:/config -v /lib/modules:/lib/modules --sysctl="net.ipv4.conf.all.src_valid_mark=1" --restart unless-stopped -p $server_port:$server_port/udp $ports_holder fazee6/wireguard:latest"

        $docker_command

        if [ $? -ne 0 ]; then
        echo "Failed to start WireGuard Docker container."
        return 1
        fi

        docker update --restart unless-stopped wireguard

        sleep 5

        wg_config_file_creation_loop
        wg_peer_myPC_file_creation
        wg_peer_myPhone_file_creation
        wg_peer_myExtra1_file_creation
        wg_peer_myExtra2_file_creation

        sleep 5
        sudo docker restart wireguard

    else
        sudo docker run -d \
        --name=wireguard \
        --cap-add=NET_ADMIN \
        --cap-add=SYS_MODULE \
        -e PUID=1000 \
        -e PGID=1000 \
        -e TZ=Africa/Nairobi \
        -e SERVERURL="$server_ip" \
        -e SERVERPORT="$server_port" \
        -e PEERS="myPC,myPhone,myExtra1,myExtra2" \
        -e PEERDNS=auto \
        -e INTERNAL_SUBNET=$ip_subnet.0 \
        -e ALLOWEDIPS=0.0.0.0/0 \
        -e PERSISTENTKEEPALIVE_PEERS= \
        -e LOG_CONFS=true \
        -p "$server_port":"$server_port"/udp \
        -v /wg_config/config:/config \
        -v /lib/modules:/lib/modules \
        --sysctl="net.ipv4.conf.all.src_valid_mark=1" \
        --restart unless-stopped \
        fazee6/wireguard:latest

        if [ $? -ne 0 ]; then
        echo "Failed to start WireGuard Docker container."
        return 1
        fi

        docker update --restart unless-stopped wireguard

        sleep 5

        wg_config_file_creation
        wg_peer_myPC_file_creation
        wg_peer_myPhone_file_creation
        wg_peer_myExtra1_file_creation
        wg_peer_myExtra2_file_creation

        sleep 5

        if [ $? -eq 0 ]; then
            docker restart wireguard
            echo "Restarting WireGuard Docker container."
        fi
    fi
    
    if [ $? -eq 0 ]; then
        echo ""
        echo -e "${YELLOW}┌─────────────────────────────────────┐${NC}"
        echo -e "${YELLOW}│        WireGuard Server Info        │${NC}"
        echo -e "${YELLOW}├─────────────────────────────────────┤${NC}"
        echo -e "${YELLOW}│${NC} Public IP:  ${GREEN}$server_ip${NC}"
        echo -e "${YELLOW}│${NC} Port:       ${GREEN}$server_port${NC}"
        echo -e "${YELLOW}└─────────────────────────────────────┘${NC}"
        echo ""
        echo -e "${MAGENTA}┌─────────────────────────────────────────────────┐${NC}"
        echo -e "${MAGENTA}│  WireGuard configuration files are available in: │${NC}"
        echo -e "${MAGENTA}│  ${GREEN}/wg_config/config/${NC}"
        echo -e "${MAGENTA}└─────────────────────────────────────────────────┘${NC}"
        echo ""
    else
        echo -e "${RED}╔═══════════════════════════════════════════════╗${NC}"
        echo -e "${RED}║  WireGuard did not start. Please check logs.  ║${NC}"
        echo -e "${RED}╚══════════════════════════════════════════════╝${NC}"
    fi
}