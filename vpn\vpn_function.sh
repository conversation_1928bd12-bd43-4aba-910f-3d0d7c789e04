get_public_ip() {
    local ip

    # Method 1: dig
    ip=$(dig +short myip.opendns.com @resolver1.opendns.com)
    if [[ "$ip" =~ ^([0-9]{1,3}\.){3}[0-9]{1,3}$ ]]; then
        echo "$ip"
        return
    fi

    # Method 2: ifconfig.co
    ip=$(curl -4 -s ifconfig.co)
    if [[ "$ip" =~ ^([0-9]{1,3}\.){3}[0-9]{1,3}$ ]]; then
        echo "$ip"
        return
    fi

    # Method 3: ifconfig.me
    ip=$(curl -4 -s ifconfig.me)
    if [[ "$ip" =~ ^([0-9]{1,3}\.){3}[0-9]{1,3}$ ]]; then
        echo "$ip"
        return
    fi

    echo "Failed to retrieve public IP."
}

get_valid_port() {
    local port_number=$1

    # Check if it's a valid number within range
    if [[ ! "$port_number" =~ ^[0-9]+$ ]] || [[ "$port_number" -lt 1 || "$port_number" -gt 65535 ]]; then
        echo -e "${RED}Invalid port number. Please enter a valid integer between 1 and 65535.${NC}"
        return 1
    fi

    return 0
}

validate_input() {
    local prompt_message=$1
    local variable_name=$2
    local default_value=$3  # Add default value as the third parameter
    shift 3
    local valid_choices=("$@")

    while true; do
        read -rp "${YELLOW}$prompt_message${NC}" "$variable_name"

        # Check if the input is empty and set it to the default value if it is
        if [ -z "${!variable_name}" ]; then
            declare -g "$variable_name=$default_value"
        fi

        local is_valid=false
        for choice in "${valid_choices[@]}"; do
            if [[ "${!variable_name}" == "$choice" ]]; then
                is_valid=true
                break
            fi
        done

        if [ "$is_valid" = false ]; then
            echo -e "${RED}Invalid choice. Please enter one of: ${valid_choices[*]}${NC}"
        else
            break
        fi
    done
}

create_qrcode_for_phone() {
    echo -e "${CYAN}┌─────────────────────────────────┐${NC}"
    echo -e "${CYAN}│    Generating QR Code...        │${NC}"
    echo -e "${CYAN}└─────────────────────────────────┘${NC}"
    qrencode -t ANSIUTF8 "$(cat /wg_config/config/peer_myPhone/peer_myPhone.conf)"
}

show_pc_config(){
    echo -e "${CYAN}┌─────────────────────────────────┐${NC}"
    echo -e "${CYAN}│    PC Configuration             │${NC}"
    echo -e "${CYAN}└─────────────────────────────────┘${NC}"
    cat /wg_config/config/peer_myPC/peer_myPC.conf
}

get_ip_subnet() {
    echo -e "${CYAN}┌─────────────────────────────────┐${NC}"
    echo -e "${CYAN}│    IP Subnet Configuration      │${NC}"
    echo -e "${CYAN}└─────────────────────────────────┘${NC}"
    read -rp "Enter the IP subnet for WireGuard (default: 10.13.13): " IP_SUBNET
    if [ -z "$IP_SUBNET" ]; then
        IP_SUBNET="10.13.13" # Set default IP subnet
    fi

    ip_regex="^([0-9]{1,3}\.){2}[0-9]{1,3}$" # Regex pattern for first three parts of IP subnet

    while ! [[ "$IP_SUBNET" =~ $ip_regex ]]; do
        if [ -z "$IP_SUBNET" ]; then
            IP_SUBNET="10.13.13" # Set default IP subnet if no input is provided
            break
        fi
        echo -e "${RED}Invalid IP subnet. Please enter a valid IP subnet with first three parts (e.g., 10.10.10).${NC}"
        read -rp "Enter the IP subnet for WireGuard: " IP_SUBNET
    done
    ip_subnet=$IP_SUBNET
    echo -e "${GREEN}IP subnet set to: $ip_subnet${NC}"
}

remove_wg_config_files(){
    directory_path="/wg_config"

    if [ -d "$directory_path" ]; then
        echo -e "${YELLOW}Directory $directory_path exists. Removing it...${NC}"
        sudo rm -r "$directory_path"
        echo -e "${GREEN}Directory removed successfully.${NC}"
    fi
}

remove_v2ray_config_files() {
    directory_path="/etc/v2ray"

    if [ -d "$directory_path" ]; then
        echo -e "${YELLOW}Directory $directory_path exists. Removing it...${NC}"
        sudo rm -r "$directory_path"
        echo -e "${GREEN}Directory removed successfully.${NC}"
    else
        echo -e "${YELLOW}Directory $directory_path does not exist.${NC}"
    fi
}

remove_xray_config_files() {
    directory_path="/etc/xray"

    if [ -d "$directory_path" ]; then
        echo -e "${YELLOW}Directory $directory_path exists. Removing it...${NC}"
        sudo rm -r "$directory_path"
        echo -e "${GREEN}Directory removed successfully.${NC}"
    else
        echo -e "${YELLOW}Directory $directory_path does not exist.${NC}"
    fi
}

stop_and_remove_container() {
    local container_name="$1"
    
    echo -e "${CYAN}┌─────────────────────────────────┐${NC}"
    echo -e "${CYAN}│    Container Management         │${NC}"
    echo -e "${CYAN}└─────────────────────────────────┘${NC}"

    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        echo -e "${RED}Docker is not running.${NC}"
        return 1
    fi
    
    # Check if the container exists
    if docker ps -a --format '{{.Names}}' | grep -q "^$container_name$"; then
        
        # Check if the container is running
        if docker ps --format '{{.Names}}' | grep -q "^$container_name$"; then
            echo -e "${YELLOW}Stopping container: $container_name${NC}"
            if ! docker stop "$container_name"; then
                if ! docker kill "$container_name"; then
                    echo -e "${RED}Failed to stop container: $container_name${NC}"
                    return 1
                fi
            fi
        fi
        
        echo -e "${YELLOW}Removing container: $container_name${NC}"
        if ! docker rm -f "$container_name"; then
            echo -e "${RED}Failed to remove container: $container_name${NC}"
            return 1
        fi
        echo -e "${GREEN}Container $container_name removed successfully.${NC}"
    else
        echo -e "${YELLOW}Container $container_name does not exist.${NC}"
    fi
    
    return 0
}

remove_wireguard(){
    echo -e "${RED}╔═══════════════════════════════╗${NC}"
    echo -e "${RED}║     Removing WireGuard        ║${NC}"
    echo -e "${RED}╚═══════════════════════════════╝${NC}"
    stop_and_remove_container "wireguard"
    echo -e "${GREEN}WireGuard removed successfully.${NC}"
    remove_wg_config_files
}

remove_sock5(){
    echo -e "${RED}╔═══════════════════════════════╗${NC}"
    echo -e "${RED}║   Removing SOCKS5 Proxy       ║${NC}"
    echo -e "${RED}╚═══════════════════════════════╝${NC}"
    stop_and_remove_container "proxy"
    echo -e "${GREEN}SOCKS5 Proxy removed successfully.${NC}"
}

remove_shadowsock(){
    echo -e "${RED}╔═══════════════════════════════╗${NC}"
    echo -e "${RED}║   Removing Shadowsocks        ║${NC}"
    echo -e "${RED}╚═══════════════════════════════╝${NC}"
    stop_and_remove_container "ss-libev"
    echo -e "${GREEN}Shadowsocks removed successfully.${NC}"
}

remove_v2ray(){
    echo -e "${RED}╔═══════════════════════════════╗${NC}"
    echo -e "${RED}║        Removing V2Ray         ║${NC}"
    echo -e "${RED}╚═══════════════════════════════╝${NC}"
    stop_and_remove_container "v2ray"
    echo -e "${GREEN}V2Ray removed successfully.${NC}"
    remove_v2ray_config_files
}

remove_xray(){
    echo -e "${RED}╔═══════════════════════════════╗${NC}"
    echo -e "${RED}║        Removing Xray          ║${NC}"
    echo -e "${RED}╚═══════════════════════════════╝${NC}"
    stop_and_remove_container "xray"
    echo -e "${GREEN}Xray removed successfully.${NC}"
    remove_xray_config_files
}

remove_all_vpns(){
    echo -e "${RED}╔═══════════════════════════════╗${NC}"
    echo -e "${RED}║     Removing All VPNs         ║${NC}"
    echo -e "${RED}╚═══════════════════════════════╝${NC}"
    remove_wireguard
    remove_sock5
    remove_shadowsock
    remove_v2ray
    remove_xray

    echo -e "${YELLOW}Pruning Docker images and volumes...${NC}"
    docker image prune -f
    docker volume prune -f
    echo -e "${GREEN}All VPNs removed and Docker cleaned up.${NC}"
}

install_all_vpns(){
    echo -e "${GREEN}╔═══════════════════════════════╗${NC}"
    echo -e "${GREEN}║     Installing All VPNs       ║${NC}"
    echo -e "${GREEN}╚═══════════════════════════════╝${NC}"
    # Install WireGuard with default values
    install_wireguard <<EOF
10.13.13
no
1
1
EOF

    sleep 2

    # Install Sock5 with default values
    install_sock5 <<EOF
41234
EOF

    sleep 2

    # Install Shadowsocks with default values
    install_shadowsocks <<EOF
10400
EOF

    sleep 2

    # Install V2ray with default values
    install_v2ray <<EOF
4443

EOF

    sleep 2

    # Install xray with default values
    install_xray <<EOF
443

EOF

    echo -e "${GREEN}All VPNs installed successfully.${NC}"
}
