#!/bin/bash

# File Operations Menu Script
# Assuming create_menu is defined elsewhere (e.g., in a main script or a shared UI script)
# If not, its definition needs to be included or sourced as well.

# Main menu function for this module (can be called from a main script)
file_operations_main_menu() {
    while true; do
        options=(
            "1. File Operations"
            "2. Folder Operations"
            "3. Compression Operations"
            "4. Permission Management"
            "5. Back to Main Menu"
        )

        # Assuming create_menu exists and handles display
        create_menu "File Operations Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) show_file_menu ;;
            2) show_folder_menu ;;
            3) show_compression_menu ;;
            4) show_permission_menu ;;
            5) return ;; # Exit this menu function
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done
}

# Function to display file operations menu
show_file_menu() {
    while true; do
        options=(
            "1. Bulk Rename Files"
            "2. Search Files by Extension"
            "3. Copy Files by Extension"
            "4. Move Files by Extension"
            "5. Search and Delete Files"
            "6. Back to Previous Menu"
        )

        create_menu "File Operations" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) bulk_rename_files ;;             # From file_operations_file_rename.sh
            2) search_files_by_extension ;;     # From file_operations_file_manage.sh
            3) copy_files_by_extension ;;       # From file_operations_file_manage.sh
            4) move_files_by_extension ;;       # From file_operations_file_manage.sh
            5) search_delete_files ;;           # From file_operations_file_manage.sh
            6) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done
}

# Function to display folder operations menu
show_folder_menu() {
    while true; do
        options=(
            "1. Bulk Rename Folders"
            "2. Search Folders"
            "3. Copy Folders"
            "4. Move Folders"
            "5. Delete Folders"
            "6. Delete Empty Folders"
            "7. Back to Previous Menu"
        )

        create_menu "Folder Operations" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) bulk_rename_folders ;;           # From file_operations_folder_ops.sh
            2) search_folders ;;                # From file_operations_folder_ops.sh
            3) copy_folders ;;                  # From file_operations_folder_ops.sh
            4) move_folders ;;                  # From file_operations_folder_ops.sh
            5) delete_folders ;;                # From file_operations_folder_ops.sh
            6) delete_empty_folders ;;          # From file_operations_folder_ops.sh
            7) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done
}

# Function to display compression operations menu
show_compression_menu() {
    while true; do
        options=(
            "1. Compress Item(s)"             # Renamed for clarity
            "2. Decompress Archive(s)"
            "3. Back to Previous Menu"
        )

        create_menu "Compression Operations" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) compress_items ;;                # From file_operations_compression.sh
            2) decompress_archive ;;            # From file_operations_compression.sh
            3) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done
}

# Function to display permission management menu
show_permission_menu() {
    while true; do
        options=(
            "1. Change File Permissions"
            "2. Change Folder Permissions"
            "3. Change Ownership"
            "4. Back to Previous Menu"
        )

        create_menu "Permission Management" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) change_file_permissions ;;       # From file_operations_permissions.sh
            2) change_folder_permissions ;;     # From file_operations_permissions.sh
            3) change_ownership ;;              # From file_operations_permissions.sh
            4) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done
}

# If this script is meant to be run directly, you might add:
# file_operations_main_menu