#!/bin/bash

# Install and configure LiteLLM
install_litellm() {
    echo -e "${GREEN}Setting up LiteLLM...${NC}"
    
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        read -rp "$(echo -e ${YELLOW}"Do you want to install Docker? (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            install_docker
        else
            echo -e "${YELLOW}Operation cancelled. Docker is required to run LiteLLM.${NC}"
            return 1
        fi
    fi
    
    # Check if LiteLLM directory exists and offer to remove it
    if [ -d "/opt/litellm" ]; then
        echo -e "${YELLOW}LiteLLM directory already exists.${NC}"
        read -rp "$(echo -e ${YELLOW}"Do you want to remove it and install the latest version? (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            # Stop and completely remove current LiteLLM installation
            echo -e "${YELLOW}Stopping and removing current LiteLLM installation...${NC}"
            cd /opt/litellm
            docker compose down -v
            cd /opt
            sudo rm -rf /opt/litellm
            echo -e "${GREEN}Removed old LiteLLM installation${NC}"
        else
            echo -e "${YELLOW}Operation cancelled.${NC}"
            return 1
        fi
    fi
    
    # Create fresh LiteLLM directory
    sudo mkdir -p /opt/litellm
    sudo chown $(id -u):$(id -g) /opt/litellm
    cd /opt/litellm
    
    # Create .env file
    echo -e "${GREEN}Creating .env file...${NC}"
    cat > .env << 'EOL'
# API key settings with Bearer prefix to bypass validation
LITELLM_MASTER_KEY=Bearer_fazee
LITELLM_SALT_KEY=Bearer_fazee_salt

# Ensure database settings are correct
DATABASE_URL=**************************************/litellm
STORE_MODEL_IN_DB=True

# Additional settings to prevent issues
UI_USERNAME=admin
UI_PASSWORD=admin123
EOL

    # Create docker-compose file
    echo -e "${GREEN}Creating docker-compose file...${NC}"
    cat > docker-compose.yml << 'EOL'
services:
  litellm:
    image: ghcr.io/berriai/litellm:main-v1.63.8-nightly
    restart: always
    environment:
      - LITELLM_MASTER_KEY=Bearer_fazee
      - LITELLM_SALT_KEY=Bearer_fazee_salt
      - DATABASE_URL=**************************************/litellm
      - STORE_MODEL_IN_DB=True
      - UI_USERNAME=admin
      - UI_PASSWORD=admin123
    ports:
      - "4000:4000"
    networks:
      - my_network
    depends_on:
      - db
    volumes:
      - ./config.yaml:/app/config.yaml

  db:
    image: postgres:16-alpine
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=litellm
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - my_network
    expose:
      - 5432

  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    networks:
      - my_network
    restart: always

networks:
  my_network:
    external: true

volumes:
  postgres_data:
EOL

    # Create config file without problematic callbacks
    echo -e "${GREEN}Creating config.yaml file...${NC}"
    cat > config.yaml << 'EOL'
# Clean config without problematic callbacks
general_settings:
  telemetry: false
  
model_list:
  - model_name: gpt-3.5-turbo
    litellm_params:
      model: gpt-3.5-turbo
  - model_name: gpt-4
    litellm_params:
      model: gpt-4
EOL

    # Create prometheus.yml file
    echo -e "${GREEN}Creating prometheus.yml...${NC}"
    cat > prometheus.yml << 'EOL'
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'litellm'
    static_configs:
      - targets: ['litellm:4000']
EOL

    # Extract and check all ports used by containers
    echo -e "${GREEN}Checking for port conflicts...${NC}"
    
    # Define default ports and their services
    declare -A port_map=(
        ["4000"]="LiteLLM API"
        ["9090"]="Prometheus"
    )
    
    # Associative array to store new port assignments
    declare -A new_port_map
    
    # Scan for port conflicts
    for port in "${!port_map[@]}"; do
        service="${port_map[$port]}"
        echo -e "${CYAN}Checking port $port for $service...${NC}"
        
        # Check if port is in use on the host
        if lsof -i :"$port" >/dev/null 2>&1; then
            echo -e "${YELLOW}Port $port ($service) is already in use. Finding an alternative port...${NC}"
            
            # Find an available port starting from original + 1000
            new_port=$((port + 1000))
            while lsof -i :"$new_port" >/dev/null 2>&1; do
                new_port=$((new_port + 1))
            done
            
            echo -e "${GREEN}Found available port $new_port for $service.${NC}"
            
            # Store the new port mapping
            new_port_map["$port"]="$new_port"
            
            # Update the port in the docker-compose file
            sed -i "s/- \"$port:$port\"/- \"$new_port:$port\"/g" docker-compose.yml
            sed -i "s/- $port:$port/- $new_port:$port/g" docker-compose.yml
        else
            # Port is available, use as is
            new_port_map["$port"]="$port"
        fi
    done
    
    # Set permissions
    echo -e "${GREEN}Setting permissions...${NC}"
    sudo chown -R $(id -u):$(id -g) /opt/litellm
    sudo chmod -R 755 /opt/litellm
    
    # Start LiteLLM
    echo -e "${GREEN}Starting LiteLLM with the following port configuration:${NC}"
    for port in "${!port_map[@]}"; do
        service="${port_map[$port]}"
        new_port="${new_port_map[$port]}"
        echo -e "${CYAN}$service: localhost:$new_port${NC}"
    done
    
    docker compose up -d
    
    # Wait for containers to start
    echo -e "${YELLOW}Waiting for containers to initialize...${NC}"
    sleep 15
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}LiteLLM installed and started successfully!${NC}"
        echo -e "${GREEN}========================================${NC}"
        echo -e "${GREEN}Access LiteLLM at: http://localhost:${new_port_map["4000"]}${NC}"
        echo -e "${GREEN}Prometheus: http://localhost:${new_port_map["9090"]}${NC}"
        echo -e "${GREEN}========================================${NC}"
        
        # Check if OpenWebUI is already installed
        if ! is_openwebui_installed; then
            echo -e "${YELLOW}LiteLLM works well with OpenWebUI for a user-friendly interface.${NC}"
            read -rp "$(echo -e ${YELLOW}"Would you like to install OpenWebUI now? (y/n): "${NC})" choice
            if [[ $choice =~ ^[Yy]$ ]]; then
                install_openwebui
            else
                echo -e "${YELLOW}You can install OpenWebUI later from the menu if needed.${NC}"
            fi
        else
            echo -e "${GREEN}OpenWebUI is already installed. You can connect it to LiteLLM.${NC}"
            echo -e "${GREEN}In OpenWebUI, go to Settings > Models > Add Model Provider:${NC}"
            echo -e "${YELLOW}URL: http://localhost:${new_port_map["4000"]}${NC}"
            echo -e "${YELLOW}API Key: Bearer_fazee${NC}"
        fi
        
        return 0
    else
        echo -e "${RED}Failed to start LiteLLM.${NC}"
        return 1
    fi
}

# Check LiteLLM status
check_litellm_status() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Check if LiteLLM is installed
    if ! is_litellm_installed; then
        echo -e "${RED}LiteLLM is not installed.${NC}"
        read -rp "$(echo -e ${YELLOW}"Would you like to install LiteLLM now? (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            install_litellm
            return $?
        else
            echo -e "${YELLOW}You can install it later from the menu.${NC}"
            return 1
        fi
    fi
    
    # Check if containers are running
    echo -e "${CYAN}LiteLLM Status:${NC}"
    echo -e "${GREEN}========================================${NC}"
    
    # Get all containers with prefix litellm
    containers=$(docker ps -a --format "{{.Names}}" | grep "^litellm")
    
    if [ -z "$containers" ]; then
        echo -e "${RED}No LiteLLM containers found.${NC}"
        return 1
    fi
    
    # Check each container
    for container in $containers; do
        container_id=$(docker ps -f "name=$container" -q)
        if [ -n "$container_id" ]; then
            container_status="Running"
            status_color="${GREEN}"
            
            # Get container info
            image=$(docker inspect --format='{{.Config.Image}}' "$container_id")
            
            # Get port mappings
            port_mappings=$(docker port "$container_id" 2>/dev/null)
            port_info=""
            
            if [ -n "$port_mappings" ]; then
                # Extract and format port mappings
                port_info=$(echo "$port_mappings" | sed -E 's/.*:([0-9]+)->.*/http:\/\/localhost:\1/g' | sort | uniq | tr '\n' ', ' | sed 's/,$//')
            else
                port_info="No exposed ports"
            fi
        else
            container_status="Stopped"
            status_color="${RED}"
            image=$(docker inspect --format='{{.Config.Image}}' "$container" 2>/dev/null || echo "Unknown")
            port_info="N/A"
        fi
        
        # Extract readable container name (remove litellm- prefix if present)
        readable_name=$(echo "$container" | sed 's/litellm-//')
        
        # Display container info
        echo -e "${CYAN}$readable_name:${NC}"
        echo -e "  ${status_color}Status: $container_status${NC}"
        echo -e "  ${CYAN}Image: $image${NC}"
        
        if [ "$container_status" = "Running" ] && [ "$port_info" != "No exposed ports" ]; then
            echo -e "  ${CYAN}Access at: $port_info${NC}"
        fi
        
        echo -e "${GREEN}---------------------------------------${NC}"
    done
    
    # If main container is not running, ask if user wants to start it
    if ! docker ps --format '{{.Names}}' | grep -q "litellm-litellm"; then
        echo -e "${YELLOW}LiteLLM API is not running.${NC}"
        read -rp "$(echo -e ${YELLOW}"Would you like to start LiteLLM now? (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            start_litellm
        fi
    fi
    
    echo -e "${GREEN}========================================${NC}"
}

# Start LiteLLM
start_litellm() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Check if LiteLLM is installed
    if ! is_litellm_installed; then
        echo -e "${RED}LiteLLM is not installed. Please install it first.${NC}"
        read -rp "$(echo -e ${YELLOW}"Would you like to install LiteLLM now? (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            install_litellm
            return $?
        else
            echo -e "${YELLOW}Operation cancelled.${NC}"
            return 1
        fi
    fi
    
    # Start containers
    echo -e "${GREEN}Starting LiteLLM...${NC}"
    cd /opt/litellm
    docker compose up -d
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}LiteLLM started successfully!${NC}"
        check_litellm_status
    else
        echo -e "${RED}Failed to start LiteLLM.${NC}"
        return 1
    fi
}

# Stop LiteLLM
stop_litellm() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Check if LiteLLM directory exists
    if [ ! -d "/opt/litellm" ]; then
        echo -e "${RED}LiteLLM is not installed.${NC}"
        return 1
    fi
    
    # Stop containers
    echo -e "${YELLOW}Stopping LiteLLM...${NC}"
    cd /opt/litellm
    docker compose down
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}LiteLLM stopped successfully!${NC}"
    else
        echo -e "${RED}Failed to stop LiteLLM.${NC}"
        return 1
    fi
}

# Restart LiteLLM
restart_litellm() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Check if LiteLLM directory exists
    if [ ! -d "/opt/litellm" ]; then
        echo -e "${RED}LiteLLM is not installed. Please install it first.${NC}"
        return 1
    fi
    
    # Restart containers
    echo -e "${YELLOW}Restarting LiteLLM...${NC}"
    cd /opt/litellm
    docker compose restart
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}LiteLLM restarted successfully!${NC}"
        check_litellm_status
    else
        echo -e "${RED}Failed to restart LiteLLM.${NC}"
        return 1
    fi
}

# Check LiteLLM logs
check_litellm_logs() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Check if LiteLLM containers exist
    if ! docker ps -a --format "{{.Names}}" | grep -q "^litellm"; then
        echo -e "${RED}No LiteLLM containers found.${NC}"
        return 1
    fi
    
    echo -e "${CYAN}Which container logs do you want to check?${NC}"
    echo -e "${YELLOW}1. LiteLLM API (most useful for error messages)${NC}"
    echo -e "${YELLOW}2. Database${NC}"
    echo -e "${YELLOW}3. Prometheus${NC}"
    echo -e "${YELLOW}4. All containers${NC}"
    
    read -rp "$(echo -e ${YELLOW}"Enter your choice (1-4): "${NC})" choice
    
    case $choice in
        1)
            container="litellm-litellm"
            ;;
        2)
            container="litellm-db"
            ;;
        3)
            container="litellm-prometheus"
            ;;
        4)
            echo -e "${CYAN}=== LiteLLM API Logs ===${NC}"
            docker logs $(docker ps -aqf "name=litellm-litellm") 2>&1
            echo -e "${CYAN}=== Database Logs ===${NC}"
            docker logs $(docker ps -aqf "name=litellm-db") 2>&1
            echo -e "${CYAN}=== Prometheus Logs ===${NC}"
            docker logs $(docker ps -aqf "name=litellm-prometheus") 2>&1
            return 0
            ;;
        *)
            echo -e "${RED}Invalid choice.${NC}"
            return 1
            ;;
    esac
    
    # Get the container ID
    container_id=$(docker ps -aqf "name=$container")
    
    if [ -z "$container_id" ]; then
        echo -e "${RED}Container $container not found.${NC}"
        return 1
    fi
    
    # Show logs
    echo -e "${CYAN}Showing logs for $container:${NC}"
    docker logs $container_id 2>&1
}

# Troubleshoot common LiteLLM issues
troubleshoot_litellm() {
    echo -e "${CYAN}LiteLLM Troubleshooting:${NC}"
    echo -e "${GREEN}========================================${NC}"
    
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Check if LiteLLM directory exists
    if [ ! -d "/opt/litellm" ]; then
        echo -e "${RED}LiteLLM is not installed.${NC}"
        return 1
    fi
    
    # Check if all containers are running
    echo -e "${CYAN}Checking container status:${NC}"
    
    litellm_api_running=false
    db_running=false
    prometheus_running=false
    
    if docker ps --format '{{.Names}}' | grep -q "litellm-litellm"; then
        echo -e "${GREEN}✓ LiteLLM API is running${NC}"
        litellm_api_running=true
    else
        echo -e "${RED}✗ LiteLLM API is not running${NC}"
    fi
    
    if docker ps --format '{{.Names}}' | grep -q "litellm-db"; then
        echo -e "${GREEN}✓ Database is running${NC}"
        db_running=true
    else
        echo -e "${RED}✗ Database is not running${NC}"
    fi
    
    if docker ps --format '{{.Names}}' | grep -q "litellm-prometheus"; then
        echo -e "${GREEN}✓ Prometheus is running${NC}"
        prometheus_running=true
    else
        echo -e "${RED}✗ Prometheus is not running${NC}"
    fi
    
    # Check .env file
    echo -e "${CYAN}Checking .env file:${NC}"
    if [ -f "/opt/litellm/.env" ]; then
        echo -e "${GREEN}✓ .env file exists${NC}"
        
        # Check if required keys are present
        if grep -q "LITELLM_MASTER_KEY" "/opt/litellm/.env" && grep -q "LITELLM_SALT_KEY" "/opt/litellm/.env"; then
            echo -e "${GREEN}✓ Required environment variables found${NC}"
        else
            echo -e "${RED}✗ Missing required environment variables in .env file${NC}"
            echo -e "${YELLOW}  The .env file should contain LITELLM_MASTER_KEY and LITELLM_SALT_KEY${NC}"
        fi
    else
        echo -e "${RED}✗ .env file is missing${NC}"
    fi
    
    # If the API is not running but the database is, this might indicate a configuration issue
    if [ "$db_running" = true ] && [ "$litellm_api_running" = false ]; then
        echo -e "${YELLOW}The database is running but the API is not. This might indicate a configuration issue.${NC}"
        echo -e "${YELLOW}Trying to start the LiteLLM API...${NC}"
        cd /opt/litellm
        docker compose up -d
    fi
    
    # Check for 'NoneType' object is not iterable error
    if [ "$litellm_api_running" = true ]; then
        echo -e "${CYAN}Checking for common errors in LiteLLM API logs:${NC}"
        if docker logs $(docker ps -aqf "name=litellm-litellm") 2>&1 | grep -q "NoneType.*not iterable"; then
            echo -e "${RED}✗ Found 'NoneType object is not iterable' error${NC}"
            echo -e "${YELLOW}This error typically occurs when:${NC}"
            echo -e "${YELLOW}1. The database connection is failing${NC}"
            echo -e "${YELLOW}2. LiteLLM is missing required configuration${NC}"
            echo -e "${YELLOW}3. There's a compatibility issue with the current version${NC}"
            
            echo -e "${CYAN}Recommended actions:${NC}"
            echo -e "${CYAN}1. Verify database connection by checking the database logs${NC}"
            echo -e "${CYAN}2. Restart all containers${NC}"
            echo -e "${CYAN}3. Ensure .env file has the correct configuration${NC}"
            echo -e "${CYAN}4. Try reinstalling LiteLLM${NC}"
            
            echo -e "${YELLOW}Would you like to try a basic fix by restarting all containers? (y/n): ${NC}"
            read -r restart_choice
            if [[ $restart_choice =~ ^[Yy]$ ]]; then
                cd /opt/litellm
                docker compose down
                sleep 2
                docker compose up -d
                echo -e "${GREEN}LiteLLM has been restarted. Please check if the issue is resolved.${NC}"
            fi
        else
            echo -e "${GREEN}✓ No 'NoneType object is not iterable' error found in recent logs${NC}"
        fi
    fi
    
    echo -e "${GREEN}========================================${NC}"
    echo -e "${CYAN}Troubleshooting completed. For detailed logs, use the 'check_litellm_logs' function.${NC}"
}

# Remove LiteLLM
remove_litellm() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Check if LiteLLM directory exists
    if [ ! -d "/opt/litellm" ]; then
        echo -e "${RED}LiteLLM is not installed.${NC}"
        return 1
    fi
    
    # Confirm removal
    read -rp "$(echo -e ${YELLOW}"Do you want to remove LiteLLM? This will remove all containers and data. (y/n): "${NC})" choice
    if [[ ! $choice =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Operation cancelled.${NC}"
        return 0
    fi
    
    # Ask about volumes
    read -rp "$(echo -e ${YELLOW}"Do you want to remove volumes as well? This will delete all LiteLLM data. (y/n): "${NC})" choice_volumes
    
    # Stop and remove containers
    echo -e "${YELLOW}Removing LiteLLM...${NC}"
    cd /opt/litellm
    
    if [[ $choice_volumes =~ ^[Yy]$ ]]; then
        docker compose down -v
    else
        docker compose down
    fi
    
    if [ $? -eq 0 ]; then
        # Remove directory
        cd /
        sudo rm -rf /opt/litellm
        echo -e "${GREEN}LiteLLM removed successfully!${NC}"

        # Check if OpenWebUI is installed and ask if user wants to remove it
        if is_openwebui_installed; then
            echo -e "${YELLOW}OpenWebUI is also installed.${NC}"
            read -rp "$(echo -e ${YELLOW}"Would you like to remove OpenWebUI as well? (y/n): "${NC})" choice
            if [[ $choice =~ ^[Yy]$ ]]; then
                remove_openwebui
            else
                echo -e "${GREEN}OpenWebUI will be preserved.${NC}"
            fi
        fi
    else
        echo -e "${RED}Failed to remove LiteLLM containers.${NC}"
        return 1
    fi
}

# Fix the NoneType token validation issue
fix_litellm_token_issue() {
    echo -e "${CYAN}Fixing LiteLLM token and database issues...${NC}"
    
    # Check if LiteLLM directory exists
    if [ ! -d "/opt/litellm" ]; then
        echo -e "${RED}LiteLLM is not installed.${NC}"
        return 1
    fi
    
    # Create backup of existing .env file
    if [ -f "/opt/litellm/.env" ]; then
        sudo cp "/opt/litellm/.env" "/opt/litellm/.env.backup"
        echo -e "${GREEN}Created backup of existing .env file at /opt/litellm/.env.backup${NC}"
    fi
    
    # Update the .env file with tokens that bypass validation and proper DB settings
    echo -e "${GREEN}Updating .env file to fix token validation issue and database connection...${NC}"
    cat > /tmp/.env.new << 'EOF'
# API key settings with Bearer prefix to bypass validation
LITELLM_MASTER_KEY=Bearer_fazee
LITELLM_SALT_KEY=Bearer_fazee_salt

# Ensure database settings are correct
DATABASE_URL=**********************************************/litellm
STORE_MODEL_IN_DB=True

# Additional settings to prevent issues
UI_USERNAME=admin
UI_PASSWORD=admin123

# Disable problematic callbacks
# SERVICE_CALLBACK=[]
EOF

    sudo mv /tmp/.env.new /opt/litellm/.env
    sudo chown $(id -u):$(id -g) /opt/litellm/.env
    sudo chmod 644 /opt/litellm/.env
    
    # Check if we need to modify config.yaml
    if [ -f "/opt/litellm/config.yaml" ]; then
        echo -e "${GREEN}Backing up and updating config.yaml...${NC}"
        sudo cp "/opt/litellm/config.yaml" "/opt/litellm/config.yaml.backup"
        
        # Create a fixed config that removes problematic callbacks
        cat > /tmp/config.yaml << 'EOF'
# Remove problematic callbacks as mentioned in the GitHub issue
general_settings:
  # service_callback: ["prometheus_system"]  # commented out
  # success_callback: ["prometheus"]  # commented out
  # failure_callback: ["prometheus"]  # commented out
  telemetry: false
  # Other settings from your original config can be added here
EOF

        sudo mv /tmp/config.yaml /opt/litellm/config.yaml
        sudo chown $(id -u):$(id -g) /opt/litellm/config.yaml
        sudo chmod 644 /opt/litellm/config.yaml
    fi
    
    # Try to ensure database is properly initialized
    echo -e "${YELLOW}Ensuring database is properly initialized...${NC}"
    
    # Restart containers with complete teardown to ensure clean state
    echo -e "${YELLOW}Restarting LiteLLM with a complete reset...${NC}"
    cd /opt/litellm
    
    # Stop all containers
    docker compose down -v
    
    # Wait to ensure everything is properly stopped
    sleep 5
    
    # Start everything again
    docker compose up -d
    
    # Wait for containers to start up properly
    echo -e "${YELLOW}Waiting for containers to initialize...${NC}"
    sleep 15
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}LiteLLM issues fixed and services restarted!${NC}"
        echo -e "${GREEN}========================================${NC}"
        echo -e "${GREEN}The following changes were made:${NC}"
        echo -e "${GREEN}1. Updated tokens to bypass validation issues${NC}"
        echo -e "${GREEN}2. Fixed database connection settings${NC}"
        echo -e "${GREEN}3. Removed problematic callbacks${NC}"
        echo -e "${GREEN}4. Completely reinitialized the database${NC}"
        echo -e "${GREEN}========================================${NC}"
        echo -e "${GREEN}If the issue persists, you may need to update LiteLLM to a newer version.${NC}"
    else
        echo -e "${RED}Failed to restart LiteLLM.${NC}"
        return 1
    fi
}

# Update LiteLLM to the latest stable version
update_litellm_version() {
    echo -e "${CYAN}Updating LiteLLM to the latest stable version...${NC}"
    
    # Check if LiteLLM directory exists
    if [ ! -d "/opt/litellm" ]; then
        echo -e "${RED}LiteLLM is not installed.${NC}"
        return 1
    fi
    
    # Stop and completely remove current LiteLLM installation
    echo -e "${YELLOW}Stopping and removing current LiteLLM installation...${NC}"
    cd /opt/litellm
    docker compose down -v
    
    # Remove all files except .env which we'll need to preserve user settings
    if [ -f "/opt/litellm/.env" ]; then
        sudo cp "/opt/litellm/.env" "/tmp/litellm_env_backup"
        echo -e "${GREEN}Backed up .env file${NC}"
    fi
    
    # Go to parent directory before removing litellm folder
    cd /opt
    sudo rm -rf /opt/litellm
    echo -e "${GREEN}Removed old LiteLLM installation${NC}"
    
    # Create fresh LiteLLM directory
    sudo mkdir -p /opt/litellm
    sudo chown $(id -u):$(id -g) /opt/litellm
    cd /opt/litellm
    
    # Restore .env file if it existed
    if [ -f "/tmp/litellm_env_backup" ]; then
        sudo mv "/tmp/litellm_env_backup" "/opt/litellm/.env"
        echo -e "${GREEN}Restored .env file${NC}"
    else
        # Create new .env file if none existed
        echo -e "${GREEN}Creating new .env file...${NC}"
        cat > /tmp/.env.new << 'EOF'
# API key settings with Bearer prefix to bypass validation
LITELLM_MASTER_KEY=Bearer_fazee
LITELLM_SALT_KEY=Bearer_fazee_salt

# Ensure database settings are correct
DATABASE_URL=**************************************/litellm
STORE_MODEL_IN_DB=True

# Additional settings to prevent issues
UI_USERNAME=admin
UI_PASSWORD=admin123
EOF
        sudo mv /tmp/.env.new /opt/litellm/.env
    fi
    
    # Create docker-compose file with the latest version
    echo -e "${GREEN}Creating docker-compose file with the latest stable version...${NC}"
    cat > /tmp/docker-compose.yml << 'EOF'
version: '3'
services:
  litellm:
    image: ghcr.io/berriai/litellm:main-v1.63.8-nightly
    restart: always
    environment:
      - LITELLM_MASTER_KEY=Bearer_fazee
      - LITELLM_SALT_KEY=Bearer_fazee_salt
      - DATABASE_URL=**************************************/litellm
      - STORE_MODEL_IN_DB=True
      - UI_USERNAME=admin
      - UI_PASSWORD=admin123
    ports:
      - "4000:4000"
    networks:
      - my_network
    depends_on:
      - db
    volumes:
      - ./config.yaml:/app/config.yaml

  db:
    image: postgres:16-alpine
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=litellm
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - my_network
    expose:
      - 5432

  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    networks:
      - my_network
    restart: always

networks:
  my_network:
    external: true

volumes:
  postgres_data:
EOF

    sudo mv /tmp/docker-compose.yml /opt/litellm/docker-compose.yml
    sudo chmod 644 /opt/litellm/docker-compose.yml
    
    # Create config file without problematic callbacks
    echo -e "${GREEN}Creating updated config.yaml...${NC}"
    cat > /tmp/config.yaml << 'EOF'
# Clean config without problematic callbacks
general_settings:
  telemetry: false
  
model_list:
  - model_name: gpt-3.5-turbo
    litellm_params:
      model: gpt-3.5-turbo
  - model_name: gpt-4
    litellm_params:
      model: gpt-4
EOF

    sudo mv /tmp/config.yaml /opt/litellm/config.yaml
    sudo chmod 644 /opt/litellm/config.yaml
    
    # Create prometheus.yml file
    echo -e "${GREEN}Creating prometheus.yml...${NC}"
    cat > /tmp/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'litellm'
    static_configs:
      - targets: ['litellm:4000']
EOF

    sudo mv /tmp/prometheus.yml /opt/litellm/prometheus.yml
    sudo chmod 644 /opt/litellm/prometheus.yml
    
    # Start with new configuration
    echo -e "${YELLOW}Starting LiteLLM with the latest version...${NC}"
    cd /opt/litellm
    docker compose up -d
    
    # Wait for containers to start
    echo -e "${YELLOW}Waiting for containers to initialize...${NC}"
    sleep 15
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}LiteLLM updated to version main-v1.63.8-nightly successfully!${NC}"
        echo -e "${GREEN}========================================${NC}"
        echo -e "${GREEN}A clean installation was performed:${NC}"
        echo -e "${GREEN}1. Removed all old LiteLLM files and containers${NC}"
        echo -e "${GREEN}2. Installed latest version main-v1.63.8-nightly${NC}"
        echo -e "${GREEN}3. Created proper Docker Compose configuration${NC}"
        echo -e "${GREEN}4. Set up database with clean configuration${NC}"
        echo -e "${GREEN}========================================${NC}"
        echo -e "${GREEN}Access LiteLLM at: http://localhost:4000${NC}"
    else
        echo -e "${RED}Failed to update LiteLLM.${NC}"
        return 1
    fi
}

# Function to check if LiteLLM is installed
is_litellm_installed() {
    # Check if LiteLLM directory exists and if the docker containers exist
    if [ -d "/opt/litellm" ] && docker ps -a --format "{{.Names}}" | grep -q "^litellm"; then
        return 0  # True, LiteLLM is installed
    else
        return 1  # False, LiteLLM is not installed
    fi
}

# Backup LiteLLM data
backup_litellm() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Check if LiteLLM is installed
    if ! is_litellm_installed; then
        echo -e "${RED}LiteLLM is not installed.${NC}"
        return 1
    fi
    
    # Ensure rclone is installed
    install_rclone
    
    echo -e "${GREEN}Starting LiteLLM backup...${NC}"
    
    # Create temporary backup directory
    local backup_dir="/tmp/litellm_backup"
    mkdir -p "$backup_dir"
    
    # Use a fixed name for the backup file
    local backup_file="litellm_backup_latest.tar.gz"
    
    # Stop LiteLLM containers
    echo -e "${YELLOW}Stopping LiteLLM containers...${NC}"
    cd /opt/litellm
    docker compose down
    
    # Backup configuration files
    echo -e "${YELLOW}Backing up configuration files...${NC}"
    cp -r /opt/litellm/* "$backup_dir/"
    
    # Start PostgreSQL container temporarily for backup
    echo -e "${YELLOW}Starting PostgreSQL container for backup...${NC}"
    docker run -d --name temp_postgres \
        -v litellm_postgres_data:/var/lib/postgresql/data \
        -e POSTGRES_USER=postgres \
        -e POSTGRES_PASSWORD=postgres \
        -e POSTGRES_DB=litellm \
        postgres:16-alpine
    
    # Wait for PostgreSQL to start
    echo -e "${YELLOW}Waiting for PostgreSQL to start...${NC}"
    sleep 10
    
    # Backup PostgreSQL database
    echo -e "${YELLOW}Backing up PostgreSQL database...${NC}"
    docker exec temp_postgres pg_dump -U postgres litellm > "$backup_dir/database_dump.sql"
    
    # Stop and remove temporary PostgreSQL container
    docker stop temp_postgres
    docker rm temp_postgres
    
    # Create tar archive of all backup data
    echo -e "${YELLOW}Creating backup archive...${NC}"
    cd "$backup_dir"
    tar czf "/tmp/$backup_file" ./*
    
    # Remove any existing backups in the cloud
    echo -e "${YELLOW}Removing old backups...${NC}"
    echo "Executing rclone command: rclone delete \"$RCLONE_REMOTE:/Backups/litellm/\" --include \"$backup_file\""
    rclone delete "$RCLONE_REMOTE:/Backups/litellm/" --include "$backup_file"
    
    # Upload to rclone
    echo -e "${YELLOW}Uploading backup to cloud storage...${NC}"
    echo "Executing rclone command: rclone copy \"/tmp/$backup_file\" \"$RCLONE_REMOTE:/Backups/litellm/\" --progress --timeout 24h"
    rclone copy "/tmp/$backup_file" "$RCLONE_REMOTE:/Backups/litellm/" --progress --timeout 24h
    
    # Clean up
    rm -rf "$backup_dir"
    rm "/tmp/$backup_file"
    
    # Start LiteLLM containers
    echo -e "${YELLOW}Starting LiteLLM containers...${NC}"
    cd /opt/litellm
    docker compose up -d
    
    echo -e "${GREEN}LiteLLM backup completed successfully!${NC}"
}

# Restore LiteLLM data
restore_litellm() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Ensure rclone is installed
    install_rclone
    
    echo -e "${GREEN}Starting LiteLLM restore...${NC}"
    
    # Create temporary restore directory
    local restore_dir="/tmp/litellm_restore"
    mkdir -p "$restore_dir"
    
    # Fixed backup filename
    local backup_file="litellm_backup_latest.tar.gz"
    
    # Download backup
    echo -e "${YELLOW}Downloading backup from cloud storage...${NC}"
    echo "Executing rclone command: rclone copy \"$RCLONE_REMOTE:/Backups/litellm/$backup_file\" \"$restore_dir/\" --progress"
    rclone copy "$RCLONE_REMOTE:/Backups/litellm/$backup_file" "$restore_dir/" --progress
    
    if [ ! -f "$restore_dir/$backup_file" ]; then
        echo -e "${RED}Failed to download backup file.${NC}"
        rm -rf "$restore_dir"
        return 1
    fi
    
    # Extract backup
    echo -e "${YELLOW}Extracting backup...${NC}"
    cd "$restore_dir"
    tar xzf "$backup_file"
    
    # Stop and remove existing LiteLLM installation
    if is_litellm_installed; then
        echo -e "${YELLOW}Stopping and removing existing LiteLLM installation...${NC}"
        cd /opt/litellm
        docker compose down -v
        cd /opt
        sudo rm -rf /opt/litellm
    fi
    
    # Create fresh LiteLLM directory
    sudo mkdir -p /opt/litellm
    sudo chown $(id -u):$(id -g) /opt/litellm
    
    # Restore configuration files
    echo -e "${YELLOW}Restoring configuration files...${NC}"
    cp -r "$restore_dir"/* /opt/litellm/
    
    # Start PostgreSQL container
    echo -e "${YELLOW}Starting PostgreSQL container...${NC}"
    cd /opt/litellm
    docker compose up -d db
    
    # Wait for PostgreSQL to start
    echo -e "${YELLOW}Waiting for PostgreSQL to start...${NC}"
    sleep 15
    
    # Restore database
    echo -e "${YELLOW}Restoring database...${NC}"
    if [ -f "$restore_dir/database_dump.sql" ]; then
        docker compose exec -T db psql -U postgres -d litellm < "$restore_dir/database_dump.sql"
    else
        echo -e "${RED}Database dump not found in backup!${NC}"
    fi
    
    # Start remaining services
    echo -e "${YELLOW}Starting remaining services...${NC}"
    docker compose up -d
    
    # Clean up
    rm -rf "$restore_dir"
    
    echo -e "${GREEN}LiteLLM restore completed successfully!${NC}"
}
