install_sock5(){
    echo -e "${RED}╔═══════════════════════════════╗${NC}"
    echo -e "${RED}║     Installing SOCKS5         ║${NC}"
    echo -e "${RED}╚═══════════════════════════════╝${NC}"
    echo ""

    install_docker

    echo -e "${YELLOW}Pulling image from Docker Hub...${NC}"
    docker pull fazee6/socks5proxy:latest

    echo -e "${YELLOW}Checking if there is already a container with the name proxy...${NC}"
    stop_and_remove_container "proxy"

    echo ""

    while true; do
        read -rp "$(echo -e ${YELLOW}"Enter port to use with SOCKS5 proxy server: "${NC})" sock5_proxy_port

        # Call the get_valid_port function with the entered port number
        get_valid_port "$sock5_proxy_port"

        # Check the exit status of the get_valid_port function
        if [ $? -eq 0 ]; then
            break  # Break out of the loop when a valid port is entered
        fi
    done

    sudo docker run -d --name proxy \
        -p $sock5_proxy_port:8888 \
        -e SOCKS_PORT=8888 \
        $DOCKER_NETWORK \
        fazee6/socks5proxy

    docker update --restart unless-stopped proxy

    server_ip=$(get_public_ip)

    echo ""
    echo -e "${CYAN}╔════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║     SOCKS5 Proxy Server Info       ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║${NC} Server IP:  ${GREEN}$server_ip${NC}"
    echo -e "${CYAN}║${NC} Port:       ${GREEN}$sock5_proxy_port${NC}"
    echo -e "${CYAN}╚════════════════════════════════════╝${NC}"
    echo ""
}