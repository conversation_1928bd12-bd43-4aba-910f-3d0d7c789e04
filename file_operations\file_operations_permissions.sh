#!/bin/bash

# Permission and Ownership Management Functions
# Contains functions for changing file/folder permissions and ownership.

# Function to convert human readable to octal permissions
convert_to_octal() {
    local human="$1"
    # Ensure input is exactly 9 characters long
    if [ ${#human} -ne 9 ]; then return 1; fi

    local owner="${human:0:3}"
    local group="${human:3:3}"
    local others="${human:6:3}"

    local octal=""
    for perm_set in "$owner" "$group" "$others"; do
        local num=0
        # Validate characters in the set
        if [[ ! "$perm_set" =~ ^[rwx-]{3}$ ]]; then return 1; fi
        # Calculate octal value
        [[ $perm_set == *"r"* ]] && ((num+=4))
        [[ $perm_set == *"w"* ]] && ((num+=2))
        [[ $perm_set == *"x"* ]] && ((num+=1))
        octal+="$num"
    done
    echo "$octal"
    return 0
}

# Function to explain permissions
explain_permissions() {
    echo -e "${BLUE}Permission Examples:${NC}"
    echo "  Octal | Symbolic  | Description"
    echo "  ------|-----------|---------------------------------------------------------"
    echo "  777   | rwxrwxrwx | Full permissions for Owner, Group, and Others."
    echo "  755   | rwxr-xr-x | Owner: R/W/X, Group: R/X, Others: R/X (Common for dirs)."
    echo "  644   | rw-r--r-- | Owner: R/W, Group: R, Others: R (Common for files)."
    echo "  664   | rw-rw-r-- | Owner: R/W, Group: R/W, Others: R."
    echo "  700   | rwx------ | Owner: R/W/X, Group: None, Others: None (Private)."
    echo "  ---   | ---       | R=Read, W=Write, X=Execute"
}

# Function to validate and process permissions input
process_permissions() {
    local input="$1"

    # Check if input is octal format (3 digits, 0-7)
    if [[ "$input" =~ ^[0-7]{3}$ ]]; then
        echo "$input"
        return 0
    fi

    # Check if input is human readable format (9 chars, rwx-)
    if [[ "$input" =~ ^[rwx-]{9}$ ]]; then
        local octal_perm
        octal_perm=$(convert_to_octal "$input")
        if [ $? -eq 0 ]; then
            echo "$octal_perm"
            return 0
        fi
    fi

    # Invalid format
    echo -e "${RED}Invalid permission format. Use octal (e.g., 755) or symbolic (e.g., rwxr-xr-x).${NC}"
    return 1
}

# Function to display file/folder permissions in both formats
show_item_permissions() {
    local item="$1"
    if [ ! -e "$item" ]; then
        echo -e "${YELLOW}Item not found: $item${NC}"
        return
    fi
    local octal_perm=$(stat -c '%a' "$item")
    local human_perm=$(stat -c '%A' "$item") # %A includes file type prefix (d, -, l, etc.)
    local type_char=${human_perm:0:1}
    human_perm=${human_perm:1} # Remove the type character for consistency with input format

    local type_desc=""
    case $type_char in
      d) type_desc="(Directory)" ;;
      -) type_desc="(File)" ;;
      l) type_desc="(Symbolic Link)" ;;
      *) type_desc="(Other)" ;;
    esac

    echo -e "${BLUE}$item${NC} ${YELLOW}${type_desc}${NC}"
    echo -e "  Current permissions: ${GREEN}$human_perm ($octal_perm)${NC}"
}

# Modified function to change file permissions (handles single file or pattern in dir)
change_file_permissions() {
    echo -e "${YELLOW}Enter the path (file or directory):${NC}"
    read -r path

    # Check if path exists
    if [ ! -e "$path" ]; then
        echo -e "${RED}Invalid path: Path does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    local files_to_process=()
    local recursive="n" # Default for file operations

    if [ -f "$path" ]; then
        # Single file case
        files_to_process+=("$path")
        echo -e "${GREEN}Selected file:${NC}"
        show_item_permissions "$path"
    elif [ -d "$path" ]; then
        # Directory case - ask for pattern and recursion
        echo -e "${YELLOW}Enter file pattern within '$path' (e.g., *.txt, * for all files):${NC}"
        read -r pattern
        if ! validate_pattern "$pattern"; then # Validate the pattern itself
             read -p "Press Enter to continue..."
             return
        fi

        echo -e "${YELLOW}Search recursively in subdirectories? (y/n):${NC}"
        read -r recursive_input
        if [[ "$recursive_input" == "y" || "$recursive_input" == "Y" ]]; then
            recursive="y"
        fi

        # Find files matching the pattern
        echo -e "${GREEN}Searching for files matching '$pattern' in '$path' ${NC}"
        local find_cmd="find \"$path\""
        if [ "$recursive" != "y" ]; then
            find_cmd+=" -maxdepth 1"
        fi
        find_cmd+=" -type f -name \"$pattern\""

        # Use process substitution to read file paths safely
        while IFS= read -r file; do
            files_to_process+=("$file")
        done < <(eval $find_cmd)

        if [ ${#files_to_process[@]} -eq 0 ]; then
            echo -e "${YELLOW}No files found matching the pattern '$pattern'${NC}"
            read -p "Press Enter to continue..."
            return
        fi

        echo -e "${GREEN}Files that will be affected:${NC}"
         for file in "${files_to_process[@]}"; do
             show_item_permissions "$file"
         done
         echo -e "\n${YELLOW}Total files found: ${#files_to_process[@]}${NC}"

    else
        echo -e "${RED}Path is not a regular file or directory: $path${NC}"
        read -p "Press Enter to continue..."
        return
    fi


    explain_permissions
    echo -e "${YELLOW}Enter new permissions (e.g., 755 or rwxr-xr-x):${NC}"
    read -r perm_input

    local perms
    perms=$(process_permissions "$perm_input")
    if [ $? -ne 0 ]; then
        # Error message already printed by process_permissions
        read -p "Press Enter to continue..."
        return
    fi

    echo -e "${YELLOW}Proceed with changing permissions to $perms for the selected file(s)? (y/n)${NC}"
    read -r confirm

    if [[ "$confirm" == "y" || "$confirm" == "Y" ]]; then
        local changed_count=0
        local failed_count=0
        for file in "${files_to_process[@]}"; do
             if chmod "$perms" "$file"; then
                 echo -e "${GREEN}Changed permissions of $file to $perms${NC}"
                 show_item_permissions "$file" # Show updated permissions
                 ((changed_count++))
             else
                 echo -e "${RED}Failed to change permissions for $file${NC}"
                 ((failed_count++))
             fi
        done
        echo -e "${GREEN}Permission changes completed. ${YELLOW}Changed: $changed_count, Failed: $failed_count${NC}"
    else
        echo -e "${BLUE}Operation cancelled${NC}"
    fi

    read -p "Press Enter to continue..."
}


# Function to change folder permissions (single folder, optional recursion)
change_folder_permissions() {
    echo -e "${YELLOW}Enter the folder path:${NC}"
    read -r path
    path="${path%/}" # Remove trailing slash

    if [ ! -d "$path" ]; then
        echo -e "${RED}Invalid path: Directory does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    # Show current permissions for the folder
    echo -e "${GREEN}Selected folder:${NC}"
    show_item_permissions "$path"

    explain_permissions
    echo -e "${YELLOW}Enter new permissions for the folder itself (e.g., 755 or rwxr-xr-x):${NC}"
    read -r perm_input

    local perms
    perms=$(process_permissions "$perm_input")
    if [ $? -ne 0 ]; then
        read -p "Press Enter to continue..."
        return
    fi

    echo -e "${YELLOW}Apply permissions recursively to all contents (files and subfolders)? (y/n):${NC}"
    read -r recursive_input
    local recursive="n"
     if [[ "$recursive_input" == "y" || "$recursive_input" == "Y" ]]; then
        recursive="y"
        echo -e "${YELLOW}Enter permissions for files within the folder (e.g., 644 or rw-r--r--):${NC}"
        read -r file_perm_input
        local file_perms
        file_perms=$(process_permissions "$file_perm_input")
         if [ $? -ne 0 ]; then
             read -p "Press Enter to continue..."
             return
         fi

         echo -e "${YELLOW}Enter permissions for subdirectories within the folder (e.g., 755 or rwxr-xr-x):${NC}"
         read -r dir_perm_input
         local dir_perms
         dir_perms=$(process_permissions "$dir_perm_input")
          if [ $? -ne 0 ]; then
              read -p "Press Enter to continue..."
              return
          fi
     fi

    echo -e "${YELLOW}Preview of changes:${NC}"
    echo "Folder '$path' will be set to: $perms"
    if [ "$recursive" = "y" ]; then
        echo "Files inside '$path' will be set to: $file_perms"
        echo "Subdirectories inside '$path' will be set to: $dir_perms"
        echo -e "${BLUE}(Listing all affected items can be lengthy, showing summary only)${NC}"
        # Optionally, list a few items for preview:
        # find "$path" -maxdepth 2 \( -type d -o -type f \) | head -n 5 | while read -r item; do show_item_permissions "$item"; done
    fi

    echo -e "${YELLOW}Proceed with permission change? (y/n)${NC}"
    read -r confirm

    if [[ "$confirm" == "y" || "$confirm" == "Y" ]]; then
        local change_failed=0
        echo -e "${BLUE}Applying permissions...${NC}"
        # Apply to the top-level directory first
        if ! chmod "$perms" "$path"; then
            echo -e "${RED}Failed to change permissions for top-level folder $path${NC}"
            change_failed=1
        else
             echo -e "${GREEN}Changed permissions of folder $path to $perms${NC}"
        fi

        # Apply recursively if requested
        if [ "$recursive" = "y" ] && [ $change_failed -eq 0 ]; then
             echo -e "${BLUE}Applying permissions recursively...${NC}"
             # Apply permissions to files
             if find "$path" -type f -exec chmod "$file_perms" {} +; then
                 echo -e "${GREEN}Applied file permissions ($file_perms) recursively.${NC}"
             else
                 echo -e "${RED}Failed to apply file permissions recursively.${NC}"
                 change_failed=1
             fi
             # Apply permissions to directories (excluding the top one already set)
             if find "$path" -mindepth 1 -type d -exec chmod "$dir_perms" {} +; then
                 echo -e "${GREEN}Applied subdirectory permissions ($dir_perms) recursively.${NC}"
             else
                 echo -e "${RED}Failed to apply subdirectory permissions recursively.${NC}"
                 change_failed=1
             fi
        fi

        if [ $change_failed -eq 0 ]; then
             echo -e "${GREEN}Permission changes completed successfully.${NC}"
        else
             echo -e "${RED}Some permission changes failed.${NC}"
        fi
         # Show updated permissions for the main folder
         echo -e "${BLUE}Updated permissions for $path:${NC}"
         show_item_permissions "$path"
    else
        echo -e "${BLUE}Operation cancelled${NC}"
    fi
    read -p "Press Enter to continue..."
}


# Function to change ownership (file or folder, optional recursion)
change_ownership() {
    echo -e "${YELLOW}Enter the path (file or folder):${NC}"
    read -r path

    if [ ! -e "$path" ]; then
        echo -e "${RED}Invalid path: Path does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    # Show current ownership
    echo -e "${GREEN}Current ownership for $path:${NC}"
    current_owner=$(stat -c '%U' "$path")
    current_group=$(stat -c '%G' "$path")
    echo -e "  Owner: ${BLUE}$current_owner${NC}"
    echo -e "  Group: ${BLUE}$current_group${NC}"

    echo -e "${YELLOW}Enter new owner (user):${NC}"
    read -r new_user
    echo -e "${YELLOW}Enter new group (press Enter to use '$new_user' group):${NC}"
    read -r new_group

    # If group is empty, use the same as user
    new_group=${new_group:-$new_user}

    # Validate user and group existence
    local valid_owner=0
    local valid_group=0
    if id "$new_user" >/dev/null 2>&1; then
        valid_owner=1
    else
        echo -e "${RED}Error: User '$new_user' does not exist.${NC}"
    fi

    if getent group "$new_group" >/dev/null 2>&1; then
        valid_group=1
    else
        echo -e "${RED}Error: Group '$new_group' does not exist.${NC}"
    fi

    if [ $valid_owner -eq 0 ] || [ $valid_group -eq 0 ]; then
        read -p "Press Enter to continue..."
        return
    fi

    local recursive="n"
    if [ -d "$path" ]; then
        echo -e "${YELLOW}Apply ownership recursively to all contents? (y/n):${NC}"
        read -r recursive_input
         if [[ "$recursive_input" == "y" || "$recursive_input" == "Y" ]]; then
            recursive="y"
         fi
    fi

    echo -e "${YELLOW}Preview of changes:${NC}"
    echo "Path '$path' ownership will be set to: ${BLUE}$new_user:$new_group${NC}"
    if [ "$recursive" = "y" ]; then
        echo "Ownership will be applied recursively to all contents."
        echo -e "${BLUE}(Listing all affected items can be lengthy, showing summary only)${NC}"
    fi

    echo -e "${YELLOW}Proceed with ownership change? (y/n)${NC}"
    read -r confirm

    if [[ "$confirm" == "y" || "$confirm" == "Y" ]]; then
        local chown_cmd="chown"
        local change_failed=0
        if [ "$recursive" = "y" ]; then
            chown_cmd+=" -R"
        fi

        echo -e "${BLUE}Applying ownership change...${NC}"
        if eval $chown_cmd "$new_user:$new_group" "\"$path\""; then
            echo -e "${GREEN}Ownership change successful for $path ${NC}"
             if [ "$recursive" = "y" ]; then echo -e "${GREEN}(and contents recursively)${NC}"; fi
             # Show updated ownership for the main path
             echo -e "${BLUE}Updated ownership for $path:${NC}"
             updated_owner=$(stat -c '%U' "$path")
             updated_group=$(stat -c '%G' "$path")
             echo -e "  Owner: ${BLUE}$updated_owner${NC}"
             echo -e "  Group: ${BLUE}$updated_group${NC}"
        else
            echo -e "${RED}Ownership change failed for $path ${NC}"
             if [ "$recursive" = "y" ]; then echo -e "${RED}(recursive operation might be incomplete)${NC}"; fi
            change_failed=1
        fi

    else
        echo -e "${BLUE}Operation cancelled${NC}"
    fi
    read -p "Press Enter to continue..."
}