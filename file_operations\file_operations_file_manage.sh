#!/bin/bash

# File Management Functions
# Contains functions for searching, copying, moving, and deleting files.

# Function to search files by extension
search_files_by_extension() {
    while true; do
        echo -e "${YELLOW}Enter the path to search in (. for current directory):${NC}"
        read -r search_path
        search_path="${search_path%/}"

        if ! validate_path "$search_path"; then
            read -p "Press Enter to try again..."
            continue
        fi

        if [ ! -d "$search_path" ]; then
            echo -e "${RED}Invalid path: Directory does not exist${NC}"
            read -p "Press Enter to try again..."
            continue
        fi
        break
    done

    echo -e "${YELLOW}Search recursively in subdirectories? (y/n):${NC}"
    read -r recursive

    echo -e "${GREEN}Available extensions in this path:${NC}"
    extensions=$(get_unique_extensions "$search_path" "$recursive")
    if [ -z "$extensions" ]; then
        echo -e "${YELLOW}No files with extensions found${NC}"
    else
        echo "$extensions"
    fi

    echo -e "${YELLOW}Enter file extension from above list (without dot):${NC}"
    read -r ext

    echo -e "${GREEN}Found files:${NC}"
    if [ "$recursive" = "y" ]; then
        find "$search_path" -type f -name "*.$ext" -print
    else
        find "$search_path" -maxdepth 1 -type f -name "*.$ext" -print
    fi
    read -p "Press Enter to continue..."
}

# Function to copy files by extension
copy_files_by_extension() {
    # Assume source_path and recursive are set by a calling menu/function
    # If not, they need to be prompted for here or passed as arguments.
    # Check if source_path is set, if not, prompt
    if [ -z "$source_path" ]; then
        echo -e "${YELLOW}Enter the source path to copy from:${NC}"
        read -r source_path
        source_path="${source_path%/}"
        if [ ! -d "$source_path" ]; then
            echo -e "${RED}Invalid source path: Directory does not exist${NC}"
            read -p "Press Enter to continue..."
            return
        fi
    fi

    # Check if recursive is set, if not, prompt
    if [ -z "$recursive" ]; then
        echo -e "${YELLOW}Copy recursively from subdirectories? (y/n):${NC}"
        read -r recursive
    fi

    echo -e "${GREEN}Available extensions in source path ($source_path):${NC}"
    extensions=$(get_unique_extensions "$source_path" "$recursive")
    if [ -z "$extensions" ]; then
        echo -e "${YELLOW}No files with extensions found in source path${NC}"
        read -p "Press Enter to continue..."
        return
    fi
    echo "$extensions"

    while true; do
        echo -e "${YELLOW}Enter file extension from above list (without dot):${NC}"
        read -r ext

        if ! validate_extension "$ext"; then
            read -p "Press Enter to try again..."
            continue
        fi
        break
    done

    echo -e "${YELLOW}Enter destination directory:${NC}"
    read -r dest_dir
    dest_dir="${dest_dir%/}"

    if [ ! -d "$dest_dir" ]; then
        echo -e "${YELLOW}Directory doesn't exist. Create it? (y/n)${NC}"
        read -r create_dir
        if [ "$create_dir" = "y" ]; then
            mkdir -p "$dest_dir"
        else
            return
        fi
    fi

    # Get absolute paths for comparison
    abs_source=$(readlink -f "$source_path")
    abs_dest=$(readlink -f "$dest_dir")

    # Show files that will be copied
    echo -e "${GREEN}Files to be copied:${NC}"
    if [ "$recursive" = "y" ]; then
        if [[ "$abs_dest" == "$abs_source"* ]]; then
            found_files=$(find "$source_path" -type f -name "*.$ext" -not -path "$dest_dir/*" -not -path "$dest_dir")
        else
            found_files=$(find "$source_path" -type f -name "*.$ext")
        fi
    else
        found_files=$(find "$source_path" -maxdepth 1 -type f -name "*.$ext")
    fi

    if [ -z "$found_files" ]; then
        echo -e "${YELLOW}No files found matching the pattern${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo "$found_files"
    echo -e "\n${YELLOW}Proceed with copy? (y/n)${NC}"
    read -r confirm

    if [ "$confirm" = "y" ]; then
        while IFS= read -r file; do
            cp "$file" "$dest_dir/"
            echo -e "${GREEN}Copied: $file -> $dest_dir/${NC}"
        done <<< "$found_files"
        echo -e "${GREEN}Files copied successfully${NC}"
    else
        echo -e "${BLUE}Operation cancelled${NC}"
    fi
    read -p "Press Enter to continue..."
}

# Function to move files by extension
move_files_by_extension() {
    echo -e "${YELLOW}Enter the source path to move from:${NC}"
    read -r source_path
    source_path="${source_path%/}"

    if [ ! -d "$source_path" ]; then
        echo -e "${RED}Invalid source path: Directory does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo -e "${YELLOW}Move recursively from subdirectories? (y/n):${NC}"
    read -r recursive

    echo -e "${GREEN}Available extensions in this path:${NC}"
    extensions=$(get_unique_extensions "$source_path" "$recursive")
    if [ -z "$extensions" ]; then
        echo -e "${YELLOW}No files with extensions found${NC}"
        read -p "Press Enter to continue..."
        return
    fi
    echo "$extensions"

    echo -e "${YELLOW}Enter file extension from above list (without dot):${NC}"
    read -r ext

    echo -e "${YELLOW}Enter destination directory:${NC}"
    read -r dest_dir
    dest_dir="${dest_dir%/}"

    if [ ! -d "$dest_dir" ]; then
        echo -e "${YELLOW}Directory doesn't exist. Create it? (y/n)${NC}"
        read -r create_dir
        if [ "$create_dir" = "y" ]; then
            mkdir -p "$dest_dir"
        else
            return
        fi
    fi

    # Get absolute paths for comparison
    abs_source=$(readlink -f "$source_path")
    abs_dest=$(readlink -f "$dest_dir")

    # Show files that will be moved
    echo -e "${GREEN}Files to be moved:${NC}"
    if [ "$recursive" = "y" ]; then
        if [[ "$abs_dest" == "$abs_source"* ]]; then
            found_files=$(find "$source_path" -type f -name "*.$ext" -not -path "$dest_dir/*" -not -path "$dest_dir")
        else
            found_files=$(find "$source_path" -type f -name "*.$ext")
        fi
    else
        found_files=$(find "$source_path" -maxdepth 1 -type f -name "*.$ext")
    fi

    if [ -z "$found_files" ]; then
        echo -e "${YELLOW}No files found matching the pattern${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo "$found_files"
    echo -e "\n${YELLOW}Proceed with move? (y/n)${NC}"
    read -r confirm

    if [ "$confirm" = "y" ]; then
        while IFS= read -r file; do
            mv "$file" "$dest_dir/"
            echo -e "${GREEN}Moved: $file -> $dest_dir/${NC}"
        done <<< "$found_files"
        echo -e "${GREEN}Files moved successfully${NC}"
    else
        echo -e "${BLUE}Operation cancelled${NC}"
    fi
    read -p "Press Enter to continue..."
}

# Enhanced function to search and delete files
search_delete_files() {
    echo -e "${YELLOW}Enter the path to search in:${NC}"
    read -r search_path
    search_path="${search_path%/}"  # Remove trailing slash if any

    if [ ! -d "$search_path" ]; then
        echo -e "${RED}Invalid path: Directory does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo -e "${YELLOW}Choose search criteria:${NC}"
    echo "1. By name pattern (including wildcards)"
    echo "2. By extension"
    echo "3. By complex pattern"
    read -r search_choice

    echo -e "${YELLOW}Search recursively in subdirectories? (y/n):${NC}"
    read -r recursive

    case $search_choice in
        1)  # Search by name pattern
            echo -e "${YELLOW}Enter file name pattern (wildcards allowed, e.g., test*.txt):${NC}"
            read -r pattern
            # Use find with -name and handle spaces/special characters
            if [ "$recursive" = "y" ]; then
                found_files=$(find "$search_path" -type f -name "*${pattern}*" -print0 | tr '\0' '\n')
            else
                found_files=$(find "$search_path" -maxdepth 1 -type f -name "*${pattern}*" -print0 | tr '\0' '\n')
            fi
            ;;
        2)  # Search by extension
            echo -e "${YELLOW}Enter file extension (without dot):${NC}"
            read -r ext
            if [ "$recursive" = "y" ]; then
                found_files=$(find "$search_path" -type f -name "*.${ext}" -print0 | tr '\0' '\n')
            else
                found_files=$(find "$search_path" -maxdepth 1 -type f -name "*.${ext}" -print0 | tr '\0' '\n')
            fi
            ;;
        3)  # Search by complex pattern
            echo -e "${YELLOW}Enter complex pattern (e.g., *2023*.pdf or *_backup*):${NC}"
            read -r pattern
            if [ "$recursive" = "y" ]; then
                found_files=$(find "$search_path" -type f -name "*${pattern}*" -print0 | tr '\0' '\n')
            else
                found_files=$(find "$search_path" -maxdepth 1 -type f -name "*${pattern}*" -print0 | tr '\0' '\n')
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            return
            ;;
    esac

    # Show found files
    echo -e "${GREEN}Found files:${NC}"
    if [ -z "$found_files" ]; then
        echo -e "${YELLOW}No files found matching the pattern${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo "$found_files"
    echo -e "\n${YELLOW}Total files found: $(echo "$found_files" | wc -l)${NC}"

    # Confirm deletion
    echo -e "${RED}Warning: Are you sure you want to delete these files?${NC}"
    echo -e "${YELLOW}Type 'yes' to confirm deletion:${NC}"
    read -r confirm

    if [ "$confirm" = "yes" ]; then
        while IFS= read -r file; do
            [ -n "$file" ] && rm -f "$file" && echo -e "${GREEN}Deleted: $file${NC}"
        done <<< "$found_files"
        echo -e "${GREEN}Files deleted successfully${NC}"
    else
        echo -e "${BLUE}Operation cancelled${NC}"
    fi
    read -p "Press Enter to continue..."
}