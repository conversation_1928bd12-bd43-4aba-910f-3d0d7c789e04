vpn_menu() {
    options=(
        "01. Install Wireguard"
        "02. Install Sock5"
        "03. Install Shadowsocks"
        "04. Install V2ray"
        "05. Install Xray"
        "06. Install 3x-ui Panel"
        "07. Install All Above"
        "08. Remove Wireguard"
        "09. Remove Sock5"
        "10. Remove Shadowsocks"
        "11. Remove V2Ray"
        "12. Remove Xray"
        "13. Remove 3x-ui Panel"
        "14. Remove All VPN/Proxy Containers And Images"
        "15. Print PC Config"
        "16. Create QR Code For Phone"
        "17. Return to Main Menu"
        "18. Exit"
    )

    while true; do
        create_menu "VPN Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1)  install_wireguard ;;
            2)  install_sock5 ;;
            3)  install_shadowsocks ;;
            4)  install_v2ray ;;
            5)  install_xray ;;
            6)  install_3x_ui ;;
            7)  install_all_vpns ;;
            8)  remove_wireguard ;;
            9)  remove_sock5 ;;
            10) remove_shadowsocks ;;
            11) remove_v2ray ;;
            12) remove_xray ;;
            13) remove_3x_ui ;;
            14) remove_all_vpns ;;
            15) show_pc_config ;;
            16) create_qrcode_for_phone ;;
            17) return ;;
            18) exit ;;
            *)  echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done 
}