# Certificate Module

This module provides functionality for generating, managing, and renewing SSL certificates using Let's Encrypt and Cloudflare DNS verification.

## Files

### get-cert.sh
Core functions for SSL certificate management.

**Key Functions:**
- `print_message()`: Prints formatted informational messages
- `print_error()`: Prints formatted error messages
- `prompt_with_default()`: Prompts for input with default values
- `check_certificate_exists()`: Checks if a certificate already exists
- `check_certificate_expiry()`: Checks if a certificate is expiring soon
- `setup_cloudflare_config()`: Sets up Cloudflare DNS verification configuration
- `create_renewal_script()`: Creates scripts for automatic certificate renewal
- `sync_certificates_to_remote()`: Syncs certificates to remote storage
- `copy_to_nginx_proxy()`: Copies certificates to Nginx proxy container
- `check_rclone_certificates()`: Checks for certificates in remote storage
- `generate_ssl_certificate()`: Generates a new SSL certificate
- `setup_renewal_script()`: Sets up automatic renewal for certificates
- `restore_certificate_from_remote()`: Restores certificates from remote backup
- `backup_certificate_to_remote()`: Backs up certificates to remote storage

### cert_menu.sh
Provides menu interface for certificate operations.

**Key Functions:**
- `certificate_menu()`: Main menu for certificate operations

## Usage

The Certificate module provides functionality for managing SSL certificates for secure HTTPS connections.

To access Certificate functionality:
1. Select "Certificate Menu" from the main menu
2. Choose from generation, renewal, backup, or restoration options
3. Follow the interactive prompts to manage your certificates

## Integration with Other Modules

The Certificate module integrates with:
- Rclone module for remote certificate storage and synchronization
- Docker module for container-based certificate usage
- File Operations module for certificate file management
- Various service modules that require SSL certificates for secure connections 