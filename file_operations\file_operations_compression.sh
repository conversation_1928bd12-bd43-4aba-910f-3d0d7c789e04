#!/bin/bash

# Compression and Decompression Functions
# Contains functions for compressing items and decompressing archives,
# along with helper functions for checking/installing required tools.

# Function to check and install required packages
install_package() {
    local package="$1"
    if ! command -v "$package" &> /dev/null; then
        echo -e "${YELLOW}$package is not installed. Installing...${NC}"
        if command -v apt &> /dev/null; then
            case "$package" in
                "rar"|"unrar")
                    # Special handling for rar/unrar on Ubuntu
                    if ! grep -q "^deb .*multiverse" /etc/apt/sources.list /etc/apt/sources.list.d/* 2>/dev/null; then
                        echo -e "${YELLOW}Enabling multiverse repository...${NC}"
                        sudo add-apt-repository multiverse -y
                    fi
                    sudo apt update
                    sudo apt install -y rar unrar
                    ;;
                *)
                    sudo apt update && sudo apt install -y "$package"
                    ;;
            esac
            # Verify installation
            if ! command -v "$package" &> /dev/null; then
                 # Handle cases like 'rar' package providing 'unrar' command
                 # Check if unrar command exists separately
                 if [[ "$package" == "rar" ]] && command -v "unrar" &> /dev/null; then
                     return 0 # Consider successful if unrar is available
                 fi
                 echo -e "${RED}Failed to install $package automatically.${NC}"
                 return 1
            fi
        else
            echo -e "${RED}This script requires Ubuntu/Debian system for automatic package installation.${NC}"
            echo -e "${YELLOW}Please install $package manually on your system.${NC}"
            return 1
        fi
    fi
    return 0
}

# Function to check and install pv (pipe viewer)
check_pv() {
    if ! command -v pv &> /dev/null; then
        echo -e "${YELLOW}Installing pv (pipe viewer) for progress bars...${NC}"
        if command -v apt &> /dev/null; then
            sudo apt update && sudo apt install -y pv
            if ! command -v pv &> /dev/null; then
                 echo -e "${RED}Failed to install pv automatically.${NC}"
                 return 1
            fi
        else
            echo -e "${RED}This script requires Ubuntu/Debian system for automatic package installation.${NC}"
            echo -e "${YELLOW}Please install pv manually on your system.${NC}"
            return 1
        fi
    fi
    return 0
}

# Function to check and install all required compression tools
check_compression_tools() {
    local tools=("tar" "gzip" "zip" "unzip" "rar" "unrar") # Added gzip
    local missing=()
    local install_failed=0

    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            # Special handling for rar/unrar as they might be provided by one package
            if [[ "$tool" == "rar" || "$tool" == "unrar" ]]; then
                 if ! command -v "rar" &> /dev/null && ! command -v "unrar" &> /dev/null; then
                     # Only add 'rar' to missing list, install_package handles both
                     if [[ ! " ${missing[@]} " =~ " rar " ]]; then
                         missing+=("rar")
                     fi
                 fi
            else
                 missing+=("$tool")
            fi
        fi
    done

    if [ ${#missing[@]} -gt 0 ]; then
        echo -e "${YELLOW}Some compression tools are missing. Attempting installation...${NC}"
        for package in "${missing[@]}"; do
            if ! install_package "$package"; then
                echo -e "${RED}Failed to install $package. Some operations may not work.${NC}"
                install_failed=1
                # Don't stop, try installing others
            fi
        done
        if [ $install_failed -eq 1 ]; then
             read -p "Press Enter to continue..."
        fi
    fi
    # Check for pv separately
    check_pv
}


# Enhanced function to compress items
compress_items() {
    echo -e "${YELLOW}Enter the base path:${NC}"
    read -r base_path
    base_path="${base_path%/}"

    if [ ! -d "$base_path" ]; then
        echo -e "${RED}Invalid path: Directory does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    # Show only first level folders in the path
    echo -e "${GREEN}Available folders in $base_path:${NC}"
    find "$base_path" -mindepth 1 -maxdepth 1 -type d | sed "s|$base_path/||"

    echo -e "\n${YELLOW}Enter folder name to compress (* for all folders in base path):${NC}"
    read -r folder_choice

    # Check and install required tools first
    check_compression_tools
    # Check pv specifically for progress bars
    local use_pv=0
    if command -v pv &> /dev/null; then
        use_pv=1
    fi


    echo -e "${YELLOW}Choose compression format:${NC}"
    echo "1. TAR.GZ (tar.gz)"
    echo "2. ZIP"
    echo "3. RAR"
    read -r format_choice

    local archive_name=""
    local source_path=""
    local archive_base_path="$base_path" # Default archive location

    if [ "$folder_choice" = "*" ]; then
        # Compress the entire base_path directory contents
        archive_name=$(basename "$base_path")_all_folders
        source_path="$base_path"
        # Items to compress are all items directly within base_path
        items_to_compress=$(find "$base_path" -mindepth 1 -maxdepth 1 -printf "%f\n")
        if [ -z "$items_to_compress" ]; then
             echo -e "${YELLOW}No items found directly within $base_path to compress.${NC}"
             read -p "Press Enter to continue..."
             return
        fi
    else
        # Compress a specific folder within base_path
        if [ ! -d "$base_path/$folder_choice" ]; then
            echo -e "${RED}Folder not found: $base_path/$folder_choice${NC}"
            read -p "Press Enter to continue..."
            return
        fi
        archive_name="$folder_choice"
        source_path="$base_path/$folder_choice"
        items_to_compress="$folder_choice" # Relative path for tar/zip/rar commands
    fi

    # Show items to be compressed
    echo -e "${GREEN}Content to be compressed:${NC}"
    if [ "$folder_choice" = "*" ]; then
        echo "All items in: $base_path ($(du -sh "$base_path" | cut -f1))"
    else
        echo "$source_path ($(du -sh "$source_path" | cut -f1))"
    fi

    echo -e "${YELLOW}Proceed with compression? (y/n)${NC}"
    read -r confirm

    if [ "$confirm" = "y" ]; then
        # Create archive in the same directory as the base path
        local archive_path="$archive_base_path/${archive_name}"
        local cmd_failed=0

        case $format_choice in
            1)  # TAR.GZ
                if ! command -v tar &> /dev/null || ! command -v gzip &> /dev/null; then
                    echo -e "${RED}Error: 'tar' or 'gzip' command not found. Cannot create tar.gz archive.${NC}"
                    cmd_failed=1
                else
                    archive_path="${archive_path}.tar.gz"
                    echo -e "${BLUE}Creating ${archive_path}...${NC}"
                    # Use relative paths for items within the archive
                    if [ "$folder_choice" = "*" ]; then
                        size=$(du -sb "$base_path" | cut -f1)
                        if [ $use_pv -eq 1 ]; then
                            (cd "$base_path" && tar -czf - $items_to_compress 2>/dev/null | pv -s $size > "$archive_path")
                        else
                            (cd "$base_path" && tar -czf "$archive_path" $items_to_compress)
                        fi
                    else
                        size=$(du -sb "$source_path" | cut -f1)
                         if [ $use_pv -eq 1 ]; then
                            (cd "$base_path" && tar -czf - "$folder_choice" 2>/dev/null | pv -s $size > "$archive_path")
                        else
                            (cd "$base_path" && tar -czf "$archive_path" "$folder_choice")
                        fi
                    fi
                    [ $? -ne 0 ] && cmd_failed=1
                fi
                ;;
            2)  # ZIP
                 if ! command -v zip &> /dev/null; then
                    echo -e "${RED}Error: 'zip' command not found. Cannot create zip archive.${NC}"
                    cmd_failed=1
                else
                    archive_path="${archive_path}.zip"
                    echo -e "${BLUE}Creating ${archive_path}...${NC}"
                    # zip handles recursion with -r
                    if [ "$folder_choice" = "*" ]; then
                         # Zip contents of base_path, not base_path itself
                         (cd "$base_path" && zip -qr "$archive_path" $items_to_compress)
                    else
                         (cd "$base_path" && zip -qr "$archive_path" "$folder_choice")
                    fi
                     [ $? -ne 0 ] && cmd_failed=1
                 fi
                ;;
            3)  # RAR
                 if ! command -v rar &> /dev/null; then
                    echo -e "${RED}Error: 'rar' command not found. Cannot create rar archive.${NC}"
                    cmd_failed=1
                else
                    archive_path="${archive_path}.rar"
                    echo -e "${BLUE}Creating ${archive_path}...${NC}"
                    # rar handles recursion with -r
                     if [ "$folder_choice" = "*" ]; then
                         # Rar contents of base_path
                         (cd "$base_path" && rar a -r "$archive_path" $items_to_compress >/dev/null)
                    else
                         (cd "$base_path" && rar a -r "$archive_path" "$folder_choice" >/dev/null)
                    fi
                     [ $? -ne 0 ] && cmd_failed=1
                 fi
                ;;
            *)
                echo -e "${RED}Invalid option${NC}"
                cmd_failed=1
                ;;
        esac

        if [ $cmd_failed -eq 0 ]; then
            echo -e "${GREEN}Successfully created archive.${NC}"
            echo -e "${BLUE}Archive location: $archive_path${NC}"
        else
            echo -e "${RED}Compression failed.${NC}"
            # Attempt to remove partially created archive if it exists
            [ -f "$archive_path" ] && rm -f "$archive_path"
        fi
    else
         echo -e "${BLUE}Operation cancelled.${NC}"
    fi
    read -p "Press Enter to continue..."
}

# Non-interactive version of compress_items
# Usage: compress_items_noninteractive base_path folder_name format_choice output_path
# Example: compress_items_noninteractive /opt/adguardhome "config work" tar.gz /opt/adguard_backups/backup.tar.gz
compress_items_noninteractive() {
    local base_path="$1"
    local folder_choice="$2"
    local format="$3"
    local output_path="$4"
    
    base_path="${base_path%/}"
    
    # Validate parameters
    if [ -z "$base_path" ] || [ -z "$folder_choice" ] || [ -z "$format" ] || [ -z "$output_path" ]; then
        echo "Missing required parameters for compress_items_noninteractive"
        echo "Usage: compress_items_noninteractive base_path folder_name format output_path"
        return 1
    fi

    if [ ! -d "$base_path" ]; then
        echo "Invalid base path: Directory does not exist: $base_path"
        return 1
    fi
    
    # Check if the specified folders exist
    local all_folders_exist=true
    if [ "$folder_choice" != "*" ]; then
        for folder in $folder_choice; do
            if [ ! -d "$base_path/$folder" ] && [ ! -f "$base_path/$folder" ]; then
                echo "Item not found: $base_path/$folder"
                all_folders_exist=false
            fi
        done
    fi
    
    if [ "$all_folders_exist" = "false" ]; then
        return 1
    fi

    # Check and install required tools first
    check_compression_tools
    
    # Check pv specifically for progress bars
    local use_pv=0
    if command -v pv &> /dev/null; then
        use_pv=1
    fi

    # Extract directory from output_path
    local output_dir=$(dirname "$output_path")
    if [ ! -d "$output_dir" ]; then
        mkdir -p "$output_dir"
        if [ $? -ne 0 ]; then
            echo "Failed to create output directory: $output_dir"
            return 1
        fi
    fi

    # Create archive
    local cmd_failed=0
    local items_to_compress=""
    
    if [ "$folder_choice" = "*" ]; then
        # Compress all items in base_path
        items_to_compress=$(find "$base_path" -mindepth 1 -maxdepth 1 -printf "%f\n")
        if [ -z "$items_to_compress" ]; then
            echo "No items found directly within $base_path to compress."
            return 1
        fi
    else
        # Use the specified folders
        items_to_compress="$folder_choice"
    fi

    echo "Creating archive of: $items_to_compress from $base_path"
    echo "Output will be: $output_path"
    
    case $format in
        "tar.gz"|"tgz"|"1")  # TAR.GZ
            if ! command -v tar &> /dev/null || ! command -v gzip &> /dev/null; then
                echo "Error: 'tar' or 'gzip' command not found. Cannot create tar.gz archive."
                cmd_failed=1
            else
                # Ensure output has the right extension
                if [[ "$output_path" != *.tar.gz ]]; then
                    output_path="${output_path}.tar.gz"
                fi
                
                echo "Creating tar.gz archive: $output_path"
                # Use relative paths for items within the archive
                if [ "$folder_choice" = "*" ]; then
                    (cd "$base_path" && tar -czf "$output_path" $items_to_compress)
                else
                    (cd "$base_path" && tar -czf "$output_path" $items_to_compress)
                fi
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        "zip"|"2")  # ZIP
            if ! command -v zip &> /dev/null; then
                echo "Error: 'zip' command not found. Cannot create zip archive."
                cmd_failed=1
            else
                # Ensure output has the right extension
                if [[ "$output_path" != *.zip ]]; then
                    output_path="${output_path}.zip"
                fi
                
                echo "Creating zip archive: $output_path"
                # zip handles recursion with -r
                if [ "$folder_choice" = "*" ]; then
                    (cd "$base_path" && zip -qr "$output_path" $items_to_compress)
                else
                    (cd "$base_path" && zip -qr "$output_path" $items_to_compress)
                fi
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        "rar"|"3")  # RAR
            if ! command -v rar &> /dev/null; then
                echo "Error: 'rar' command not found. Cannot create rar archive."
                cmd_failed=1
            else
                # Ensure output has the right extension
                if [[ "$output_path" != *.rar ]]; then
                    output_path="${output_path}.rar"
                fi
                
                echo "Creating rar archive: $output_path"
                # rar handles recursion with -r
                if [ "$folder_choice" = "*" ]; then
                    (cd "$base_path" && rar a -r "$output_path" $items_to_compress >/dev/null)
                else
                    (cd "$base_path" && rar a -r "$output_path" $items_to_compress >/dev/null)
                fi
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        *)
            echo "Invalid format. Please use 'tar.gz', 'zip', or 'rar'."
            cmd_failed=1
            ;;
    esac

    if [ $cmd_failed -eq 0 ]; then
        echo "Successfully created archive: $output_path"
        return 0
    else
        echo "Compression failed."
        # Attempt to remove partially created archive if it exists
        [ -f "$output_path" ] && rm -f "$output_path"
        return 1
    fi
}

# Also update the decompress_archive function with a non-interactive version
decompress_archive_noninteractive() {
    local archive_path="$1"
    local dest_dir="$2"
    
    if [ -z "$archive_path" ] || [ -z "$dest_dir" ]; then
        echo "Missing required parameters for decompress_archive_noninteractive"
        echo "Usage: decompress_archive_noninteractive archive_path dest_dir"
        return 1
    fi
    
    if [ ! -f "$archive_path" ]; then
        echo "Invalid archive path: File does not exist: $archive_path"
        return 1
    fi
    
    if [ ! -d "$dest_dir" ]; then
        mkdir -p "$dest_dir"
        if [ $? -ne 0 ]; then
            echo "Failed to create destination directory: $dest_dir"
            return 1
        fi
    fi
    
    # Check and install required tools
    check_compression_tools
    
    local cmd_failed=0
    local archive_name=$(basename "$archive_path")
    
    echo "Decompressing: $archive_name to $dest_dir"
    
    case "$archive_name" in
        *.tar.gz|*.tgz)
            if ! command -v tar &> /dev/null || ! command -v gzip &> /dev/null; then
                echo "Error: 'tar' or 'gzip' command not found. Cannot decompress."
                cmd_failed=1
            else
                tar -xzf "$archive_path" -C "$dest_dir"
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        *.zip)
            if ! command -v unzip &> /dev/null; then
                echo "Error: 'unzip' command not found. Cannot decompress."
                cmd_failed=1
            else
                unzip -oq "$archive_path" -d "$dest_dir"
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        *.rar)
            if ! command -v unrar &> /dev/null; then
                echo "Error: 'unrar' command not found. Cannot decompress."
                cmd_failed=1
            else
                unrar x -o+ "$archive_path" "$dest_dir/" > /dev/null
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        *)
            echo "Unsupported archive format: $archive_name"
            cmd_failed=1
            ;;
    esac

    if [ $cmd_failed -eq 0 ]; then
        echo "Successfully decompressed $archive_name to $dest_dir"
        return 0
    else
        echo "Failed to decompress $archive_name"
        return 1
    fi
}

# Enhanced function to decompress archive(s)
decompress_archive() {
    echo -e "${YELLOW}Enter the base path containing archives:${NC}"
    read -r base_path
    base_path="${base_path%/}"

    if [ ! -d "$base_path" ]; then
        echo -e "${RED}Invalid path: Directory does not exist${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    # Check and install required tools first
    check_compression_tools
    # Check pv specifically for progress bars
    local use_pv=0
    if command -v pv &> /dev/null; then
        use_pv=1
    fi

    # Show only compressed files in the path
    echo -e "${GREEN}Available archives in $base_path:${NC}"
    found_archives=$(find "$base_path" -maxdepth 1 -type f \( -name "*.tar.gz" -o -name "*.tgz" -o -name "*.zip" -o -name "*.rar" \) -printf "%f\n")

    if [ -z "$found_archives" ]; then
        echo -e "${YELLOW}No archives found in this path${NC}"
        read -p "Press Enter to continue..."
        return
    fi

    echo "$found_archives"

    echo -e "\n${YELLOW}Enter archive name to decompress (* for all archives):${NC}"
    read -r archive_choice

    echo -e "${YELLOW}Decompress in the same location? (y/n):${NC}"
    read -r same_location

    local dest_dir=""
    if [ "$same_location" = "n" ]; then
        echo -e "${YELLOW}Enter destination directory:${NC}"
        read -r dest_dir
        dest_dir="${dest_dir%/}"

        if [ ! -d "$dest_dir" ]; then
            echo -e "${YELLOW}Directory doesn't exist. Create it? (y/n)${NC}"
            read -r create_dir
            if [ "$create_dir" = "y" ]; then
                mkdir -p "$dest_dir"
                if [ $? -ne 0 ]; then
                    echo -e "${RED}Failed to create destination directory.${NC}"
                    read -p "Press Enter to continue..."
                    return
                fi
            else
                echo -e "${BLUE}Operation cancelled${NC}"
                read -p "Press Enter to continue..."
                return
            fi
        fi
    else
        dest_dir="$base_path"
    fi

    # Function to decompress a single archive
    decompress_single_file() {
        local archive_name="$1" # Just the filename
        local dest="$2"
        local full_path="$base_path/$archive_name"
        local size=0
        local cmd_failed=0

        # Check if file exists before proceeding
        if [ ! -f "$full_path" ]; then
             echo -e "${RED}Error: Archive file not found: $full_path${NC}"
             return
        fi

        # Try to get size for pv
        if [ $use_pv -eq 1 ]; then
             size=$(stat -f%z "$full_path" 2>/dev/null || stat -c%s "$full_path" 2>/dev/null || echo 0)
        fi


        echo -e "${YELLOW}Processing: $archive_name${NC}"
        case "$archive_name" in
            *.tar.gz|*.tgz)
                 if ! command -v tar &> /dev/null || ! command -v gzip &> /dev/null; then
                    echo -e "${RED}Error: 'tar' or 'gzip' command not found. Cannot decompress.${NC}"
                    cmd_failed=1
                 else
                    if [ $use_pv -eq 1 ] && [ $size -gt 0 ]; then
                        pv -s $size "$full_path" | tar -xzf - -C "$dest"
                    else
                        tar -xzf "$full_path" -C "$dest"
                    fi
                     [ $? -ne 0 ] && cmd_failed=1
                 fi
                ;;
            *.zip)
                 if ! command -v unzip &> /dev/null; then
                    echo -e "${RED}Error: 'unzip' command not found. Cannot decompress.${NC}"
                    cmd_failed=1
                 else
                    # unzip has -o to overwrite existing files without prompting
                    unzip -oq "$full_path" -d "$dest"
                     [ $? -ne 0 ] && cmd_failed=1
                 fi
                ;;
            *.rar)
                 if ! command -v unrar &> /dev/null; then
                    echo -e "${RED}Error: 'unrar' command not found. Cannot decompress.${NC}"
                    cmd_failed=1
                 else
                    # unrar x extracts with full path, -o+ overwrites existing files
                    unrar x -o+ "$full_path" "$dest/" > /dev/null
                     [ $? -ne 0 ] && cmd_failed=1
                 fi
                ;;
            *)
                echo -e "${RED}Unsupported archive format: $archive_name${NC}"
                cmd_failed=1
                ;;
        esac

        if [ $cmd_failed -eq 0 ]; then
             echo -e "${GREEN}Successfully decompressed $archive_name to $dest${NC}"
        else
             echo -e "${RED}Failed to decompress $archive_name${NC}"
        fi
    }

    # Process archives based on user choice
    if [ "$archive_choice" = "*" ]; then
        echo -e "${GREEN}Decompressing all archives to $dest_dir${NC}"
        echo "$found_archives" | while IFS= read -r archive; do
            [ -n "$archive" ] && decompress_single_file "$archive" "$dest_dir"
        done
    else
        # Check if the chosen archive exists in the list
        local found=0
        echo "$found_archives" | while IFS= read -r archive; do
             if [ "$archive" = "$archive_choice" ]; then
                 found=1
                 break
             fi
        done

        if [ $found -eq 1 ]; then
            decompress_single_file "$archive_choice" "$dest_dir"
        else
            echo -e "${RED}Archive not found in the list: $archive_choice${NC}"
        fi
    fi

    read -p "Press Enter to continue..."
}