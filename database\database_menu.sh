#!/bin/bash

database_menu() {
    while true; do
        options=(
            "1. Create MySQL Container"
            "2. List MySQL Containers"
            "3. Remove MySQL Container"
            "4. Back to Main Menu"
        )

        create_menu "Database Menu" "${options[@]}"
        
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) create_mysql_container ;;
            2) list_mysql_containers ;;
            3) remove_mysql_container ;;
            4) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done
}
