#!/bin/bash

encrypt_folder() {
    # Check if openssl is installed
    if ! command -v openssl &> /dev/null; then
        echo -e "${RED}Error: openssl is not installed. Please install it first.${NC}"
        return 1
    fi

    # Ask for base path
    echo -e "${CYAN}Enter the base path to list folders:${NC}"
    read -r base_path

    # Check if base path exists
    if [ ! -d "$base_path" ]; then
        echo -e "${RED}Error: Base path does not exist!${NC}"
        return 1
    fi

    # List all directories in the base path
    echo -e "${CYAN}Available folders:${NC}"
    ls -d "$base_path"/*/ 2>/dev/null | nl

    # Ask which folder to encrypt
    echo -e "${YELLOW}Enter the number of the folder to encrypt:${NC}"
    read -r folder_number

    # Get the selected folder
    selected_folder=$(ls -d "$base_path"/*/ 2>/dev/null | sed -n "${folder_number}p")

    if [ -z "$selected_folder" ]; then
        echo -e "${RED}Invalid folder selection!${NC}"
        return 1
    fi

    # Create output folder name
    output_folder="${selected_folder%/}_encrypted"

    # Check if source folder exists
    if [ ! -d "$selected_folder" ]; then
        echo -e "${RED}Error: Source folder does not exist!${NC}"
        return 1
    fi

    # Create output folder if it doesn't exist
    mkdir -p "$output_folder"

    # Ask for encryption password
    echo -e "${CYAN}Enter encryption password:${NC}"
    read -s ENCRYPTION_KEY
    echo -e "${CYAN}Confirm encryption password:${NC}"
    read -s ENCRYPTION_KEY_CONFIRM

    if [ "$ENCRYPTION_KEY" != "$ENCRYPTION_KEY_CONFIRM" ]; then
        echo -e "${RED}Passwords do not match!${NC}"
        return 1
    fi

    # Function to encrypt a file
    encrypt_file() {
        local source="$1"
        local dest="$2"
        openssl enc -aes-256-cbc -salt -in "$source" -out "$dest" -k "$ENCRYPTION_KEY" 2>/dev/null
    }

    # Create the runner script
    cat > "$output_folder/run.sh" << 'EOL'
#!/bin/bash

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Ask for decryption password
echo "Enter decryption password:"
read -s KEY

# Function to decrypt and execute
decrypt_and_execute() {
    local encrypted_file="$1"
    local temp_dir="$SCRIPT_DIR/temp_decrypt"
    local base_name=$(basename "$encrypted_file" .enc)
    local temp_file="$temp_dir/$base_name"
    
    # Create temp directory with restricted permissions
    mkdir -p "$temp_dir"
    chmod 700 "$temp_dir"
    
    # Test password with the main script first
    if ! openssl enc -aes-256-cbc -d -salt -in "$encrypted_file" -k "$KEY" -out "$temp_file" 2>/dev/null; then
        echo "Error: Invalid password!"
        rm -rf "$temp_dir"
        exit 1
    fi
    
    if [ -x "$encrypted_file" ]; then
        chmod +x "$temp_file"
    fi
    
    # Export the temp directory path for sourced scripts to find other files
    export SCRIPT_TEMP_DIR="$temp_dir"
    
    # Decrypt all other files to the temp directory
    find "$SCRIPT_DIR" -type f -name "*.enc" | while read -r enc_file; do
        local rel_path=${enc_file#"$SCRIPT_DIR/"}
        local dec_path="$temp_dir/${rel_path%.enc}"
        mkdir -p "$(dirname "$dec_path")"
        openssl enc -aes-256-cbc -d -salt -in "$enc_file" -k "$KEY" -out "$dec_path" 2>/dev/null
        if [ -x "$enc_file" ]; then
            chmod +x "$dec_path"
        fi
    done
    
    # Change to the temp directory and execute main script
    cd "$temp_dir"
    if [ -x "$temp_file" ]; then
        "$temp_file" "$@"
    else
        source "$temp_file"
    fi
    
    # Secure cleanup
    find "$temp_dir" -type f -exec shred -u {} \;
    rm -rf "$temp_dir"
}

# Find and execute main.sh
MAIN_SCRIPT="$SCRIPT_DIR/main.sh.enc"
if [ -f "$MAIN_SCRIPT" ]; then
    decrypt_and_execute "$MAIN_SCRIPT" "$@"
else
    echo "Error: main.sh.enc not found!"
    exit 1
fi
EOL

    chmod +x "$output_folder/run.sh"

    # Encrypt all files in the source folder
    cd "$selected_folder"
    find . -type f ! -name ".*" | while read -r file; do
        # Create directory structure in output folder
        mkdir -p "$output_folder/$(dirname "$file")"
        
        # Encrypt the file
        encrypt_file "$file" "$output_folder/${file}.enc"
        
        # If the source file is executable, make the encrypted file executable too
        if [ -x "$file" ]; then
            chmod +x "$output_folder/${file}.enc"
        fi
    done

    echo -e "${GREEN}Encryption completed successfully!${NC}"
    echo -e "${CYAN}To run your encrypted script, use: $output_folder/run.sh${NC}"

    # Ask for confirmation before removing the original folder
    echo -e "${YELLOW}Do you want to remove the original folder? (y/N):${NC}"
    read -r remove_confirm

    if [[ $remove_confirm =~ ^[Yy]$ ]]; then
        # Remove the original folder
        rm -rf "$selected_folder"
        echo -e "${GREEN}Original folder removed successfully.${NC}"
    else
        echo -e "${CYAN}Original folder kept intact.${NC}"
    fi
}
