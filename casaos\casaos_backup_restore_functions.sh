#!/bin/bash

# Function to handle permissions
fix_app_permissions() {
    local app_name="$1"

    echo -e "${YELLOW}Setting permissions for $app_name...${NC}"

    if [ "$app_name" = "jellyfin" ]; then
        echo -e "${YELLOW}Setting special permissions for Jellyfin...${NC}"
        # Set ownership to root
        chown -R root:root "/DATA/AppData/$app_name"
        # Set base permissions
        chmod -R 755 "/DATA/AppData/$app_name"
        # Set more permissive permissions for data directory and its contents
        find "/DATA/AppData/$app_name/config" -type f -exec chmod 666 {} \;
        find "/DATA/AppData/$app_name/config" -type d -exec chmod 777 {} \;
        # Ensure database directory is writable
        if [ -d "/DATA/AppData/$app_name/config/data" ]; then
            chmod 777 "/DATA/AppData/$app_name/config/data"
            chmod 666 "/DATA/AppData/$app_name/config/data"/*.db* 2>/dev/null || true
        fi
    else
        # Default permissions for other apps
        chown -R root:root "/DATA/AppData/$app_name"
        chmod -R 755 "/DATA/AppData/$app_name"
    fi
}

# Function to handle restore selection
casaos_restore_menu() {
    if ! check_casaos_installed; then # Assumes check_casaos_installed is sourced
        echo -e "${RED}CasaOS is not installed.${NC}"
        read -rp "$(echo -e ${YELLOW}"Would you like to install CasaOS now? (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            install_casaos # Assumes install_casaos is sourced
            if ! check_casaos_installed; then
                return 1
            fi
        else
            return 1
        fi
    fi

    # Ensure YAML files are available
    if ! ensure_yaml_files; then # Assumes ensure_yaml_files is sourced
        return 1
    fi

    local yaml_dir="/tmp/casaos_yaml"

    while true; do
        # Get available backups and sort them in reverse chronological order
        local backups=($(rclone lsf "$RCLONE_REMOTEBackups/CasaOS/" | grep '\.tar\.gz$' | grep -v 'casaos_config_' | sort -r)) # Exclude config backups

        if [ ${#backups[@]} -eq 0 ]; then
            echo -e "${RED}No app backups found.${NC}"
            sleep 2
            return 0
        fi

        options=()
        for i in "${!backups[@]}"; do
            options+=("$((i+1)). ${backups[i]%%.tar.gz}")
        done
        options+=("$((${#backups[@]}+1)). Back")

        create_menu "Select Backup to Restore" "${options[@]}" # Assumes create_menu is sourced
        echo -e "${YELLOW}For multiple selections, enter numbers separated by spaces (e.g., 1 3 4)${NC}"
        read -rp "$(echo -e ${YELLOW}"Enter your choice(s): "${NC})" -a choices

        if [[ "${choices[0]}" == "$((${#backups[@]}+1))" ]]; then
            return 0
        fi

        # Process selected choices
        declare -A app_backups  # Associate array to store latest backup for each app
        local selected_backups=()

        for choice in "${choices[@]}"; do
            if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -le "${#backups[@]}" ]; then
                local backup="${backups[$((choice-1))]}"
                local app_name="${backup%%_[0-9]*}"  # Extract app name

                # Check if we already have a backup for this app
                if [[ -n "${app_backups[$app_name]}" ]]; then
                    echo -e "${YELLOW}Multiple backups selected for $app_name. Using the latest one.${NC}"
                    # Compare timestamps and keep the latest
                    if [[ "${app_backups[$app_name]}" < "$backup" ]]; then
                        app_backups[$app_name]="$backup"
                    fi
                else
                    app_backups[$app_name]="$backup"
                fi
            fi
        done

        # Convert associative array to list of unique backups
        for backup in "${app_backups[@]}"; do
            selected_backups+=("$backup")
        done

        if [ ${#selected_backups[@]} -eq 0 ]; then
            echo -e "${RED}No valid selections made.${NC}"
            continue
        fi

        # Process each selected backup
        for selected_backup in "${selected_backups[@]}"; do
            # Extract app name from backup filename before downloading
            local app_name="${selected_backup%%_[0-9]*}"  # Remove date/time part

            # Check if app is installed
            if ! is_app_installed "$app_name"; then # Assumes is_app_installed is sourced
                echo -e "${YELLOW}$app_name is not installed.${NC}"
                read -rp "$(echo -e ${YELLOW}"Would you like to install $app_name first? (y/n): "${NC})" install_choice
                if [[ $install_choice =~ ^[Yy]$ ]]; then
                    echo -e "${YELLOW}Installing $app_name...${NC}"
                    # Check if YAML file exists before attempting install
                    if [ ! -f "$yaml_dir/${app_name}.yaml" ]; then
                        echo -e "${RED}YAML file for $app_name not found in $yaml_dir. Cannot install.${NC}"
                        continue
                    fi
                    casaos-cli app-management install -f "$yaml_dir/${app_name}.yaml"
                    if ! wait_for_app_in_list "$app_name"; then # Assumes wait_for_app_in_list is sourced
                        echo -e "${RED}Installation command succeeded but app failed to appear in CasaOS list${NC}"
                        continue
                    fi
                    if ! wait_for_container_status "$app_name" "running"; then # Assumes wait_for_container_status is sourced
                        echo -e "${RED}Failed to install $app_name. Cannot proceed with restore.${NC}"
                        continue
                    fi
                else
                    echo -e "${RED}Cannot restore without installing the app first.${NC}"
                    continue
                fi
            fi

            # Download and restore backup
            local temp_dir="/tmp/casaos_restore"
            local archive_path="/tmp/$selected_backup"

            mkdir -p "$temp_dir"

            echo -e "${GREEN}Downloading backup...${NC}"
            echo "Executing rclone command: rclone copy \"$RCLONE_REMOTE:/Backups/CasaOS/$selected_backup\" \"/tmp\" --progress"
            rclone copy "$RCLONE_REMOTE:/Backups/CasaOS/$selected_backup" "/tmp" \
                --progress \
                --checkers 8 \
                --transfers 4 \
                --stats 1s

            if container_exists "$app_name"; then # Assumes container_exists is sourced
                echo -e "${YELLOW}Stopping $app_name using CasaOS CLI...${NC}"
                casaos-cli app-management stop "$app_name"
                echo -e "${YELLOW}Waiting 10 seconds for $app_name to stop completely...${NC}"
                sleep 10

                echo -e "${GREEN}Restoring $app_name...${NC}"
                if [ -d "/DATA/AppData/$app_name" ]; then
                    rm -rf "/DATA/AppData/$app_name"
                fi

                echo -e "${GREEN}Extracting $app_name...${NC}"
                cd /DATA/AppData && tar -xzf "/tmp/$selected_backup"

                # Fix permissions before starting
                fix_app_permissions "$app_name"

                echo -e "${YELLOW}Starting $app_name using CasaOS CLI...${NC}"
                casaos-cli app-management start "$app_name"
            else
                echo -e "${RED}Skipping $app_name: No container found${NC}"
            fi

            # Cleanup
            rm -f "/tmp/$selected_backup"
            rm -rf "$temp_dir"
            echo -e "${GREEN}Restore completed for $app_name${NC}"
        done
        # Final cleanup of any remaining temporary files
        rm -f /tmp/*.tar.gz
        rm -rf /tmp/casaos_restore
        echo -e "${GREEN}All restore operations completed successfully!${NC}"
        break
    done
}

# Create a function for copying with progress
copy_with_progress() {
    local source="$1"
    local dest="$2"
    local size=$(du -sb "$source" | cut -f1)

    # Ensure parent directory exists
    mkdir -p "$(dirname "$dest")"

    if [ -d "$source" ]; then
        # For directories, use rsync to maintain permissions and show progress
        rsync -a --info=progress2 "$source/" "$dest/"
    else
        # For single files
        pv -s "$size" "$source" > "$dest"
    fi

    # Verify the copy was successful
    if [ -d "$source" ] && [ ! -d "$dest" ]; then
        echo -e "${RED}Failed to copy directory $source to $dest${NC}"
        return 1
    elif [ -f "$source" ] && [ ! -f "$dest" ]; then
        echo -e "${RED}Failed to copy file $source to $dest${NC}"
        return 1
    fi
    return 0
}

# Function to check if a specific CasaOS app is installed
is_app_installed() {
    local app_name="$1"

    # First check if CasaOS service is responsive
    if ! check_casaos_service; then # Assumes check_casaos_service is sourced
        echo -e "${RED}Cannot check app installation status - CasaOS service is not responding${NC}"
        return 2
    fi

    if casaos-cli app-management list apps | grep -q "^$app_name[[:space:]]"; then
        return 0  # App is installed
    else
        return 1  # App is not installed
    fi
}

# Function to wait for container status
wait_for_container_status() {
    local container_name="$1"
    local desired_status="$2"  # "running" or "removed"
    local max_attempts=30
    local attempt=1

    echo -e "${YELLOW}Waiting for container $container_name to be $desired_status...${NC}"

    # If we're waiting for removal and container doesn't exist, return success immediately
    if [ "$desired_status" = "removed" ] && ! docker ps -a -q -f name="^${container_name}$" >/dev/null 2>&1; then
        echo -e "${GREEN}Container $container_name is already removed${NC}"
        return 0
    fi

    # If we're waiting for running and container doesn't exist, return failure immediately
    if [ "$desired_status" = "running" ] && ! docker ps -a -q -f name="^${container_name}$" >/dev/null 2>&1; then
        echo -e "${RED}Container $container_name does not exist${NC}"
        return 1
    fi

    while [ $attempt -le $max_attempts ]; do
        if [ "$desired_status" = "running" ]; then
            if docker ps -q -f name="^${container_name}$" >/dev/null 2>&1; then
                echo -e "${GREEN}Container $container_name is now running${NC}"
                return 0
            fi
        elif [ "$desired_status" = "removed" ]; then
            if ! docker ps -a -q -f name="^${container_name}$" >/dev/null 2>&1; then
                echo -e "${GREEN}Container $container_name has been removed${NC}"
                return 0
            fi
        fi

        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done

    echo -e "\n${RED}Timeout waiting for container $container_name to be $desired_status${NC}"
    return 1
}

# Function to wait for app to be uninstalled
wait_for_app_uninstalled() {
    local app_name="$1"
    local max_attempts=60  # 5 minutes maximum (5 seconds * 60)
    local attempt=1

    echo -e "${YELLOW}Waiting for $app_name to be uninstalled...${NC}"

    while [ $attempt -le $max_attempts ]; do
        if ! casaos-cli app-management list apps | grep -q "^$app_name[[:space:]]"; then
            echo -e "${GREEN}$app_name has been uninstalled${NC}"
            echo -e "${YELLOW}Waiting additional 5 seconds before continuing...${NC}"
            sleep 5
            return 0
        fi
        echo -n "."
        sleep 5
        attempt=$((attempt + 1))
    done

    echo -e "\n${RED}Timeout waiting for $app_name to be uninstalled${NC}"
    return 1
}

# Function to wait for app to appear in CasaOS app list
wait_for_app_in_list() {
    local app_name="$1"
    local max_attempts=60  # 5 minutes maximum (5 seconds * 60)
    local attempt=1

    echo -e "${YELLOW}Waiting for $app_name to appear in CasaOS app list...${NC}"

    while [ $attempt -le $max_attempts ]; do
        if casaos-cli app-management list apps | grep -q "^$app_name[[:space:]]"; then
            echo -e "${GREEN}$app_name found in CasaOS app list${NC}"
            echo -e "${YELLOW}Waiting additional 5 seconds before continuing...${NC}"
            sleep 5
            return 0
        fi
        echo -n "."
        sleep 5
        attempt=$((attempt + 1))
    done

    echo -e "\n${RED}Timeout waiting for $app_name to appear in CasaOS app list${NC}"
    return 1
}