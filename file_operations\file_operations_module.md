# File Operations Module

This module provides comprehensive file and folder management functionality including renaming, searching, moving, copying, compression, and permission management. It is broken down into several focused scripts for better organization and maintainability.

## Files

### file_operations_menu.sh
Provides the interactive menu system for accessing all file operations functionalities. It defines the menus that call functions from the other scripts in this module.

**Key Functions:**
- `file_operations_main_menu()`: Main entry menu for this module.
- `show_file_menu()`: Menu for file-specific operations.
- `show_folder_menu()`: Menu for folder-specific operations.
- `show_compression_menu()`: Menu for compression operations.
- `show_permission_menu()`: Menu for permission operations.

### file_operations_utils.sh
Contains common utility functions used across the file operations module.

**Key Functions:**
- **Color Definitions:** `RED`, `GREEN`, `YELLOW`, `BLUE`, `NC` for terminal output styling.
- **Validation:**
    - `validate_path()`: Checks for invalid characters in paths.
    - `validate_pattern()`: Checks for invalid characters in patterns.
    - `validate_input()`: Ensures input is not empty.
    - `validate_numeric()`: Validates numeric input within a range.
    - `validate_extension()`: Validates file extension format.
- **Path Handling:**
    - `sanitize_path()`: Escapes spaces for command usage (may need review depending on usage).
    - `handle_path()`: Adds quotes to paths with spaces for safe use in commands like `find`.
    - `get_unique_extensions()`: Lists unique file extensions in a directory.

### file_operations_file_rename.sh
Handles bulk renaming operations specifically for files.

**Key Functions:**
- `bulk_rename_files()`: Renames files based on sequence numbers or pattern removal.

### file_operations_file_manage.sh
Handles searching, copying, moving, and deleting files.

**Key Functions:**
- `search_files_by_extension()`: Finds files based on their extension.
- `copy_files_by_extension()`: Copies files matching a specific extension to a destination.
- `move_files_by_extension()`: Moves files matching a specific extension to a destination.
- `search_delete_files()`: Searches for files based on various criteria (name, extension, pattern) and deletes them after confirmation.

### file_operations_folder_ops.sh
Handles operations specifically for folders (directories).

**Key Functions:**
- `bulk_rename_folders()`: Renames folders based on sequence numbers or pattern removal.
- `search_folders()`: Finds folders based on name patterns.
- `copy_folders()`: Copies folders matching a pattern to a destination.
- `move_folders()`: Moves folders matching a pattern to a destination.
- `delete_folders()`: Deletes folders matching a pattern (including contents) after double confirmation.
- `delete_empty_folders()`: Finds and deletes empty folders within a path.

### file_operations_compression.sh
Handles compression and decompression tasks for files and folders. Includes helpers to check for and install necessary command-line tools (like `tar`, `zip`, `rar`, `pv`). These functions are interactive and prompt the user for input.

**Key Functions:**
- `install_package()`: Attempts to install missing packages (Debian/Ubuntu focused).
- `check_pv()`: Checks for and installs `pv` (pipe viewer) for progress bars.
- `check_compression_tools()`: Verifies required compression utilities are installed.
- `compress_items()`: Interactive function that compresses specified folders into TAR.GZ, ZIP, or RAR format based on user input.
- `decompress_archive()`: Interactive function that decompresses TAR.GZ, TGZ, ZIP, or RAR archives with user prompts.

### file_operations_noninteractive.sh
Contains non-interactive versions of compression and decompression functions for use in automated scripts or other modules that need to avoid user prompts.

**Key Functions:**
- `compress_items_noninteractive()`: Non-interactive compression function that takes all parameters as arguments.
  - Usage: `compress_items_noninteractive base_path folder_names output_path [format]`
  - Example: `compress_items_noninteractive /opt/data "folder1 folder2" /backups/archive.tar.gz`
  - Defaults to tar.gz format if not specified
  - Suppresses all user prompts and interactive elements
- `decompress_archive_noninteractive()`: Non-interactive decompression function.
  - Usage: `decompress_archive_noninteractive archive_path destination_dir`
  - Example: `decompress_archive_noninteractive /backups/archive.tar.gz /opt/data`
  - Suppresses all user prompts

### file_operations_permissions.sh
Handles changing permissions (mode) and ownership for files and folders.

**Key Functions:**
- `convert_to_octal()`: Converts symbolic permissions (rwxr-xr-x) to octal (755).
- `explain_permissions()`: Displays examples of common permission settings.
- `process_permissions()`: Validates and converts user input (octal or symbolic) into octal format.
- `show_item_permissions()`: Displays the current permissions of a file or folder.
- `change_file_permissions()`: Changes permissions for a single file or files matching a pattern within a directory (optionally recursive).
- `change_folder_permissions()`: Changes permissions for a specific folder, optionally applying different permissions recursively to files and subdirectories within it.
- `change_ownership()`: Changes the user and group owner of a file or folder (optionally recursive).

## Usage

The File Operations module provides comprehensive file and folder management capabilities through an interactive menu.

To access File Operations functionality:
1. Ensure the main script sources all necessary `file_operations/*.sh` scripts (typically done via a global sourcing mechanism like in `main.sh`).
2. Call the `file_operations_main_menu()` function (defined in `file_operations_menu.sh`) from your main script's menu.
3. Navigate the menus: Choose from file, folder, compression, or permission management options.
4. Follow the interactive prompts to perform operations.

## When to Use Non-Interactive Functions

Use the non-interactive functions in `file_operations_noninteractive.sh` when:

1. Building automated scripts that should run without user interaction
2. Performing operations from other modules where prompts would be disruptive
3. Creating backup/restore features that need to run silently
4. Setting up scheduled tasks or cron jobs
5. Implementing API-like functionality where user input isn't available

Example:
```bash
# Interactive (requires user input)
compress_items

# Non-interactive (all parameters provided as arguments)
compress_items_noninteractive "/opt/data" "config logs" "/backup/data_backup.tar.gz" "tar.gz"
```

## Integration with Other Modules

The File Operations module can be used by any module that requires file or folder management. It provides core functionality for manipulating the filesystem in a reliable and safe manner. 

For interactive use:
- Source `file_operations/*.sh` files and use the interactive functions

For automated/non-interactive use:
- Source `file_operations/file_operations_noninteractive.sh` directly and use the non-interactive functions

Note that non-interactive functions still handle error checking and validation, but will return exit codes instead of prompting for corrections.