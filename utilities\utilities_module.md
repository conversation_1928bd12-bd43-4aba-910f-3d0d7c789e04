# Utilities Module

This module provides general utility functions for system management, package handling, and error management.

## Files

### utilities_functions.sh
Core utility functions for system management and error handling.

**Key Functions:**
- `log_message()`: Logs formatted messages with different severity levels
- `handle_error()`: Handles errors with proper logging and exit handling
- `manage_package()`: Manages package installation, removal, and verification
- `install_all_utilities()`: Installs all required utility packages
- `remove_all_utilities()`: Removes all utility packages that were installed

### utilities_menu.sh
Provides a menu interface for utility functions.

**Key Functions:**
- `utilities_menu()`: Main menu for accessing utility functions

## Usage

The Utilities module provides essential functions for system management and setup. It's fundamental for ensuring that the required packages are installed for other modules to function correctly.

To access Utilities functionality:
1. Select "Utilities Menu" from the main menu
2. Choose to install or remove utility packages

## Integration with Other Modules

The Utilities module is a core dependency for all other modules, as it ensures the system has the necessary packages installed for proper operation. Most modules will depend on the functionality provided by this module. 