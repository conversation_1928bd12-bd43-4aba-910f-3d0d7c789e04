#!/bin/bash

# Common variables for colored output and defaults
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color
DEFAULT_DOMAIN="mcsaguru.com"
DEFAULT_CF_TOKEN="8dIpQLjNIa64ISsI17ROKuJV-lBB25a1awJ6vw8T"
CERTBOT_DIR="/opt/certbot"

# Common utility functions for output and input
print_message() {
    echo -e "${GREEN}[+] $1${NC}"
}

print_error() {
    echo -e "${RED}[!] $1${NC}"
}

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    read -p "$prompt [$default]: " value
    echo "${value:-$default}"
}

# Check if a certificate exists for the given domain
check_certificate_exists() {
    local domain=$1
    local cert_dir
    
    # Check if the directory exists first
    if [ ! -d "/etc/letsencrypt/live/" ]; then
        print_message "No existing certificates found. Will generate a new one."
        return 1
    fi
    
    # Find the most recent certificate directory
    cert_dir=$(sudo find /etc/letsencrypt/live/ -maxdepth 1 -type d -name "${domain}*" 2>/dev/null | sort -r | head -n1)
    
    if [ -n "$cert_dir" ] && [ -f "$cert_dir/fullchain.pem" ]; then
        # Get expiration date
        local expiry_date=$(sudo openssl x509 -enddate -noout -in "$cert_dir/fullchain.pem" | cut -d= -f2)
        print_message "Certificate already exists at: $cert_dir/fullchain.pem"
        print_message "Certificate expires on: $expiry_date"
        return 0
    fi
    print_message "No existing certificate found for $domain. Will generate a new one."
    return 1
}

# Check if a certificate is expiring within a week
check_certificate_expiry() {
    local domain=$1
    local cert_dir
    
    # Find the most recent certificate directory
    cert_dir=$(sudo find /etc/letsencrypt/live/ -maxdepth 1 -type d -name "${domain}*" | sort -r | head -n1)
    
    if [ -z "$cert_dir" ] || [ ! -f "$cert_dir/fullchain.pem" ]; then
        return 1
    fi

    # Get expiration date in seconds since epoch
    local expiry_date=$(sudo openssl x509 -enddate -noout -in "$cert_dir/fullchain.pem" | cut -d= -f2)
    local expiry_epoch=$(date -d "$expiry_date" +%s)
    local current_epoch=$(date +%s)
    local seconds_in_two_weeks=2419200

    # Calculate difference in seconds
    local difference=$((expiry_epoch - current_epoch))

    if [ $difference -le $seconds_in_two_weeks ]; then
        return 0
    fi
    return 1
}

# Setup Cloudflare configuration with API token
setup_cloudflare_config() {
    local cf_token=$1
    
    # Create directory if it doesn't exist
    sudo mkdir -p "$CERTBOT_DIR"
    sudo chmod 700 "$CERTBOT_DIR"
    
    # Define the config file path
    local config_file="$CERTBOT_DIR/cloudflare.ini"
    
    print_message "Creating Cloudflare configuration..."
    # First remove if it's a directory
    if [ -d "$config_file" ]; then
        sudo rm -rf "$config_file"
    fi
    
    # Create the configuration file
    echo "# Cloudflare API token with Zone:DNS:Edit permissions
dns_cloudflare_api_token = $cf_token" | sudo tee "$config_file" > /dev/null
    
    sudo chmod 600 "$config_file"
}

# Function to create renewal script and setup crontab
create_renewal_script() {
    local domain=$1
    local renewal_script="$CERTBOT_DIR/renew-cert-${domain}.sh"
    
    # Ensure CERTBOT_DIR exists with proper permissions
    sudo mkdir -p "$CERTBOT_DIR"
    
    print_message "Creating renewal script..."
    # Using sudo tee instead of direct redirection
    cat << 'EOF' | sudo tee "$renewal_script" > /dev/null
#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

print_message() {
    echo -e "${GREEN}[+] $1${NC}"
}

print_error() {
    echo -e "${RED}[!] $1${NC}"
}

sync_certificates_to_remote() {
    print_message "Syncing certificates to remote storage..."
    echo "Executing rclone command: sudo rclone sync \"/etc/letsencrypt/live/\" \"$RCLONE_REMOTE:/Backups/Certificates\" --progress -L"
    sudo rclone sync "/etc/letsencrypt/live/" "$RCLONE_REMOTE:/Backups/Certificates" --progress -L
}

copy_to_nginx_proxy() {
    local domain=$1
    local npm_dir="/DATA/AppData/nginxproxymanager/data/custom_ssl/npm-3"
    local cert_dir="/etc/letsencrypt/live/$domain"

    if [ -d "$npm_dir" ]; then
        print_message "Found Nginx Proxy Manager directory. Copying certificates..."
        
        # Copy the certificates
        sudo cp "$cert_dir/fullchain.pem" "$npm_dir/fullchain.pem"
        sudo cp "$cert_dir/privkey.pem" "$npm_dir/privkey.pem"
        
        if [ $? -eq 0 ]; then
            print_message "Successfully copied certificates to Nginx Proxy Manager"
            # Set proper permissions
            sudo chown -R 1000:1000 "$npm_dir"
            sudo chmod 600 "$npm_dir"/*.pem
        else
            print_error "Failed to copy certificates to Nginx Proxy Manager"
        fi
    fi
}

check_certificate_exists() {
    local domain=$1
    local cert_dir
    
    cert_dir=$(sudo find /etc/letsencrypt/live/ -maxdepth 1 -type d -name "${domain}*" | sort -r | head -n1)
    
    if [ -n "$cert_dir" ] && [ -f "$cert_dir/fullchain.pem" ]; then
        echo "$cert_dir"
        return 0
    fi
    return 1
}

check_certificate_expiry() {
    local cert_path=$1
    
    # Get expiration date in seconds since epoch
    local expiry_date=$(sudo openssl x509 -enddate -noout -in "$cert_path" | cut -d= -f2)
    local expiry_epoch=$(date -d "$expiry_date" +%s)
    local current_epoch=$(date +%s)
    local seconds_in_15_days=1296000

    # Calculate difference in seconds
    local difference=$((expiry_epoch - current_epoch))

    if [ $difference -le $seconds_in_15_days ]; then
        echo "$expiry_date"
        return 0
    fi
    echo "$expiry_date"
    return 1
}

DOMAIN="$DOMAIN"

# Find certificate directory and check if it exists
CERT_DIR=$(check_certificate_exists "$DOMAIN")
if [ $? -ne 0 ]; then
    print_error "Certificate does not exist for domain: $DOMAIN"
    exit 1
fi

CERT_PATH="$CERT_DIR/fullchain.pem"
print_message "Found certificate at: $CERT_PATH"

# Check expiry and renew if needed
expiry_date=$(check_certificate_expiry "$CERT_PATH")
if [ $? -eq 0 ]; then
    print_message "Certificate expires soon (Expiry date: $expiry_date). Running renewal..."
    
    # Clean up existing certificate directories before renewal
    print_message "Cleaning up existing certificate directories..."
    sudo rm -rf "/etc/letsencrypt/live/$DOMAIN"*
    sudo rm -rf "/etc/letsencrypt/archive/$DOMAIN"*
    sudo rm -rf "/etc/letsencrypt/renewal/$DOMAIN"*
    
    docker run --rm \
        -v /opt/certbot/cloudflare.ini:/cloudflare.ini:ro \
        -v /etc/letsencrypt:/etc/letsencrypt \
        -v /var/lib/letsencrypt:/var/lib/letsencrypt \
        certbot/dns-cloudflare certonly \
        --dns-cloudflare \
        --dns-cloudflare-credentials /cloudflare.ini \
        -d "*.$DOMAIN" \
        -d "$DOMAIN" \
        --agree-tos \
        --non-interactive \
        --email <EMAIL> \
        --force-renewal
    
    if [ $? -eq 0 ]; then
        print_message "Certificate renewal successful"
        sync_certificates_to_remote
        copy_to_nginx_proxy "$DOMAIN"
    else
        print_error "Certificate renewal failed"
    fi
else
    print_message "Certificate is still valid (Expires: $expiry_date). No renewal needed."
fi
EOF

    # Replace DOMAIN placeholder with actual domain
    sudo sed -i "s/DOMAIN=\"\$DOMAIN\"/DOMAIN=\"$domain\"/" "$renewal_script"
    sudo chmod +x "$renewal_script"
    
    # Setup crontab to run twice daily
    local cron_job="0 0,12 * * * $renewal_script >> $CERTBOT_DIR/renewal-${domain}.log 2>&1"
    # Remove any existing cron jobs for this domain
    (crontab -l 2>/dev/null | grep -v "renew-cert-${domain}.sh") | crontab -
    # Add the new cron job
    (crontab -l 2>/dev/null; echo "$cron_job") | crontab -
    
    echo "$renewal_script"
    return 0
}

# Function to check and copy certificates from rclone remote
check_rclone_certificates() {
    local domain=$1
    print_message "Checking for existing certificates in remote storage..."
    install_rclone
    # Check if domain exists in rclone remote
    echo "Executing rclone command: rclone lsd $RCLONE_REMOTE:/Backups/Certificates"
    if rclone lsd $RCLONE_REMOTE:/Backups/Certificates | grep -q "$domain"; then
        print_message "Found certificates for $domain in remote storage. Copying..."
        
        # Create directory if it doesn't exist
        sudo mkdir -p "/etc/letsencrypt/live/"
        
        # Copy certificates from remote
        echo "Executing rclone command: sudo rclone sync \"$RCLONE_REMOTE:/Backups/Certificates/$domain\" \"/etc/letsencrypt/live/$domain\" --progress -L"
        sudo rclone sync "$RCLONE_REMOTE:/Backups/Certificates/$domain" "/etc/letsencrypt/live/$domain" --progress -L
        
        if [ $? -eq 0 ]; then
            print_message "Successfully copied certificates from remote storage"
            return 0
        else
            print_error "Failed to copy certificates from remote storage"
            return 1
        fi
    fi
    
    print_message "No existing certificates found in remote storage"
    return 1
}

# Function to sync certificates to rclone remote
sync_certificates_to_remote() {
    install_rclone
    local domain=$1
    print_message "Syncing certificates to remote storage..."
    
    echo "Executing rclone command: sudo rclone sync \"/etc/letsencrypt/live/\" \"$RCLONE_REMOTE:/Backups/Certificates\" --progress -L"
    sudo rclone sync "/etc/letsencrypt/live/" "$RCLONE_REMOTE:/Backups/Certificates" --progress -L
    
    if [ $? -eq 0 ]; then
        print_message "Successfully synced certificates to remote storage"
        return 0
    else
        print_error "Failed to sync certificates to remote storage"
        return 1
    fi
}

# Function to copy certificates to nginx proxy manager
copy_to_nginx_proxy() {
    local domain=$1
    local npm_dir="/DATA/AppData/nginxproxymanager/data/custom_ssl/npm-3"
    local cert_dir="/etc/letsencrypt/live/$domain"

    if [ -d "$npm_dir" ]; then
        print_message "Found Nginx Proxy Manager directory. Copying certificates..."
        
        # Copy the certificates
        sudo cp "$cert_dir/fullchain.pem" "$npm_dir/fullchain.pem"
        sudo cp "$cert_dir/privkey.pem" "$npm_dir/privkey.pem"
        
        if [ $? -eq 0 ]; then
            print_message "Successfully copied certificates to Nginx Proxy Manager"
            # Set proper permissions
            sudo chown -R 1000:1000 "$npm_dir"
            sudo chmod 600 "$npm_dir"/*.pem
        else
            print_error "Failed to copy certificates to Nginx Proxy Manager"
        fi
    fi
}

# Main function to generate SSL certificate
generate_ssl_certificate() {
    print_message "Please provide the following information (press Enter to use default values):"
    local domain=$(prompt_with_default "Enter your domain name" "$DEFAULT_DOMAIN")
    
    # First check rclone remote for existing certificates
    if check_rclone_certificates "$domain"; then
        if check_certificate_exists "$domain"; then
            if check_certificate_expiry "$domain"; then
                print_message "Certificate is expiring within 15 days. Proceeding with renewal..."
            else
                print_message "Certificate is still valid and not expiring soon. Using existing certificate."
                create_renewal_script "$domain"
                return 0
            fi
        fi
    fi

    local cf_token=$(prompt_with_default "Enter your Cloudflare API token" "$DEFAULT_CF_TOKEN")

    # Check if running as root
    if [ "$EUID" -ne 0 ]; then 
        print_error "Please run as root (use sudo)"
        return 1
    fi

    # Create directories for certbot
    sudo mkdir -p /etc/letsencrypt /var/lib/letsencrypt
    setup_cloudflare_config "$cf_token"

    # Generate certificate using Docker
    print_message "Pulling Certbot Docker image..."
    docker pull certbot/dns-cloudflare

    print_message "Generating SSL certificate..."
    # First clean up existing certificate directories
    if [ -d "/etc/letsencrypt/live/$domain" ]; then
        print_message "Cleaning up existing certificate directories..."
        sudo rm -rf "/etc/letsencrypt/live/$domain"*
        sudo rm -rf "/etc/letsencrypt/archive/$domain"*
        sudo rm -rf "/etc/letsencrypt/renewal/$domain"*
    fi

    docker run -it --rm \
      -v $CERTBOT_DIR/cloudflare.ini:/cloudflare.ini:ro \
      -v /etc/letsencrypt:/etc/letsencrypt \
      -v /var/lib/letsencrypt:/var/lib/letsencrypt \
      certbot/dns-cloudflare certonly \
      --dns-cloudflare \
      --dns-cloudflare-credentials /cloudflare.ini \
      -d "*.$domain" \
      -d "$domain" \
      --agree-tos \
      --non-interactive \
      --email <EMAIL> \
      --force-renewal

    if [ $? -eq 0 ]; then
        # Sync the new certificates to remote storage
        sync_certificates_to_remote "$domain"
        
        # Copy certificates to nginx proxy manager if directory exists
        copy_to_nginx_proxy "$domain"
        
        # Setup renewal script and crontab
        local renewal_script
        renewal_script=$(create_renewal_script "$domain")
        
        print_message "Setup complete!"
        print_message "Configuration and renewal script are stored in $CERTBOT_DIR"
        print_message "Renewal script: $renewal_script"
        print_message "Renewal log: $CERTBOT_DIR/renewal-${domain}.log"
    else
        print_error "Certificate generation failed"
        return 1
    fi
}

# Function to setup only the renewal script without generating a new certificate
setup_renewal_script() {
    print_message "Please provide the following information (press Enter to use default values):"
    local domain=$(prompt_with_default "Enter your domain name" "$DEFAULT_DOMAIN")
    local cf_token=$(prompt_with_default "Enter your Cloudflare API token" "$DEFAULT_CF_TOKEN")

    setup_cloudflare_config "$cf_token"
    local renewal_script
    renewal_script=$(create_renewal_script "$domain")
    
    print_message "Renewal script setup complete!"
    print_message "Configuration and renewal script are stored in $CERTBOT_DIR"
    print_message "Renewal script: $renewal_script"
    print_message "Renewal log: $CERTBOT_DIR/renewal-${domain}.log"
}

# Function to restore certificate from remote
restore_certificate_from_remote() {
    print_message "Please provide the following information:"
    local domain=$(prompt_with_default "Enter your domain name" "$DEFAULT_DOMAIN")
    
    print_message "Available certificates in remote storage:"
    echo "Executing rclone command: rclone lsd $RCLONE_REMOTE:/Backups/Certificates"
    rclone lsd $RCLONE_REMOTE:/Backups/Certificates
    
    echo "Executing rclone command: rclone lsd $RCLONE_REMOTE:/Backups/Certificates"
    if ! rclone lsd $RCLONE_REMOTE:/Backups/Certificates | grep -q "$domain"; then
        print_error "No certificate found for $domain in remote storage"
        return 1
    fi
    
    print_message "Found certificate for $domain. Proceeding with restore..."
    
    # Create directory if it doesn't exist
    mkdir -p "/etc/letsencrypt/live/$domain"
    
    # Copy certificates from remote
    echo "Executing rclone command: rclone sync \"$RCLONE_REMOTE:/Backups/Certificates/$domain\" \"/etc/letsencrypt/live/$domain\" --progress -L"
    rclone sync "$RCLONE_REMOTE:/Backups/Certificates/$domain" "/etc/letsencrypt/live/$domain" --progress -L
    
    if [ $? -eq 0 ]; then
        print_message "Successfully restored certificates from remote storage"
        
        # Verify the restored certificate
        if [ -f "/etc/letsencrypt/live/$domain/fullchain.pem" ]; then
            local expiry_date=$(openssl x509 -enddate -noout -in "/etc/letsencrypt/live/$domain/fullchain.pem" | cut -d= -f2)
            print_message "Certificate details:"
            print_message "Domain: $domain"
            print_message "Expires on: $expiry_date"
            
            # Setup renewal script
            local renewal_script
            renewal_script=$(create_renewal_script "$domain")
            print_message "Renewal script created at: $renewal_script"
        else
            print_error "Certificate files appear to be incomplete or corrupted"
            return 1
        fi
        
        return 0
    else
        print_error "Failed to restore certificates from remote storage"
        return 1
    fi
}

# Function to backup certificate to remote
backup_certificate_to_remote() {
    print_message "Please provide the following information:"
    local domain=$(prompt_with_default "Enter your domain name" "$DEFAULT_DOMAIN")
    
    # Check if certificate exists locally
    if ! check_certificate_exists "$domain"; then
        print_error "No certificate found locally for $domain"
        return 1
    fi
    
    print_message "Found local certificate for $domain. Proceeding with backup..."
    
    # Sync to remote
    echo "Executing rclone command: sudo rclone sync \"/etc/letsencrypt/live/$domain\" \"$RCLONE_REMOTE:/Backups/Certificates/$domain\" --progress -L"
    sudo rclone sync "/etc/letsencrypt/live/$domain" "$RCLONE_REMOTE:/Backups/Certificates/$domain" --progress -L
    
    if [ $? -eq 0 ]; then
        print_message "Successfully backed up certificates to remote storage"
        
        # Verify the backup
        echo "Executing rclone command: rclone lsd $RCLONE_REMOTE:/Backups/Certificates"
        if rclone lsd $RCLONE_REMOTE:/Backups/Certificates | grep -q "$domain"; then
            print_message "Backup verification successful"
            print_message "Remote backup location: $RCLONE_REMOTEBackups/Certificates/$domain"
            return 0
        else
            print_error "Backup verification failed"
            return 1
        fi
    else
        print_error "Failed to backup certificates to remote storage"
        return 1
    fi
} 