#!/bin/bash

# Add this function at the top of the file if not already present
ensure_expect_installed() {
    if ! command -v expect >/dev/null 2>&1; then
        echo "Installing expect..."
        apt-get update && apt-get install -y expect
    fi
}

# Node Management Functions
list_nodes() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║        List All Nodes         ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    docker exec headscale headscale nodes list
}

delete_node() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║        Delete Node            ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    list_nodes
    read -rp "$(echo -e ${YELLOW}"Enter node ID to delete: "${NC})" node_id
    
    if [[ -n "$node_id" ]]; then
        docker exec headscale headscale nodes delete -i "$node_id" --force
        echo -e "${GREEN}Node deleted successfully${NC}"
    else
        echo -e "${RED}Invalid node ID${NC}"
    fi
}

rename_node() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║        Rename Node            ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    list_nodes
    read -rp "$(echo -e ${YELLOW}"Enter node ID to rename: "${NC})" node_id
    read -rp "$(echo -e ${YELLOW}"Enter new name: "${NC})" new_name
    
    if [[ -n "$node_id" && -n "$new_name" ]]; then
        docker exec headscale headscale nodes rename -i "$node_id" "$new_name"
        echo -e "${GREEN}Node renamed successfully${NC}"
    else
        echo -e "${RED}Invalid input${NC}"
    fi
}

# User Management Functions
list_users() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║        List All Users         ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    docker exec headscale headscale users list
}

create_user() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║        Create User            ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    read -rp "$(echo -e ${YELLOW}"Enter new username: "${NC})" username
    
    if [[ -n "$username" ]]; then
        docker exec headscale headscale users create "$username"
        echo -e "${GREEN}User created successfully${NC}"
    else
        echo -e "${RED}Invalid username${NC}"
    fi
}

delete_user() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║        Delete User            ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    list_users
    read -rp "$(echo -e ${YELLOW}"Enter username to delete: "${NC})" username
    
    if [[ -n "$username" ]]; then
        # First confirm with the user
        read -rp "$(echo -e ${YELLOW}"Are you sure you want to delete user '$username'? (y/N): "${NC})" confirm
        if [[ $confirm == [yY] ]]; then
            docker exec -i headscale headscale users destroy "$username" --force
            echo -e "${GREEN}User deleted successfully${NC}"
        else
            echo -e "${YELLOW}User deletion cancelled${NC}"
        fi
    else
        echo -e "${RED}Invalid username${NC}"
    fi
}

# Route Management Functions
list_routes() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║        List All Routes        ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    docker exec headscale headscale routes list
}

manage_route() {
    local action=$1
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║        Manage Route           ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    list_routes
    read -rp "$(echo -e ${YELLOW}"Enter route ID(s) to $action (separate multiple IDs with spaces): "${NC})" -a route_ids
    
    if [[ ${#route_ids[@]} -gt 0 ]]; then
        for route_id in "${route_ids[@]}"; do
            if [[ -n "$route_id" ]]; then
                echo -e "${YELLOW}Processing route ID: $route_id${NC}"
                docker exec headscale headscale routes "$action" -r "$route_id"
                echo -e "${GREEN}Route ${action}d successfully${NC}"
            fi
        done
    else
        echo -e "${RED}No valid route IDs provided${NC}"
    fi
}

# PreAuth Key Management Functions
list_preauthkeys() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║      List PreAuth Keys        ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    list_users
    read -rp "$(echo -e ${YELLOW}"Enter username to list keys: "${NC})" username
    
    if [[ -n "$username" ]]; then
        docker exec headscale headscale preauthkeys list -u "$username"
    else
        echo -e "${RED}Invalid username${NC}"
    fi
}

create_preauthkey() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║     Create PreAuth Key        ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    list_users
    read -rp "$(echo -e ${YELLOW}"Enter username: "${NC})" username
    read -rp "$(echo -e ${YELLOW}"Enter expiration (e.g., 24h, 7d): "${NC})" expiration
    read -rp "$(echo -e ${YELLOW}"Make reusable? (y/n): "${NC})" reusable
    
    if [[ -n "$username" ]]; then
        local cmd="docker exec headscale headscale preauthkeys create -u $username"
        [[ -n "$expiration" ]] && cmd="$cmd -e $expiration"
        [[ "$reusable" == [yY] ]] && cmd="$cmd --reusable"
        eval "$cmd"
        echo -e "${GREEN}PreAuth key created successfully${NC}"
    else
        echo -e "${RED}Invalid username${NC}"
    fi
}
