#!/bin/bash

# Function to check if a port is in use
check_port() {
    local port=$1
    local protocol=$2
    if [ -z "$protocol" ]; then
        protocol="tcp"
    fi
    
    if netstat -tuln | grep -q ":$port\s"; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to check all required ports
check_all_ports() {
    local ports=("53" "67" "80" "443" "3000" "853" "784" "8853" "5443")
    local protocols=("tcp" "udp")
    local in_use_ports=()

    for port in "${ports[@]}"; do
        for protocol in "${protocols[@]}"; do
            if check_port "$port" "$protocol"; then
                in_use_ports+=("$port/$protocol")
            fi
        done
    done

    if [ ${#in_use_ports[@]} -gt 0 ]; then
        echo "The following ports are in use:"
        printf '%s\n' "${in_use_ports[@]}"
    fi
    return 0
}

# Function to check if AdGuard container is running
is_adguard_running() {
    if docker ps -a | grep -q "adguardhome"; then
        return 0
    else
        return 1
    fi
}

# Function to stop AdGuard container
stop_adguard() {
    if is_adguard_running; then
        echo "Stopping AdGuard Home container..."
        docker stop adguardhome
        docker rm adguardhome
        echo "AdGuard Home container stopped and removed."
    else
        echo "AdGuard Home container is not running."
    fi

    # Remove AdGuard Home directories
    if [ -d "/opt/adguardhome" ]; then
        echo "Removing AdGuard Home directories..."
        rm -rf /opt/adguardhome
        echo "AdGuard Home directories removed."
    fi
}

# Function to completely uninstall AdGuard
uninstall_adguard() {
    echo "Starting AdGuard Home uninstallation process..."
    
    if is_adguard_running; then
        echo -e "${YELLOW}AdGuard Home is currently running.${NC}"
    else
        echo -e "${YELLOW}AdGuard Home is not currently running.${NC}"
        read -rp "Proceed with cleanup? (y/n): " cleanup_choice
        if [[ ! "$cleanup_choice" =~ ^[Yy]$ ]]; then
            echo "Uninstallation cancelled."
            return 1
        fi
    fi
    
    # First backup prompt
    read -rp "Would you like to backup the configuration before uninstalling? (y/n): " backup_choice
    if [[ "$backup_choice" =~ ^[Yy]$ ]]; then
        echo "Creating backup before uninstallation..."
        if ! backup_adguard_config; then
            read -rp "Backup failed. Continue with uninstallation anyway? (y/n): " continue_choice
            if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
                echo "Uninstallation cancelled."
                return 1
            fi
        fi
    fi

    # Stop and remove container if running
    if is_adguard_running; then
        echo "Stopping and removing AdGuard Home container..."
        docker stop adguardhome
        docker rm adguardhome
    else
        # Check for stopped containers and remove them
        echo "Checking for stopped AdGuard Home containers..."
        stopped_containers=$(docker ps -a -f name=adguardhome -q)
        if [ -n "$stopped_containers" ]; then
            echo "Found stopped AdGuard Home containers, removing them..."
            docker rm $stopped_containers
        fi
    fi

    # Remove the Docker image
    echo "Removing AdGuard Home Docker image..."
    docker rmi adguard/adguardhome:latest --force

    # Remove configuration directories
    if [ -d "/opt/adguardhome" ]; then
        echo "Removing AdGuard Home directories..."
        rm -rf /opt/adguardhome
    fi

    # Remove backup directory if empty
    if [ -d "/opt/adguard_backups" ] && [ -z "$(ls -A /opt/adguard_backups)" ]; then
        echo "Removing empty backup directory..."
        rm -rf /opt/adguard_backups
    fi

    # Revert port 53 configuration if it was modified
    echo "Checking port 53 configuration..."
    if [ -f "/etc/systemd/resolved.conf.d/adguardhome.conf" ]; then
        echo "Reverting port 53 configuration..."
        revert_adguard_port_fix
    fi

    echo "AdGuard Home has been completely uninstalled."
}

# Function to install AdGuard container
install_adguard() {
    # Check if already installed
    if is_adguard_running; then
        echo "AdGuard Home is already installed and running."
        read -rp "Would you like to reinstall AdGuard Home? (y/n): " reinstall_choice
        if [[ "$reinstall_choice" =~ ^[Yy]$ ]]; then
            echo "Proceeding with reinstallation..."
            uninstall_adguard
        else
            echo "Installation cancelled."
            return 1
        fi
    fi

    # Rest of the installation process
    # Ensure Docker is installed and running
    install_docker

    # Check if port 53 is in use and fix if needed
    if check_port 53; then
        echo "Port 53 is in use. Attempting to fix..."
        fix_adguard_port_conflict
        if [ $? -ne 0 ]; then
            echo "Failed to free port 53. Please check manually."
            return 1
        fi
    fi

    # Check all other ports
    if ! check_all_ports; then
        echo "Some required ports are in use. Please free them before starting AdGuard Home."
        return 1
    fi

    # Remove existing directories if they exist and create fresh ones
    echo "Setting up fresh directories for AdGuard Home..."
    if [ -d "/opt/adguardhome" ]; then
        echo "Removing existing AdGuard Home directory..."
        rm -rf /opt/adguardhome
    fi
    
    echo "Creating fresh directories..."
    mkdir -p /opt/adguardhome/work
    mkdir -p /opt/adguardhome/config

    # Pull the latest image first
    echo "Pulling latest AdGuard Home image..."
    docker pull adguard/adguardhome:latest

    # Start the container
    echo "Starting AdGuard Home container..."
    docker run --name adguardhome \
        --restart unless-stopped \
        -v /opt/adguardhome/work:/opt/adguardhome/work \
        -v /opt/adguardhome/config:/opt/adguardhome/conf \
        -p 53:53/tcp -p 53:53/udp \
        -p 80:80/tcp -p 443:443/tcp -p 443:443/udp -p 3000:3000/tcp \
        -p 853:853/tcp \
        -p 784:784/udp -p 853:853/udp -p 8853:8853/udp \
        -p 5443:5443/tcp -p 5443:5443/udp \
        -d adguard/adguardhome:latest

    if [ $? -eq 0 ]; then
        echo "AdGuard Home container started successfully."
        echo "You can access the web interface at http://localhost:3000"
        
        # Prompt for configuration restore
        read -rp "Would you like to restore a previous configuration? (y/n): " restore_choice
        if [[ "$restore_choice" =~ ^[Yy]$ ]]; then
            echo "Stopping container for configuration restore..."
            docker stop adguardhome
            
            # Check if rclone is installed
            if ! install_rclone; then
                echo "Failed to verify/install Rclone. Restore cannot proceed."
                docker start adguardhome
                return 1
            fi

            local backup_dir="/opt/adguard_backups"
            local rclone_source="$RCLONE_REMOTEBackups/Adguard"
            mkdir -p "$backup_dir"

            # Get latest backup
            echo "Checking available backups in cloud storage..."
            echo "Executing rclone command: rclone ls \"$RCLONE_REMOTE:/Backups/Adguard\""
            if rclone ls "$rclone_source" | grep -q "adguard_backup.tar.gz"; then
                echo "Found backup: adguard_backup.tar.gz"
                local temp_backup="$backup_dir/adguard_backup.tar.gz"
                
                # Download and restore backup using direct copy command
                echo "Downloading backup from cloud storage..."
                echo "Executing rclone command: rclone copy \"$RCLONE_REMOTE:/Backups/Adguard/adguard_backup.tar.gz\" \"$backup_dir\" --progress"
                if rclone copy "$rclone_source/adguard_backup.tar.gz" "$backup_dir" --progress; then
                    if check_compression_tools; then
                        echo "Restoring configuration..."
                        if decompress_archive_noninteractive "$temp_backup" "/opt/adguardhome"; then
                            echo "Configuration restored successfully."
                            rm -f "$temp_backup"
                            echo "Restarting AdGuard Home with restored configuration..."
                            docker start adguardhome
                            return 0
                        fi
                    fi
                fi
                
                # Cleanup on failure
                rm -f "$temp_backup"
                echo "Failed to restore configuration. Starting AdGuard Home with default settings..."
                docker start adguardhome
            else
                echo "No backup files found in cloud storage."
                docker start adguardhome
                return 1
            fi
        fi
        return 0
    else
        echo "Failed to start AdGuard Home container."
        return 1
    fi
}

# Function to check AdGuard container status
check_adguard_status() {
    if is_adguard_running; then
        echo "AdGuard Home container is running."
        docker ps | grep adguardhome
    else
        echo "AdGuard Home container is not running."
    fi
}

# Function to view AdGuard container logs
view_adguard_logs() {
    if is_adguard_running; then
        docker logs adguardhome
    else
        echo "AdGuard Home container is not running."
    fi
}

# Function to start AdGuard container
start_adguard() {
    # Check if AdGuard is already running
    if is_adguard_running; then
        echo "AdGuard Home is already running."
        return 0
    fi
    
    # Check if AdGuard is installed but not running
    if [ -d "/opt/adguardhome" ]; then
        echo "AdGuard Home is installed but not running. Starting..."
        docker start adguardhome
        if [ $? -eq 0 ]; then
            echo "AdGuard Home started successfully."
            return 0
        else
            echo "Failed to start AdGuard Home. It may need to be reinstalled."
            return 1
        fi
    else
        # If not installed, install it
        echo "AdGuard Home is not installed. Installing..."
        install_adguard
        return $?
    fi
}
