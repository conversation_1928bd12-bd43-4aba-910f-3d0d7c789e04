#!/bin/bash

# ByteStash Container Management Functions
# Container-specific operations for ByteStash

# Function to show container details
show_container_details() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Container Details               ║${NC}"
    echo -e "${<PERSON>YAN}╚══════════════════════════════════════╝${NC}"
    
    if docker ps -a --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        docker inspect "${BYTESTASH_CONTAINER_NAME}" --format '
        Container Name: {{.Name}}
        Image: {{.Config.Image}}
        Status: {{.State.Status}}
        Created: {{.Created}}
        Started: {{.State.StartedAt}}
        Ports: {{range .NetworkSettings.Ports}}{{.}}{{end}}
        Mounts: {{range .Mounts}}{{.Source}}:{{.Destination}} {{end}}
        '
    else
        echo -e "${RED}✗ ByteStash container not found${NC}"
    fi
}

# Function to show container resource usage
show_container_resources() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Resource Usage                  ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    if docker ps --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        docker stats "${BYTESTASH_CONTAINER_NAME}" --no-stream
    else
        echo -e "${RED}✗ ByteStash container is not running${NC}"
    fi
}

# Function to remove container
remove_bytestash_container() {
    echo -e "${YELLOW}⚠ This will permanently remove the ByteStash container${NC}"
    echo -e "${YELLOW}Data will be preserved in ${BYTESTASH_DATA_DIR}${NC}"
    echo -e "${YELLOW}Continue? [y/N]:${NC}"
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Operation cancelled${NC}"
        return 1
    fi
    
    # Stop container if running
    if docker ps --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        stop_bytestash
    fi
    
    # Remove container
    if docker rm "${BYTESTASH_CONTAINER_NAME}" &> /dev/null; then
        echo -e "${GREEN}✓ ByteStash container removed successfully${NC}"
    else
        echo -e "${RED}✗ Failed to remove container or container not found${NC}"
    fi
}

# Function to recreate container
recreate_bytestash_container() {
    echo -e "${YELLOW}This will recreate the ByteStash container with current settings${NC}"
    echo -e "${YELLOW}Continue? [y/N]:${NC}"
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Operation cancelled${NC}"
        return 1
    fi
    
    remove_bytestash_container
    install_bytestash
}

# Function to export container
export_bytestash_container() {
    echo -e "${YELLOW}Enter export file path (e.g., /tmp/bytestash_export.tar):${NC}"
    read -r export_path
    
    if [[ -z "$export_path" ]]; then
        echo -e "${RED}No export path specified${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}Exporting container...${NC}"
    if docker export "${BYTESTASH_CONTAINER_NAME}" > "$export_path"; then
        echo -e "${GREEN}✓ Container exported to: $export_path${NC}"
    else
        echo -e "${RED}✗ Failed to export container${NC}"
    fi
}

# Function to show container network info
show_container_network() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Network Information             ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    if docker ps -a --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        docker inspect "${BYTESTASH_CONTAINER_NAME}" --format '
        Network Mode: {{.HostConfig.NetworkMode}}
        IP Address: {{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}
        Gateway: {{range .NetworkSettings.Networks}}{{.Gateway}}{{end}}
        Ports: {{range .NetworkSettings.Ports}}{{.}}{{end}}
        '
    else
        echo -e "${RED}✗ ByteStash container not found${NC}"
    fi
}