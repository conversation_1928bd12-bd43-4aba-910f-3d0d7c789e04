print_v2ray_client_id() {

    ID=$(cat /etc/v2ray/config.json | jq -r '.inbounds[0].settings.clients[0].id')
    echo "$ID"
}

install_v2ray() {

    install_docker

    echo -e "${RED}╔═══════════════════════════════╗${NC}"
    echo -e "${RED}║     Installing V2Ray VPN      ║${NC}"
    echo -e "${RED}╚═══════════════════════════════╝${NC}"
    echo ""

    echo -e "${YELLOW}Pulling image from Docker Hub...${NC}"
    docker pull fazee6/v2fly-core:latest

    DIRECTORY="/etc/v2ray"
    FILE="/etc/v2ray/config.json"
    
    while true; do
        read -rp "Enter port to use with v2ray server: " v2ray_port_input
        get_valid_port "$v2ray_port_input"
        if [ $? -eq 0 ]; then
            break
        fi
    done

    read -rp "Enter the client ID (press Enter for default): " CLIENT_ID
    DEFAULT_CLIENT_ID="b831381d-6224-4d43-ad4f-8cda48b30811"
    CLIENT_ID="${CLIENT_ID:-$DEFAULT_CLIENT_ID}"

    CONFIG_CONTENT=$(cat <<EOF
{
    "log": {
        "loglevel": "warning"
    },
    "inbounds": [
        {
            "port": "$v2ray_port_input",
            "protocol": "vmess",
            "settings": {
                "clients": [
                    {
                        "id": "$CLIENT_ID",
                        "level": 1,
                        "alterId": 0
                    }
                ],
                "disableInsecureEncryption": true
            },
            "streamSettings": {
                "network": "tcp",
                "security": "none",
                "tcpSettings": {
                    "header": {
                        "type": "none"
                    }
                }
            },
            "sniffing": {
                "enabled": true,
                "destOverride": ["http", "tls"]
            }
        }
    ],
    "outbounds": [
        {
            "protocol": "freedom",
            "settings": {
                "domainStrategy": "UseIP"
            }
        },
        {
            "tag": "blocked",
            "protocol": "blackhole",
            "settings": {}
        }
    ],
    "routing": {
        "domainStrategy": "IPIfNonMatch",
        "rules": [
            {
                "type": "field",
                "protocol": ["bittorrent"],
                "outboundTag": "blocked"
            }
        ]
    }
}
EOF
)

    if [ ! -d "$DIRECTORY" ]; then
        sudo mkdir -p "$DIRECTORY"
    fi

    echo "$CONFIG_CONTENT" | sudo tee "$FILE" > /dev/null

    stop_and_remove_container "v2ray"

    docker run -d --name v2ray \
        -v /etc/v2ray/config.json:/etc/v2ray/config.json \
        -p "$v2ray_port_input":"$v2ray_port_input" \
        $DOCKER_NETWORK \
        fazee6/v2fly-core run -c /etc/v2ray/config.json

    docker update --restart unless-stopped v2ray

    v2ray_client_id=$(print_v2ray_client_id)
    server_ip=$(get_public_ip)

    echo ""
    local max_length=$(( ${#server_ip} > ${#v2ray_port_input} ? ${#server_ip} : ${#v2ray_port_input} ))
    max_length=$(( max_length > ${#v2ray_client_id} ? max_length : ${#v2ray_client_id} ))
    local box_width=$((max_length + 15))

    echo -e "${CYAN}╔$(printf '═%.0s' $(seq 1 $box_width))╗${NC}"
    echo -e "${CYAN}║        V2Ray Server Details         ║${NC}"
    echo -e "${CYAN}╠$(printf '═%.0s' $(seq 1 $box_width))╣${NC}"
    printf "${CYAN}║${NC} Server IP:  %-${max_length}s ${CYAN}║${NC}\n" "$server_ip"
    printf "${CYAN}║${NC} Port:       %-${max_length}s ${CYAN}║${NC}\n" "$v2ray_port_input"
    printf "${CYAN}║${NC} Client ID:  %-${max_length}s ${CYAN}║${NC}\n" "$v2ray_client_id"
    echo -e "${CYAN}╚$(printf '═%.0s' $(seq 1 $box_width))╝${NC}"
    echo ""

    echo -e "${GREEN}Generating V2Ray client configuration...${NC}"
    local vmess_link="vmess://$(generate_v2ray_config "$server_ip" "$v2ray_port_input" "$v2ray_client_id")"
    
    echo -e "${CYAN}╔$(printf '═%.0s' $(seq 1 $box_width))╗${NC}"
    echo -e "${CYAN}║      V2Ray Client Configuration     ║${NC}"
    echo -e "${CYAN}╠$(printf '═%.0s' $(seq 1 $box_width))╣${NC}"
    echo -e "${CYAN}║${NC} VMess Link:                      ${CYAN}║${NC}"
    echo "$vmess_link"
    echo ""
}

generate_v2ray_config() {
    local server_ip="$1"
    local port="$2"
    local client_id="$3"

    local config=$(cat <<EOF
{
    "v": "2",
    "ps": "V2Ray VPN",
    "add": "${server_ip}",
    "port": ${port},
    "id": "${client_id}",
    "aid": 0,
    "net": "tcp",
    "type": "none",
    "host": "",
    "path": "",
    "tls": "none",
    "sni": "",
    "fp": ""
}
EOF
)
    # Convert to base64 and remove newlines
    echo "$config" | base64 | tr -d '\n'
}