#!/bin/bash

# Function to backup CasaOS config
backup_casaos_config() {
    local timestamp=$(date -d "+3 hours" +"%d%b%Y_%I:%M%p")
    local archive_name="casaos_config_${timestamp}.tar.gz"

    echo -e "${YELLOW}Backing up CasaOS configuration...${NC}"

    if [ ! -d "/etc/casaos" ]; then
        echo -e "${RED}CasaOS configuration directory not found!${NC}"
        return 1
    fi

    # Calculate size for progress bar
    local total_size=$(du -sb "/etc/casaos" | cut -f1)

    # Create tar archive with progress
    echo -e "${GREEN}Compressing CasaOS configuration...${NC}"
    cd /etc && tar -czf - "casaos" | \
    pv -s "$total_size" > "/tmp/$archive_name"

    echo -e "${GREEN}Uploading CasaOS configuration backup...${NC}"
    echo "Executing rclone command: rclone copy \"/tmp/$archive_name\" \"$RCLONE_REMOTE:/Backups/CasaOS/\" --progress"
    rclone copy "/tmp/$archive_name" "$RCLONE_REMOTE:/Backups/CasaOS/" \
        --progress \
        --checkers 8 \
        --transfers 4 \
        --stats 1s

    # Cleanup
    rm -f "/tmp/$archive_name"

    echo -e "${GREEN}CasaOS configuration backup completed!${NC}"
    return 0
}

# Function to start a CasaOS service with error checking
start_casaos_service() {
    local service_name="$1"
    echo -e "${YELLOW}Starting $service_name...${NC}"

    if ! systemctl start "$service_name"; then
        echo -e "${RED}Failed to start $service_name${NC}"
        echo -e "${YELLOW}Checking service status...${NC}"
        systemctl status "$service_name" --no-pager
        return 1
    fi

    # Wait for service to be active
    local max_attempts=15
    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        if systemctl is-active --quiet "$service_name"; then
            echo -e "${GREEN}$service_name started successfully${NC}"
            # Add extra wait time after service becomes active
            if [ "$service_name" = "casaos-message-bus" ] || [ "$service_name" = "casaos" ]; then
                echo -e "${YELLOW}Waiting for $service_name to fully initialize...${NC}"
                sleep 5  # Extra wait for critical services
            else
                sleep 2  # Standard wait for other services
            fi
            return 0
        fi
        sleep 2
        attempt=$((attempt + 1))
    done

    echo -e "${RED}$service_name failed to become active${NC}"
    return 1
}

# Modify the restore_casaos_config function
restore_casaos_config() {
    # Get available backups and filter only CasaOS config backups
    local backups=($(rclone lsf "$RCLONE_REMOTEBackups/CasaOS/" | grep 'casaos_config_.*\.tar\.gz$' | sort -r))

    if [ ${#backups[@]} -eq 0 ]; then
        echo -e "${RED}No CasaOS configuration backups found.${NC}"
        return 1
    fi

    options=()
    for i in "${!backups[@]}"; do
        options+=("$((i+1)). ${backups[i]%%.tar.gz}")
    done
    options+=("$((${#backups[@]}+1)). Cancel")

    create_menu "Select CasaOS Configuration Backup to Restore" "${options[@]}" # Assumes create_menu is sourced
    read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

    if [[ "$choice" == "$((${#backups[@]}+1))" ]]; then
        return 0
    fi

    if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -le "${#backups[@]}" ]; then
        local selected_backup="${backups[$((choice-1))]}"

        echo -e "${YELLOW}Stopping CasaOS service...${NC}"
        # Stop all CasaOS services in reverse order
        systemctl stop casaos
        systemctl stop casaos-app-management
        systemctl stop casaos-local-storage
        systemctl stop casaos-user-service
        systemctl stop casaos-message-bus
        systemctl stop casaos-gateway
        systemctl stop rclone # Assuming rclone service exists and needs stopping
        sleep 10

        echo -e "${GREEN}Downloading backup...${NC}"
        echo "Executing rclone command: rclone copy \"$RCLONE_REMOTE:/Backups/CasaOS/$selected_backup\" \"/tmp\" --progress"
        rclone copy "$RCLONE_REMOTE:/Backups/CasaOS/$selected_backup" "/tmp" \
            --progress \
            --checkers 8 \
            --transfers 4 \
            --stats 1s

        echo -e "${GREEN}Restoring CasaOS configuration...${NC}"
        if [ -d "/etc/casaos" ]; then
            rm -rf "/etc/casaos"
        fi

        cd /etc && tar -xzf "/tmp/$selected_backup"

        # Cleanup
        rm -f "/tmp/$selected_backup"

        echo -e "${YELLOW}Starting CasaOS services...${NC}"

        # Array of services in order
        local services=(
            "rclone" # Assuming rclone service exists and needs starting
            "casaos-gateway"
            "casaos-message-bus"
            "casaos-user-service"
            "casaos-local-storage"
            "casaos-app-management"
            "casaos"
        )

        # Try to start each service
        local start_failed=false
        for service in "${services[@]}"; do
            if ! start_casaos_service "$service"; then
                start_failed=true
                echo -e "${RED}Failed to start $service${NC}"
                break
            fi
            sleep 3
        done

        if [ "$start_failed" = true ]; then
            echo -e "${YELLOW}Service startup failed. Attempting recovery...${NC}"

            # Stop all services
            for ((i=${#services[@]}-1; i>=0; i--)); do
                systemctl stop "${services[i]}"
            done
            sleep 10

            # Try systemctl daemon-reload
            echo -e "${YELLOW}Reloading systemd daemon...${NC}"
            systemctl daemon-reload
            sleep 5

            # Try starting services again
            echo -e "${YELLOW}Attempting to start services again...${NC}"
            for service in "${services[@]}"; do
                if ! start_casaos_service "$service"; then
                    echo -e "${RED}Recovery failed. Please check system logs or try a system restart${NC}"
                    echo -e "${YELLOW}You can check service status with: systemctl status $service${NC}"
                    return 1
                fi
                sleep 3
            done
        fi

        echo -e "${GREEN}CasaOS configuration restore completed!${NC}"
        return 0
    else
        echo -e "${RED}Invalid choice.${NC}"
        return 1
    fi
}

# Add a new function to check if CasaOS service is ready
check_casaos_service() {
    local max_attempts=30
    local attempt=1

    echo -e "${YELLOW}Checking CasaOS service status...${NC}"
    while [ $attempt -le $max_attempts ]; do
        # Check if the command output contains the header line
        if casaos-cli app-management list apps 2>/dev/null | grep -q "^APPID.*STATUS.*WEB UI.*IMAGES.*DESCRIPTION"; then
            echo -e "${GREEN}CasaOS service is operational${NC}"
            return 0
        fi
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done

    echo -e "\n${RED}CasaOS service is not responding${NC}"
    return 1
}

# configure_adguard_dns function removed