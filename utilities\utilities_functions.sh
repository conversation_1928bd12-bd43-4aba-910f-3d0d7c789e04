#!/bin/bash

# Common variables
declare -A PACKAGES=(
    ["gdown"]="python3-pip"
    ["jq"]="jq"
    ["nt"]="net-tools"
    ["nano"]="nano"
    ["dig"]="dnsutils"
    ["qrencode"]="qrencode"
    ["rar"]="unrar"
    ["zip"]="unzip"
    ["cron"]="cron"
    ["bash-completion"]="bash-completion"
    ["pv"]="pv"
)

# Logging function
log_message() {
    local level="$1"
    local message="$2"
    local color=""
    
    case "$level" in
        "INFO") color="$GREEN" ;;
        "WARN") color="$YELLOW" ;;
        "ERROR") color="$RED" ;;
        *) color="$NC" ;;
    esac
    
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $message${NC}"
}

# Error handling function
handle_error() {
    local exit_code=$1
    local error_message=$2
    
    if [ $exit_code -ne 0 ]; then
        log_message "ERROR" "$error_message (Exit code: $exit_code)"
        return 1
    fi
    return 0
}

# Package management function
manage_package() {
    local action="$1"  # install or remove
    local package_name="$2"
    local package_cmd="$3"
    local success=true
    
    case "$action" in
        "install")
            # Special handling for rar/unrar
            if [ "$package_name" = "rar" ]; then
                if ! command -v unrar &> /dev/null; then
                    log_message "INFO" "Installing unrar..."
                    sudo DEBIAN_FRONTEND=noninteractive apt-get install -y unrar || success=false
                    
                    if [ "$success" = true ]; then
                        log_message "INFO" "unrar installed successfully"
                    else
                        log_message "ERROR" "Failed to install unrar"
                        return 1
                    fi
                fi
                return 0
            fi
            
            # Special handling for packages that don't provide commands
            if [ "$package_name" = "bash-completion" ]; then
                if ! dpkg -l | grep -q "^ii.*$package_name"; then
                    log_message "INFO" "Installing $package_name..."
                    sudo DEBIAN_FRONTEND=noninteractive apt-get install -y $package_cmd || success=false
                    
                    if [ "$success" = true ]; then
                        log_message "INFO" "$package_name installed successfully"
                    else
                        log_message "ERROR" "Failed to install $package_name"
                        return 1
                    fi
                fi
            # Regular handling for command-based packages
            elif ! command -v "$package_name" &> /dev/null; then
                log_message "INFO" "Installing $package_name..."
                if [ "$package_name" = "gdown" ]; then
                    sudo DEBIAN_FRONTEND=noninteractive apt-get install -y python3-pip && \
                    pip3 install gdown || success=false
                else
                    sudo DEBIAN_FRONTEND=noninteractive apt-get install -y $package_cmd || success=false
                fi
                
                if [ "$success" = true ]; then
                    log_message "INFO" "$package_name installed successfully"
                else
                    log_message "ERROR" "Failed to install $package_name"
                    return 1
                fi
            fi
            ;;
            
        "remove")
            # Special handling for packages that don't provide commands
            if [ "$package_name" = "bash-completion" ]; then
                if dpkg -l | grep -q "^ii.*$package_name"; then
                    log_message "INFO" "Removing $package_name..."
                    sudo DEBIAN_FRONTEND=noninteractive apt-get remove -y $package_cmd || success=false
                    
                    if [ "$success" = true ]; then
                        log_message "INFO" "$package_name removed successfully"
                    else
                        log_message "ERROR" "Failed to remove $package_name"
                        return 1
                    fi
                fi
            # Regular handling for command-based packages
            elif command -v "$package_name" &> /dev/null; then
                log_message "INFO" "Removing $package_name..."
                if [ "$package_name" = "gdown" ]; then
                    pip3 uninstall -y gdown && \
                    sudo DEBIAN_FRONTEND=noninteractive apt-get remove -y python3-pip || success=false
                elif [ "$package_name" = "cron" ]; then
                    systemctl stop cron
                    systemctl disable cron
                    sudo DEBIAN_FRONTEND=noninteractive apt-get remove -y $package_cmd || success=false
                else
                    sudo DEBIAN_FRONTEND=noninteractive apt-get remove -y $package_cmd || success=false
                fi
                
                if [ "$success" = true ]; then
                    log_message "INFO" "$package_name removed successfully"
                else
                    log_message "ERROR" "Failed to remove $package_name"
                    return 1
                fi
            fi
            ;;
    esac
    return 0
}

install_all_utilities() {
    # Set noninteractive frontend to avoid prompts
    export DEBIAN_FRONTEND=noninteractive

    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║    Installing Utilities       ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    # Update package lists with error handling
    log_message "INFO" "Updating package lists..."
    if ! sudo apt-get update -y --allow-releaseinfo-change; then
        log_message "ERROR" "Failed to update package lists"
        return 1
    fi

    # Configure APT to avoid prompts
    sudo apt-get install -y -o Dpkg::Options::="--force-confdef" -o Dpkg::Options::="--force-confold"

    # Install packages sequentially to avoid dpkg lock conflicts
    local failed=0
    for package in "${!PACKAGES[@]}"; do
        if ! manage_package "install" "$package" "${PACKAGES[$package]}"; then
            ((failed++))
        fi
    done

    if [ $failed -eq 0 ]; then
        log_message "INFO" "All utilities installed successfully"
    else
        log_message "ERROR" "$failed package(s) failed to install"
        return 1
    fi
}

remove_all_utilities() {
    # Set noninteractive frontend to avoid prompts
    export DEBIAN_FRONTEND=noninteractive

    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║    Removing Utilities         ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    # Remove packages sequentially to avoid dpkg lock conflicts
    local failed=0
    for package in "${!PACKAGES[@]}"; do
        if ! manage_package "remove" "$package" "${PACKAGES[$package]}"; then
            ((failed++))
        fi
    done

    # Clean up package management
    log_message "INFO" "Cleaning up package management..."
    sudo DEBIAN_FRONTEND=noninteractive apt-get autoremove -y
    sudo DEBIAN_FRONTEND=noninteractive apt-get clean

    if [ $failed -eq 0 ]; then
        log_message "INFO" "All utilities removed successfully"
    else
        log_message "ERROR" "$failed package(s) failed to remove"
        return 1
    fi
}