#!/bin/bash

# Common variables for both DERP and Headscale
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;31m'
NC='\033[0m' # No Color
DEFAULT_DOMAIN="mcsaguru.com"
CERTBOT_DIR="/opt/certbot"

# Shared variables between DERP and Headscale
SELECTED_DOMAIN=""
CERT_DIR=""
DERP_SUBDOMAIN="derp.mcsaguru.com"
HEADSCALE_SUBDOMAIN="headscale.mcsaguru.com"

# Function to list and select available domains
select_domain() {
    # Get all domain directories, excluding README
    local domains=($(find /etc/letsencrypt/live/ -maxdepth 1 -type d -not -name "README" -not -name "live"))
    
    if [ ${#domains[@]} -eq 0 ]; then
        echo -e "${RED}No SSL certificates found. Please generate one first.${NC}"
        return 1
    fi

    echo -e "${GREEN}Available domains:${NC}"
    for i in "${!domains[@]}"; do
        local domain_name=$(basename "${domains[$i]}")
        echo "[$((i+1))] $domain_name"
    done

    while true; do
        read -rp "Select domain number (1-${#domains[@]}): " choice
        if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le "${#domains[@]}" ]; then
            selected_domain="${domains[$((choice-1))]}"
            echo -e "${GREEN}Selected domain: $(basename "$selected_domain")${NC}"
            return 0
        else
            echo -e "${RED}Invalid choice. Please select a number between 1 and ${#domains[@]}.${NC}"
        fi
    done
}

# Function to extract base domain from certificate directory name
extract_base_domain() {
    local cert_dir_name="$1"
    # Remove any trailing numbers or suffixes (e.g., -0001, -0002)
    echo "$cert_dir_name" | sed -E 's/-[0-9]+$//'
}

# Function to check and setup DERP server
setup_derp_server() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║    Setting Up DERP Server     ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    install_docker
    local derp_subdomain="$1"  # Get DERP subdomain prefix
    local full_domain="${derp_subdomain}.${SELECTED_DOMAIN}"  # Construct full domain

    # Check if container already exists
    if docker ps -a | grep -q "dreper"; then
        echo -e "${YELLOW}Removing existing DERP container...${NC}"
        docker rm -f dreper
    fi

    echo -e "${GREEN}Starting DERP server container...${NC}"
    docker run --restart always \
        --name dreper -p 12345:443 -p 3478:3478/udp \
        -v "${selected_domain}/fullchain.pem:/app/certs/${full_domain}.crt:ro" \
        -v "${selected_domain}/privkey.pem:/app/certs/${full_domain}.key:ro" \
        -e DERP_CERT_MODE=manual \
        -e DERP_DOMAIN="$full_domain" \
        $DOCKER_NETWORK \
        -d fazee6/derp-server

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}DERP server container started successfully!${NC}"
        return 0
    else
        echo -e "${RED}Failed to start DERP server container.${NC}"
        return 1
    fi
}

# New function to setup Headscale server
setup_headscale_server() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║   Setting Up Headscale Server ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    # Check if Headscale exists and handle removal of both services
    if docker ps -a | grep -q "headscale"; then
        echo -e "${YELLOW}A container named 'headscale' already exists.${NC}"
        read -rp "$(echo -e ${YELLOW}"Do you want to remove existing setup and create new? (y/n): "${NC})" choice
        
        case $choice in
            [Yy]*)
                echo -e "${GREEN}Removing existing containers...${NC}"
                # Remove both containers and Headscale data
                docker rm -f headscale dreper 2>/dev/null
                rm -rf /opt/headscale
                
                # Setup DERP server first
                echo -e "${GREEN}Setting up new DERP server...${NC}"
                setup_derp_server || {
                    echo -e "${RED}Failed to setup DERP server. Headscale setup aborted.${NC}"
                    return 1
                }
                ;;
            *)
                echo -e "${YELLOW}Keeping existing container. No changes made.${NC}"
                return
                ;;
        esac
    elif ! docker ps | grep -q "dreper"; then
        # If no Headscale but also no DERP, setup DERP first
        echo -e "${YELLOW}DERP server is not running. Setting up DERP server first...${NC}"
        setup_derp_server || {
            echo -e "${RED}Failed to setup DERP server. Headscale setup aborted.${NC}"
            return 1
        }
    fi

    # Create required directories (in case they don't exist after sync)
    mkdir -p /opt/headscale
    
    restore_headscale_db

    # Create DERP config for Headscale
    create_headscale_derp_config

    # Create Headscale config
    create_headscale_config

    # Run Headscale container
    echo -e "${GREEN}Starting Headscale server container...${NC}"
    docker run -d --name headscale \
        -v /opt/headscale/derp.yaml:/etc/headscale/derp.yaml:ro \
        -v /opt/headscale/config.yaml:/etc/headscale/config.yaml:ro \
        -v /opt/headscale/headscale-db:/var/lib/headscale:rw \
        -v /etc/letsencrypt/:/etc/letsencrypt:ro \
        -p 4443:443 \
        -p 9090:9090 \
        -p 50443:50443 \
        --restart always \
        $DOCKER_NETWORK \
        fazee6/headscale \
        serve

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Headscale server container started successfully!${NC}"
    else
        echo -e "${RED}Failed to start Headscale server container.${NC}"
    fi
}

# Helper function to create Headscale DERP config
create_headscale_derp_config() {
    cat > /opt/headscale/derp.yaml <<EOF
regions:
  900:
    regionid: 900
    regioncode: lv
    regionname: Las Vegas, Nevada
    nodes:
      - name: 900a
        regionid: 900
        hostname: ${DERP_SUBDOMAIN}.${SELECTED_DOMAIN}
        stunport: 3478
        derpport: 12345
EOF
}

# Helper function to create Headscale config
create_headscale_config() {
    cat > /opt/headscale/config.yaml <<EOF
server_url: https://${HEADSCALE_SUBDOMAIN}.${SELECTED_DOMAIN}
listen_addr: 0.0.0.0:443
metrics_listen_addr: 0.0.0.0:9090
grpc_listen_addr: 0.0.0.0:50443
grpc_allow_insecure: false
noise:
  private_key_path: /var/lib/headscale/noise_private.key
prefixes:
  v6: fd7a:115c:a1e0::/48
  v4: **********/10
  allocation: sequential
derp:
  paths:
    - /etc/headscale/derp.yaml
  auto_update_enabled: true
  update_frequency: 24h
disable_check_updates: false
ephemeral_node_inactivity_timeout: 30m
database:
  type: sqlite
  sqlite:
    path: /var/lib/headscale/db.sqlite
    write_ahead_log: true
log:
  format: text
  level: info
dns:
  magic_dns: false
  base_domain: tailnet.${SELECTED_DOMAIN}
  nameservers:
    - *******
    - *******
unix_socket: /var/run/headscale/headscale.sock
unix_socket_permission: "0770"
logtail:
  enabled: false
randomize_client_port: false

tls_cert_path: "/etc/letsencrypt/live/${CERT_DIR}/fullchain.pem"
tls_key_path: "/etc/letsencrypt/live/${CERT_DIR}/privkey.pem"
EOF
}

# Function to check Headscale status
check_headscale_status() {
    if docker ps | grep -q "headscale"; then
        echo -e "${GREEN}Headscale server is running${NC}"
        echo -e "URL: https://${HEADSCALE_SUBDOMAIN}.${SELECTED_DOMAIN}"
        return 0
    else
        echo -e "${RED}Headscale server is not running${NC}"
        return 1
    fi
}

# Function to delete Headscale server
delete_headscale_server() {
    if docker ps -a | grep -q "headscale"; then
        echo -e "${YELLOW}Removing Headscale container...${NC}"
        docker rm -f headscale
        rm -rf /opt/headscale
        echo -e "${GREEN}Headscale server deleted successfully${NC}"
    else
        echo -e "${YELLOW}No Headscale container found${NC}"
    fi
}


backup_headscale_db() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║    Backing Up Headscale DB    ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    if [ ! -d "/opt/headscale/headscale-db" ]; then
        echo -e "${RED}Error: Headscale DB directory not found!${NC}"
        return 1
    fi

    install_rclone

    echo -e "${GREEN}Starting Headscale DB backup...${NC}"
    echo "Executing rclone command: rclone sync /opt/headscale/headscale-db $RCLONE_REMOTE:/Backups/headscale-db --progress"
    if rclone sync /opt/headscale/headscale-db $RCLONE_REMOTE:/Backups/headscale-db --progress; then
        echo -e "${GREEN}Headscale DB backup completed successfully!${NC}"
    else
        echo -e "${RED}Failed to backup Headscale DB${NC}"
        return 1
    fi
}

restore_headscale_db() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║   Restoring Headscale DB      ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    install_rclone

    # Create directory if it doesn't exist
    mkdir -p /opt/headscale/headscale-db

    echo -e "${GREEN}Starting Headscale DB restore...${NC}"
    echo "Executing rclone command: rclone sync $RCLONE_REMOTE:/Backups/headscale-db /opt/headscale/headscale-db --progress"
    if rclone sync $RCLONE_REMOTE:/Backups/headscale-db /opt/headscale/headscale-db --progress; then
        echo -e "${GREEN}Headscale DB restore completed successfully!${NC}"
    else
        echo -e "${RED}Failed to restore Headscale DB${NC}"
        return 1
    fi
}

#Add this in Nginx Proxy Manager

# location / {
#     proxy_pass https://************:4443;
#     proxy_http_version 1.1;
#     proxy_set_header Upgrade $http_upgrade;
#     proxy_set_header Connection "upgrade";
#     proxy_set_header Host $host;
#     proxy_set_header X-Real-IP $remote_addr;
#     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#     proxy_set_header X-Forwarded-Proto $scheme;
#     proxy_buffering off;
#     proxy_ssl_verify off;
# }