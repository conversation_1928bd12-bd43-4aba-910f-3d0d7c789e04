# Encrypt Module

This module provides functionality for encrypting and decrypting folders and files.

## Files

### encrypt.sh
Provides encryption and decryption functionality.

**Key Functions:**
- `encrypt_folder()`: Encrypts a folder with password protection
- `decrypt_and_execute()`: Decrypts and executes content from encrypted files

## Usage

The Encrypt module provides tools for securing sensitive data through encryption.

To access encryption functionality:
1. Select "Encrypt Folder" from the main menu
2. Follow the interactive prompts to encrypt or decrypt content

## Integration with Other Modules

The Encrypt module can be used by any module that needs to securely store or transmit sensitive data. It provides the necessary functionality for encrypting and decrypting data as needed. 