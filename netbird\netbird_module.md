# Netbird Module

This module provides comprehensive functionality for managing, backing up, and restoring Netbird installations. Netbird is a modern VPN solution that uses WireGuard protocol and provides secure peer-to-peer networking.

## Overview

Netbird is an open-source VPN management platform that simplifies secure networking. It consists of several components:
- **Management Service**: Core service that manages peers, users, and policies
- **Signal Service**: Handles peer discovery and connection coordination
- **Dashboard**: Web-based management interface
- **Coturn**: STUN/TURN server for NAT traversal
- **Relay**: Relay server for peer connections when direct connection isn't possible

## Files

### netbird_functions.sh
Core functions for Netbird service management and monitoring.

**Key Functions:**
- `check_netbird_prerequisites()`: Checks system requirements for installation
- `install_netbird()`: Automated installation of Netbird with Zitadel IdP
- `remove_netbird()`: Complete removal of Netbird installation
- `is_netbird_running()`: Checks if Netbird containers are running
- `detect_netbird_installation()`: Automatically detects Netbird installation directory
- `check_netbird_status()`: Displays status of all Netbird services
- `stop_netbird_services()`: Stops all Netbird containers
- `start_netbird_services()`: Starts all Netbird containers
- `restart_netbird_services()`: Restarts Netbird services
- `view_netbird_logs()`: View logs from specific Netbird services
- `get_netbird_container_info()`: Display detailed container information
- `check_netbird_connectivity()`: Test connectivity to Netbird services
- `update_netbird()`: Update Netbird to latest version

### netbird_backup.sh
Functions for backing up and restoring Netbird configurations and data.

**Key Functions:**
- `backup_netbird_config()`: Backs up configuration files (docker-compose.yml, management.json, etc.)
- `backup_netbird_data()`: Backs up management database and persistent data
- `backup_netbird_full()`: Performs complete backup (configuration + data)
- `restore_netbird_config()`: Restores configuration files from backup
- `restore_netbird_data()`: Restores management database from backup
- `restore_netbird_full()`: Performs complete restore (configuration + data)

### netbird_menu.sh
Provides menu interfaces for all Netbird operations.

**Key Functions:**
- `netbird_menu()`: Main menu for Netbird operations
- `netbird_backup_menu()`: Menu for backup operations
- `netbird_restore_menu()`: Menu for restore operations
- `view_backup_status()`: Shows status of available backups
- `list_netbird_backups()`: Lists all available backups in cloud storage
- `show_netbird_info()`: Displays installation information
- `netbird_health_check()`: Performs comprehensive health check

## Features

### Installation and Management
- **Automated Installation**: One-command installation using official Netbird script with Zitadel IdP
- **Prerequisites Check**: Validates system requirements before installation
- **Domain-Only Setup**: Only requires domain name input - all other settings use secure defaults
- **Default Installation Path**: Installs to `/opt/netbird` for consistency
- **Complete Removal**: Safe removal with optional backup before deletion
- **Credential Management**: Automatically displays login credentials after installation

### Backup and Restore
- **Configuration Backup**: Backs up all configuration files including docker-compose.yml, management.json, turnserver.conf, and environment files
- **Data Backup**: Backs up the management service database containing users, peers, policies, and network configurations
- **Full Backup**: Combines both configuration and data backup for complete protection
- **Cloud Storage**: Integrates with rclone for automatic cloud backup storage
- **Incremental Restore**: Allows restoring configuration and data separately or together

### Service Management
- **Auto-Detection**: Automatically detects Netbird installation location
- **Service Control**: Start, stop, and restart individual or all Netbird services
- **Status Monitoring**: Real-time status checking of all Netbird components
- **Log Viewing**: Access logs from any Netbird service
- **Health Checks**: Comprehensive connectivity and service health monitoring

### Maintenance
- **Update Management**: Easy updating to latest Netbird versions with automatic backup
- **Container Management**: View and manage Netbird Docker containers
- **Connectivity Testing**: Test all Netbird service endpoints and ports

## Usage

### Accessing Netbird Functionality
1. Select "Netbird Menu" from the main menu
2. Choose from installation, service management, backup, or restore operations
3. Follow the interactive prompts

### Installation Process
1. **Install Netbird**: Select option 1 from the Netbird menu
2. **Enter Domain**: Provide your domain name (e.g., netbird.yourdomain.com)
3. **Automatic Setup**: The script handles all configuration automatically
4. **Access Credentials**: Login credentials are displayed after installation
5. **Default Location**: Installation is placed in `/opt/netbird`

### Removal Process
1. **Remove Netbird**: Select option 2 from the Netbird menu
2. **Backup Option**: Choose whether to backup before removal
3. **Confirmation**: Confirm removal (this action cannot be undone)
4. **Complete Cleanup**: All containers, images, and files are removed

### Backup Operations
1. **Configuration Only**: Backs up docker-compose.yml, management.json, and other config files
2. **Data Only**: Backs up the management service database
3. **Full Backup**: Backs up both configuration and data

### Restore Operations
1. **Configuration Only**: Restores configuration files to specified directory
2. **Data Only**: Restores management database (requires existing installation)
3. **Full Restore**: Restores both configuration and data

### Service Management
- Check status of all Netbird services
- Start/stop/restart services as needed
- View logs for troubleshooting
- Monitor connectivity and health

## Requirements

### System Requirements
- Docker and Docker Compose installed
- Sufficient disk space for backups
- Network access for cloud backup storage

### Dependencies
- **Rclone**: For cloud storage backup/restore operations
- **Compression tools**: tar, gzip for creating backup archives
- **Network tools**: netstat, curl for connectivity testing

## Integration with Other Modules

The Netbird module integrates with:
- **Rclone module**: For cloud storage operations
- **File Operations module**: For compression and decompression
- **Utilities module**: For common system utilities
- **Docker module**: For container management

## Backup Storage Structure

Backups are stored in the cloud storage under:
```
/Backups/Netbird/
├── netbird_config_backup.tar.gz    # Configuration files
└── netbird_data_backup.tar.gz      # Management database and data
```

## Security Considerations

- Backups contain sensitive network configuration data
- Management database includes user information and network policies
- Ensure cloud storage is properly secured
- Consider encrypting backups for additional security

## Troubleshooting

### Common Issues
1. **Installation Not Detected**: Ensure docker-compose.yml exists and contains Netbird services
2. **Backup Failures**: Check rclone configuration and cloud storage access
3. **Service Start Issues**: Verify port availability and Docker daemon status
4. **Connectivity Problems**: Check firewall settings and port configurations

### Health Check
Use the health check function to diagnose common issues:
- Installation detection
- Service status
- Port accessibility
- API connectivity

## Support

For additional help:
- Check Netbird official documentation
- Review Docker container logs
- Use the built-in health check and connectivity tests
- Verify rclone configuration for backup operations
