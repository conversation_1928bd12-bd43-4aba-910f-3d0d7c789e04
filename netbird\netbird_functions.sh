#!/bin/bash

# Function to check if Netbird containers are running
is_netbird_running() {
    local containers=("management" "signal" "dashboard" "coturn" "relay")
    local running_count=0
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "^${container}$\|_${container}_\|${container}_"; then
            ((running_count++))
        fi
    done
    
    # Return true if at least 3 core containers are running (management, signal, dashboard)
    if [ $running_count -ge 3 ]; then
        return 0
    else
        return 1
    fi
}

# Function to detect Netbird installation directory
detect_netbird_installation() {
    local possible_paths=(
        "/opt/netbird"
        "/home/<USER>/netbird"
        "/root/netbird"
        "$(pwd)/netbird"
        "/var/lib/netbird"
        "/usr/local/netbird"
    )
    
    for path in "${possible_paths[@]}"; do
        if [ -d "$path" ] && [ -f "$path/docker-compose.yml" ]; then
            echo "$path"
            return 0
        fi
    done
    
    # Try to find docker-compose.yml with netbird services
    local compose_files=$(find / -name "docker-compose.yml" -type f 2>/dev/null | head -20)
    for file in $compose_files; do
        if grep -q "netbird\|management\|signal" "$file" 2>/dev/null; then
            echo "$(dirname "$file")"
            return 0
        fi
    done
    
    return 1
}

# Function to check status of all Netbird services
check_netbird_status() {
    echo "Checking Netbird services status..."
    echo "=================================="
    
    local containers=("management" "signal" "dashboard" "coturn" "relay")
    local all_running=true
    
    for container in "${containers[@]}"; do
        echo -n "Checking $container: "
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "^${container}$\|_${container}_\|${container}_"; then
            local status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep "${container}" | awk '{print $2}')
            echo -e "${GREEN}Running${NC} ($status)"
        else
            echo -e "${RED}Not Running${NC}"
            all_running=false
        fi
    done
    
    echo "=================================="
    if $all_running; then
        echo -e "${GREEN}All Netbird services are running${NC}"
    else
        echo -e "${YELLOW}Some Netbird services are not running${NC}"
    fi
    
    return 0
}

# Function to stop Netbird services
stop_netbird_services() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Cannot stop services."
        return 1
    fi
    
    echo "Stopping Netbird services..."
    cd "$netbird_path"
    
    if [ -f "docker-compose.yml" ]; then
        docker compose stop
        echo "Netbird services stopped."
        return 0
    else
        echo "docker-compose.yml not found in $netbird_path"
        return 1
    fi
}

# Function to start Netbird services
start_netbird_services() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Cannot start services."
        return 1
    fi
    
    echo "Starting Netbird services..."
    cd "$netbird_path"
    
    if [ -f "docker-compose.yml" ]; then
        docker compose up -d
        echo "Netbird services started."
        return 0
    else
        echo "docker-compose.yml not found in $netbird_path"
        return 1
    fi
}

# Function to restart Netbird services
restart_netbird_services() {
    echo "Restarting Netbird services..."
    
    if stop_netbird_services; then
        sleep 5
        if start_netbird_services; then
            echo "Netbird services restarted successfully."
            return 0
        fi
    fi
    
    echo "Failed to restart Netbird services."
    return 1
}

# Function to view Netbird logs
view_netbird_logs() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi
    
    cd "$netbird_path"
    
    echo "Select which service logs to view:"
    echo "1. Management"
    echo "2. Signal"
    echo "3. Dashboard"
    echo "4. Coturn"
    echo "5. Relay"
    echo "6. All services"
    
    read -rp "Enter your choice (1-6): " log_choice
    
    case $log_choice in
        1) docker compose logs management ;;
        2) docker compose logs signal ;;
        3) docker compose logs dashboard ;;
        4) docker compose logs coturn ;;
        5) docker compose logs relay ;;
        6) docker compose logs ;;
        *) echo "Invalid choice." ;;
    esac
}

# Function to get Netbird container info
get_netbird_container_info() {
    echo "Netbird Container Information:"
    echo "============================="
    
    local containers=("management" "signal" "dashboard" "coturn" "relay")
    
    for container in "${containers[@]}"; do
        echo "--- $container ---"
        docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}" | grep "${container}" || echo "Container not found"
        echo
    done
}

# Function to check Netbird network connectivity
check_netbird_connectivity() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi
    
    echo "Checking Netbird connectivity..."
    echo "==============================="
    
    # Check if management API is accessible
    echo "Testing Management API..."
    if curl -s -k https://localhost:443/api/status >/dev/null 2>&1; then
        echo -e "${GREEN}Management API: Accessible${NC}"
    else
        echo -e "${RED}Management API: Not accessible${NC}"
    fi
    
    # Check if signal service is accessible
    echo "Testing Signal service..."
    if netstat -tuln | grep -q ":33073"; then
        echo -e "${GREEN}Signal service: Port 33073 is open${NC}"
    else
        echo -e "${RED}Signal service: Port 33073 is not accessible${NC}"
    fi
    
    # Check if dashboard is accessible
    echo "Testing Dashboard..."
    if curl -s http://localhost:80 >/dev/null 2>&1; then
        echo -e "${GREEN}Dashboard: Accessible${NC}"
    else
        echo -e "${RED}Dashboard: Not accessible${NC}"
    fi
    
    # Check TURN server
    echo "Testing TURN server..."
    if netstat -tuln | grep -q ":3478"; then
        echo -e "${GREEN}TURN server: Port 3478 is open${NC}"
    else
        echo -e "${RED}TURN server: Port 3478 is not accessible${NC}"
    fi
}

# Function to update Netbird installation
update_netbird() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi
    
    echo "Updating Netbird installation..."
    echo "==============================="
    
    # Backup before update
    read -rp "Would you like to create a backup before updating? (y/n): " backup_choice
    if [[ "$backup_choice" =~ ^[Yy]$ ]]; then
        if ! backup_netbird_full; then
            echo "Backup failed. Update cancelled."
            return 1
        fi
    fi
    
    cd "$netbird_path"
    
    # Pull latest images
    echo "Pulling latest Netbird images..."
    docker compose pull
    
    # Restart with new images
    echo "Restarting services with new images..."
    docker compose up -d --force-recreate
    
    echo "Netbird update completed."
    return 0
}
