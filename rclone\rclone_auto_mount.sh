auto_mount() {
    echo -e "${<PERSON><PERSON>AN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Rclone Auto Mount        ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    install_rclone
    mount_cleanup

    # Ask user how many remotes they want to mount
    read -p "$(echo -e ${YELLOW}"How many remotes do you want to mount? "${NC})" num_remotes

    # Create arrays to store remote names and mount points
    declare -a remote_names
    declare -a mount_points

    # Get remote names and mount points from user
    for ((i=1; i<=num_remotes; i++)); do
        read -p "$(echo -e ${YELLOW}"Enter the name of remote $i (e.g., Gunion34P1:Personal): "${NC})" remote_name
        remote_names+=("$remote_name")
        
        read -p "$(echo -e ${YELLOW}"Enter the mount point for $remote_name: "${NC})" mount_point
        mount_points+=("$mount_point")
    done

    # Display the selection menu
    echo -e "${YELLOW}Which remotes do you want to mount? (Select multiple by space, e.g., '1 2 3')${NC}"
    for i in "${!remote_names[@]}"; do
        echo -e "${CYAN}$((i + 1))) ${remote_names[i]} -> ${mount_points[i]}${NC}"
    done

    # Read user input
    read -p "$(echo -e ${YELLOW}"Please enter your choice: "${NC})" user_input

    # Parse user input into an array of indices
    selected_remotes=()
    for index in $user_input; do
        # Convert to zero-based index
        zero_based_index=$((index - 1))
        if [[ $zero_based_index -ge 0 && $zero_based_index -lt ${#remote_names[@]} ]]; then
            selected_remotes+=("$zero_based_index")
        else
            echo -e "${RED}Invalid choice: $index. Please select valid numbers.${NC}"
        fi
    done

    # Ensure rclone is installed once at the beginning
    if ! command -v rclone &> /dev/null; then
        echo -e "${RED}rclone is not installed. Please install it first.${NC}"
        return 1  # Indicate failure
    fi

    # Check and install libfuse3-3 only once 
    if ! dpkg -s libfuse3-3 &> /dev/null; then
        echo -e "${YELLOW}Installing libfuse3-3...${NC}"
        sudo apt install -y libfuse3-3 
    fi

    for index in "${selected_remotes[@]}"; do
        local remote_name="${remote_names[$index]}"
        local mount_point="${mount_points[$index]}"

        # Check if the mount point already exists
        if [ -d "$mount_point" ]; then
            echo -e "${YELLOW}Mount point $mount_point already exists.${NC}"
            
            # Check if it's already mounted
            if mount | grep -q "^$remote_name on $mount_point type fuse"; then
                echo -e "${GREEN}$remote_name is already mounted at $mount_point. Skipping.${NC}"
                continue
            fi
        else
            mkdir -p "$mount_point"
        fi

        # Create a unique service name
        service_name="rclone-mount-${remote_name//[:\/]/-}.service"
        
        # Check if the service already exists
        if [ -f "/etc/systemd/system/$service_name" ]; then
            echo -e "${YELLOW}Service $service_name already exists.${NC}"
            
            # Check if the service is active
            if systemctl is-active --quiet "$service_name"; then
                echo -e "${GREEN}Service $service_name is already active. Skipping.${NC}"
                continue
            else
                echo -e "${YELLOW}Service $service_name exists but is not active. Starting it.${NC}"
                sudo systemctl start "$service_name"
                continue
            fi
        fi

        # Create the systemd service file
        echo -e "${YELLOW}Creating systemd service file for $remote_name...${NC}"
        sudo tee /etc/systemd/system/$service_name > /dev/null <<EOF
[Unit]
Description=Rclone Mount Service for $remote_name (Video Streaming)
After=network-online.target fuse3
Requires=fuse3

[Service]
Type=notify
Environment="HOME=/root"
ExecStart=/usr/bin/rclone mount $remote_name $mount_point \\
  --vfs-cache-mode full \\
  --vfs-cache-max-size 20G \\
  --vfs-cache-max-age 10m \\
  --buffer-size 128M \\
  --dir-cache-time 1000h \\
  --poll-interval 120m \\
  --allow-other \\
  --vfs-read-ahead 512M \\
  --vfs-read-chunk-size 64M \\
  --vfs-read-chunk-size-limit 1G \\
  --transfers 8 \\
  --checkers 8
Restart=on-failure

[Install]
WantedBy=default.target
EOF
       
        sudo systemctl daemon-reload
        sudo systemctl enable --now "$service_name"
        echo -e "${GREEN}$remote_name is now mounted at $mount_point${NC}"
    done

    echo -e "${GREEN}Auto mount process completed.${NC}"
}