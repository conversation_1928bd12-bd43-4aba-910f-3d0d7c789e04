#!/bin/bash

# Main Netbird menu function
netbird_menu() {
    while true; do
        options=(
            "01. Check Netbird Status"
            "02. Start Netbird Services"
            "03. Stop Netbird Services"
            "04. Restart Netbird Services"
            "05. View Netbird Logs"
            "06. Container Information"
            "07. Check Connectivity"
            "08. Health Check"
            "09. Installation Info"
            "10. Backup Menu"
            "11. Restore Menu"
            "12. Update Netbird"
            "13. Back to Main Menu"
        )

        create_menu "Netbird Management Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) check_netbird_status ;;
            2) start_netbird_services ;;
            3) stop_netbird_services ;;
            4) restart_netbird_services ;;
            5) view_netbird_logs ;;
            6) get_netbird_container_info ;;
            7) check_netbird_connectivity ;;
            8) netbird_health_check ;;
            9) show_netbird_info ;;
            10) netbird_backup_menu ;;
            11) netbird_restore_menu ;;
            12) update_netbird ;;
            13) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo  # Add a blank line for better readability
    done
}

# Netbird backup menu
netbird_backup_menu() {
    while true; do
        options=(
            "01. Backup Configuration Only"
            "02. Backup Data Only"
            "03. Full Backup (Config + Data)"
            "04. View Backup Status"
            "05. Back to Netbird Menu"
        )

        create_menu "Netbird Backup Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) 
                echo "Starting configuration backup..."
                if backup_netbird_config; then
                    echo -e "${GREEN}Configuration backup completed successfully.${NC}"
                else
                    echo -e "${RED}Configuration backup failed.${NC}"
                fi
                ;;
            2) 
                echo "Starting data backup..."
                if backup_netbird_data; then
                    echo -e "${GREEN}Data backup completed successfully.${NC}"
                else
                    echo -e "${RED}Data backup failed.${NC}"
                fi
                ;;
            3) 
                echo "Starting full backup..."
                if backup_netbird_full; then
                    echo -e "${GREEN}Full backup completed successfully.${NC}"
                else
                    echo -e "${RED}Full backup failed.${NC}"
                fi
                ;;
            4) view_backup_status ;;
            5) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo  # Add a blank line for better readability
    done
}

# Netbird restore menu
netbird_restore_menu() {
    while true; do
        options=(
            "01. Restore Configuration Only"
            "02. Restore Data Only"
            "03. Full Restore (Config + Data)"
            "04. List Available Backups"
            "05. Back to Netbird Menu"
        )

        create_menu "Netbird Restore Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) 
                echo "Starting configuration restore..."
                if restore_netbird_config; then
                    echo -e "${GREEN}Configuration restore completed successfully.${NC}"
                else
                    echo -e "${RED}Configuration restore failed.${NC}"
                fi
                ;;
            2) 
                echo "Starting data restore..."
                if restore_netbird_data; then
                    echo -e "${GREEN}Data restore completed successfully.${NC}"
                else
                    echo -e "${RED}Data restore failed.${NC}"
                fi
                ;;
            3) 
                echo "Starting full restore..."
                if restore_netbird_full; then
                    echo -e "${GREEN}Full restore completed successfully.${NC}"
                else
                    echo -e "${RED}Full restore failed.${NC}"
                fi
                ;;
            4) list_netbird_backups ;;
            5) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo  # Add a blank line for better readability
    done
}

# Function to view backup status
view_backup_status() {
    echo "Netbird Backup Status"
    echo "===================="
    
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"
    
    # Check if rclone is installed
    if ! install_rclone; then
        echo "Rclone is not available. Cannot check backup status."
        return 1
    fi
    
    echo "Checking cloud storage for backups..."
    
    # List backups in cloud storage
    if rclone ls "$rclone_source" 2>/dev/null | grep -q "netbird"; then
        echo "Available backups in cloud storage:"
        echo "-----------------------------------"
        rclone ls "$rclone_source" | grep "netbird" | while read -r size file; do
            echo "File: $file (Size: $size bytes)"
        done
    else
        echo "No Netbird backups found in cloud storage."
    fi
    
    # Check local backup directory
    local backup_dir="/opt/netbird_backups"
    if [ -d "$backup_dir" ] && [ "$(ls -A "$backup_dir" 2>/dev/null)" ]; then
        echo ""
        echo "Local backup files:"
        echo "------------------"
        ls -la "$backup_dir"
    else
        echo ""
        echo "No local backup files found."
    fi
}

# Function to list available Netbird backups
list_netbird_backups() {
    echo "Available Netbird Backups"
    echo "========================="
    
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"
    
    # Check if rclone is installed
    if ! install_rclone; then
        echo "Rclone is not available. Cannot list backups."
        return 1
    fi
    
    echo "Listing backups from cloud storage..."
    
    # List all files in the Netbird backup directory
    if rclone lsl "$rclone_source" 2>/dev/null; then
        echo ""
        echo "To restore a specific backup, use the restore menu options above."
    else
        echo "No backups found or unable to access cloud storage."
        echo "Please check your rclone configuration."
    fi
}

# Function to show Netbird installation info
show_netbird_info() {
    echo "Netbird Installation Information"
    echo "==============================="
    
    local netbird_path=$(detect_netbird_installation)
    
    if [ -n "$netbird_path" ]; then
        echo "Installation found at: $netbird_path"
        echo ""
        
        # Show configuration files
        echo "Configuration files:"
        cd "$netbird_path"
        for file in docker-compose.yml management.json turnserver.conf setup.env Caddyfile; do
            if [ -f "$file" ]; then
                echo "  ✓ $file"
            else
                echo "  ✗ $file (missing)"
            fi
        done
        
        echo ""
        echo "Docker containers:"
        docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" | grep -E "management|signal|dashboard|coturn|relay" || echo "No Netbird containers found"
        
    else
        echo "Netbird installation not detected."
        echo ""
        echo "Common installation locations:"
        echo "  - /opt/netbird"
        echo "  - /home/<USER>/netbird"
        echo "  - /root/netbird"
        echo ""
        echo "If Netbird is installed elsewhere, please ensure:"
        echo "  1. docker-compose.yml exists in the installation directory"
        echo "  2. The compose file contains Netbird services"
    fi
}

# Function to perform Netbird health check
netbird_health_check() {
    echo "Netbird Health Check"
    echo "==================="
    
    local health_score=0
    local max_score=10
    
    # Check if installation is detected
    echo -n "Installation detection: "
    if detect_netbird_installation >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Found${NC}"
        ((health_score++))
    else
        echo -e "${RED}✗ Not found${NC}"
    fi
    
    # Check if services are running
    echo -n "Services running: "
    if is_netbird_running; then
        echo -e "${GREEN}✓ Running${NC}"
        ((health_score += 3))
    else
        echo -e "${RED}✗ Not running${NC}"
    fi
    
    # Check connectivity
    echo -n "Management API: "
    if curl -s -k https://localhost:443/api/status >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Accessible${NC}"
        ((health_score += 2))
    else
        echo -e "${RED}✗ Not accessible${NC}"
    fi
    
    echo -n "Signal service: "
    if netstat -tuln | grep -q ":33073"; then
        echo -e "${GREEN}✓ Port open${NC}"
        ((health_score += 2))
    else
        echo -e "${RED}✗ Port not open${NC}"
    fi
    
    echo -n "Dashboard: "
    if curl -s http://localhost:80 >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Accessible${NC}"
        ((health_score++))
    else
        echo -e "${RED}✗ Not accessible${NC}"
    fi
    
    echo -n "TURN server: "
    if netstat -tuln | grep -q ":3478"; then
        echo -e "${GREEN}✓ Port open${NC}"
        ((health_score++))
    else
        echo -e "${RED}✗ Port not open${NC}"
    fi
    
    echo ""
    echo "Health Score: $health_score/$max_score"
    
    if [ $health_score -ge 8 ]; then
        echo -e "${GREEN}Status: Excellent${NC}"
    elif [ $health_score -ge 6 ]; then
        echo -e "${YELLOW}Status: Good${NC}"
    elif [ $health_score -ge 4 ]; then
        echo -e "${YELLOW}Status: Fair${NC}"
    else
        echo -e "${RED}Status: Poor${NC}"
    fi
}
