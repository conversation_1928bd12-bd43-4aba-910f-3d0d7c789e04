# OpenWeb UI Module

This module provides functionality for setting up and managing OpenWeb UI, a web-based interface for AI models, along with LiteLLM, a lightweight proxy for Large Language Models.

## Files

### openweb_ui_functions.sh
Core functions for OpenWeb UI management.

**Key Functions:**
- `check_container_exists()`: Checks if a container exists
- `check_container_running()`: Checks if a container is running
- `stop_and_remove_container()`: Stops and removes container
- `check_docker()`: Verifies Docker is installed and running
- `check_port_in_use()`: Checks if a port is in use
- `prompt_for_port()`: Prompts for port selection
- `install_openwebui()`: Installs OpenWeb UI
- `check_openwebui_status()`: Checks OpenWeb UI status
- `start_openwebui()`: Starts OpenWeb UI container
- `stop_openwebui()`: Stops OpenWeb UI container
- `remove_openwebui()`: Removes OpenWeb UI container and data
- `is_openwebui_installed()`: Checks if OpenWeb UI is installed
- `backup_openwebui()`: Backs up OpenWeb UI data
- `restore_openwebui()`: Restores OpenWeb UI from backup

### litellm_functions.sh
Functions for LiteLLM management, which is used as a proxy for language models.

**Key Functions:**
- `install_litellm()`: Installs LiteLLM
- `check_litellm_status()`: Checks LiteLLM status
- `start_litellm()`: Starts LiteLLM container
- `stop_litellm()`: Stops LiteLLM container
- `restart_litellm()`: Restarts LiteLLM container
- `check_litellm_logs()`: Checks LiteLLM logs
- `troubleshoot_litellm()`: Troubleshoots LiteLLM issues
- `remove_litellm()`: Removes LiteLLM container and data
- `fix_litellm_token_issue()`: Fixes token issues in LiteLLM
- `update_litellm_version()`: Updates LiteLLM version
- `is_litellm_installed()`: Checks if LiteLLM is installed
- `backup_litellm()`: Backs up LiteLLM data
- `restore_litellm()`: Restores LiteLLM from backup

### openweb_ui_menu.sh
Provides menu interfaces for OpenWeb UI and LiteLLM operations.

**Key Functions:**
- `openweb_ui_menu()`: Main menu for OpenWeb UI operations
- `openwebui_submenu()`: Submenu for OpenWeb UI operations
- `litellm_submenu()`: Submenu for LiteLLM operations
- `backup_both_apps()`: Backs up both OpenWeb UI and LiteLLM
- `restore_both_apps()`: Restores both OpenWeb UI and LiteLLM
- `install_both_apps()`: Installs both OpenWeb UI and LiteLLM
- `remove_both_apps()`: Removes both OpenWeb UI and LiteLLM
- `show_both_apps_status()`: Shows status of both apps

## Usage

The OpenWeb UI module provides functionality for setting up and managing a web-based interface for AI models.

To access OpenWeb UI functionality:
1. Select "OpenWeb UI Menu" from the main menu
2. Choose from installation, configuration, backup, or management options
3. Follow the interactive prompts to manage your OpenWeb UI and LiteLLM environment

## Integration with Other Modules

The OpenWeb UI module integrates with:
- Docker module for container management
- File Operations module for backup and restore operations 