#!/bin/bash

socket5_gluetun_menu() {
    options=(
        "01. Install SOCKS5 Proxy"
        "02. Remove SOCKS5 Proxy"
        "03. Return to Main Menu"
        "04. Exit"
    )

    while true; do
        create_menu "SOCKS5 Gluetun Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) install_socks5_proxy ;;
            2) remove_socks5_proxy ;;
            3) return ;;
            4) exit ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done
}