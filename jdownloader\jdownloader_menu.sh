jdownloader_menu() {
    options=(
        "01. Add New JDownloader Container"
        "02. Remove JDownloader Container"
        "03. Return to Main Menu"
        "04. Exit"
    )

    while true; do
        create_menu "JDownloader Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) start_jdownloader ;;
            2) remove_jdownloader ;;
            3) return ;;
            4) exit ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done
}