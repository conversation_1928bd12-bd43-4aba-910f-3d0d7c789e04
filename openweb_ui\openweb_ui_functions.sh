#!/bin/bash

# Check if a container exists by name
check_container_exists() {
    docker ps -a --format '{{.Names}}' | grep -q "^$1$"
}

# Check if a container is running by name
check_container_running() {
    docker ps --format '{{.Names}}' | grep -q "^$1$"
}

# Stop and remove container
stop_and_remove_container() {
    echo -e "${YELLOW}Stopping and removing existing $1 container...${NC}"
    docker stop "$1" && docker rm "$1"
}

# Function to check if Docker is properly installed and running
check_docker() {
    # Check if Docker binary exists and is executable
    if ! command -v docker >/dev/null 2>&1 || ! [ -x "$(command -v docker)" ]; then
        return 1
    fi
    
    # Check if Docker daemon is running
    if ! systemctl is-active --quiet docker; then
        # Try to start Docker if it's not running
        sudo systemctl start docker >/dev/null 2>&1
        sleep 2
        if ! systemctl is-active --quiet docker; then
            return 1
        fi
    fi
    
    # Verify Dock<PERSON> is working by running a test command
    if ! docker version >/dev/null 2>&1; then
        return 1
    fi

    # Check if my_network network exists, if not create it
    if ! docker network inspect my_network >/dev/null 2>&1; then
        echo -e "${YELLOW}Creating my_network network...${NC}"
        if ! sudo docker network create --label com.docker.compose.network=my_network my_network >/dev/null 2>&1; then
            echo -e "${RED}Failed to create my_network network${NC}"
            return 1
        fi
        echo -e "${GREEN}my_network network created successfully${NC}"
    fi
    
    return 0
}

# Check if a port is in use
check_port_in_use() {
    local port=$1
    if lsof -i :"$port" >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is not in use
    fi
}

# Prompt for port with availability check
prompt_for_port() {
    local default_port=$1
    local port_description=$2
    local port

    while true; do
        read -rp "$(echo -e ${YELLOW}"Enter port for $port_description (default: $default_port): "${NC})" port
        port=${port:-$default_port}

        if ! [[ $port =~ ^[0-9]+$ ]]; then
            echo -e "${RED}Port must be a number. Please try again.${NC}"
            continue
        fi

        if check_port_in_use "$port"; then
            echo -e "${RED}Port $port is already in use. Please choose another port.${NC}"
            continue
        fi

        echo "$port"
        break
    done
}

# Install and run OpenWebUI
install_openwebui() {
    echo -e "${GREEN}Setting up OpenWebUI...${NC}"
    
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        read -rp "$(echo -e ${YELLOW}"Do you want to install Docker? (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            install_docker
        else
            echo -e "${YELLOW}Operation cancelled. Docker is required to run OpenWebUI.${NC}"
            return 1
        fi
    fi
    
    # Check if container already exists
    if check_container_exists "openwebui"; then
        echo -e "${YELLOW}OpenWebUI container already exists.${NC}"
        read -rp "$(echo -e ${YELLOW}"Do you want to remove it and create a new one? (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            stop_and_remove_container "openwebui"
        else
            echo -e "${YELLOW}Operation cancelled.${NC}"
            return 1
        fi
    fi
    
    # Prompt for port
    local port=$(prompt_for_port 8095 "OpenWebUI")
    
    echo -e "${GREEN}Creating OpenWebUI container...${NC}"
    if docker run -d --name openwebui $DOCKER_NETWORK -p $port:8080 -v open-webui:/app/backend/data ghcr.io/open-webui/open-webui:main; then
        echo -e "${GREEN}OpenWebUI installed successfully!${NC}"
        echo -e "${GREEN}Access it at http://localhost:$port${NC}"
        
        # Only prompt for LiteLLM installation if SKIP_LITELLM_PROMPT is not set
        if [ -z "${SKIP_LITELLM_PROMPT}" ]; then
            # Check if LiteLLM is already installed
            if ! is_litellm_installed; then
                echo -e "${YELLOW}OpenWebUI works best with LiteLLM for managing AI models.${NC}"
                read -rp "$(echo -e ${YELLOW}"Would you like to install LiteLLM now? (y/n): "${NC})" choice
                if [[ $choice =~ ^[Yy]$ ]]; then
                    install_litellm
                else
                    echo -e "${YELLOW}You can install LiteLLM later from the menu if needed.${NC}"
                fi
            else
                echo -e "${GREEN}LiteLLM is already installed. OpenWebUI can connect to it.${NC}"
            fi
        fi
        
        return 0
    else
        echo -e "${RED}Failed to install OpenWebUI.${NC}"
        return 1
    fi
}

# Check OpenWebUI status
check_openwebui_status() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Check if OpenWebUI is installed
    if ! is_openwebui_installed; then
        echo -e "${RED}OpenWebUI is not installed.${NC}"
        read -rp "$(echo -e ${YELLOW}"Would you like to install OpenWebUI now? (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            install_openwebui
            return $?
        else
            echo -e "${YELLOW}You can install it later from the menu.${NC}"
            return 1
        fi
    fi
    
    if check_container_running "openwebui"; then
        local container_id=$(docker ps -f "name=openwebui" --format "{{.ID}}")
        local port_mapping=$(docker port $container_id | grep "8080/tcp")
        local port=$(echo "$port_mapping" | cut -d'-' -f1 | cut -d':' -f2)
        
        echo -e "${GREEN}OpenWebUI is running!${NC}"
        echo -e "${GREEN}Access it at http://localhost:$port${NC}"
        
        # Show connection information for LiteLLM if it's installed
        if is_litellm_installed; then
            echo -e "${YELLOW}To connect OpenWebUI to LiteLLM:${NC}"
            echo -e "${YELLOW}1. Go to http://localhost:$port${NC}"
            echo -e "${YELLOW}2. Navigate to Settings > Models > Add Model Provider${NC}"
            echo -e "${YELLOW}3. Enter the following details:${NC}"
            echo -e "${YELLOW}   - URL: http://localhost:4000${NC}"
            echo -e "${YELLOW}   - API Key: Bearer_fazee${NC}"
        fi
    else
        if check_container_exists "openwebui"; then
            echo -e "${YELLOW}OpenWebUI is installed but not running.${NC}"
            read -rp "$(echo -e ${YELLOW}"Would you like to start it now? (y/n): "${NC})" choice
            if [[ $choice =~ ^[Yy]$ ]]; then
                start_openwebui
            fi
        else
            echo -e "${RED}OpenWebUI is not installed.${NC}"
        fi
    fi
}

# Start OpenWebUI if it exists but is not running
start_openwebui() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Check if OpenWebUI is installed
    if ! is_openwebui_installed; then
        echo -e "${RED}OpenWebUI is not installed. Please install it first.${NC}"
        read -rp "$(echo -e ${YELLOW}"Would you like to install OpenWebUI now? (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            install_openwebui
            return $?
        else
            echo -e "${YELLOW}Operation cancelled.${NC}"
            return 1
        fi
    fi
    
    if check_container_exists "openwebui" && ! check_container_running "openwebui"; then
        echo -e "${GREEN}Starting OpenWebUI...${NC}"
        if docker start openwebui; then
            check_openwebui_status
        else
            echo -e "${RED}Failed to start OpenWebUI.${NC}"
            return 1
        fi
    else
        if check_container_running "openwebui"; then
            echo -e "${YELLOW}OpenWebUI is already running.${NC}"
            check_openwebui_status
        else
            echo -e "${RED}OpenWebUI container doesn't exist. Please install it first.${NC}"
            return 1
        fi
    fi
}

# Stop OpenWebUI if it's running
stop_openwebui() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    if check_container_running "openwebui"; then
        echo -e "${YELLOW}Stopping OpenWebUI...${NC}"
        if docker stop openwebui; then
            echo -e "${GREEN}OpenWebUI stopped successfully.${NC}"
        else
            echo -e "${RED}Failed to stop OpenWebUI.${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}OpenWebUI is not running.${NC}"
    fi
}

# Remove OpenWebUI container and volume
remove_openwebui() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    if check_container_exists "openwebui"; then
        echo -e "${YELLOW}Removing OpenWebUI...${NC}"
        
        # Stop the container if it's running
        if check_container_running "openwebui"; then
            docker stop openwebui >/dev/null 2>&1
        fi
        
        # Remove the container
        if docker rm openwebui >/dev/null 2>&1; then
            echo -e "${GREEN}OpenWebUI container removed.${NC}"
        else
            echo -e "${RED}Failed to remove OpenWebUI container.${NC}"
            return 1
        fi
        
        # Ask user if they want to remove the data volume
        read -rp "$(echo -e ${YELLOW}"Do you want to remove OpenWebUI data volume? All data will be lost. (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            if docker volume rm open-webui >/dev/null 2>&1; then
                echo -e "${GREEN}OpenWebUI data volume removed.${NC}"
            else
                echo -e "${RED}Failed to remove OpenWebUI data volume.${NC}"
            fi
        else
            echo -e "${GREEN}OpenWebUI data volume preserved.${NC}"
        fi

        # Check if LiteLLM is installed and ask if user wants to remove it
        if is_litellm_installed; then
            echo -e "${YELLOW}LiteLLM is also installed.${NC}"
            read -rp "$(echo -e ${YELLOW}"Would you like to remove LiteLLM as well? (y/n): "${NC})" choice
            if [[ $choice =~ ^[Yy]$ ]]; then
                remove_litellm
            else
                echo -e "${GREEN}LiteLLM will be preserved.${NC}"
            fi
        fi
    else
        echo -e "${YELLOW}OpenWebUI is not installed.${NC}"
    fi
}

# Function to check if OpenWebUI is installed
is_openwebui_installed() {
    # Check if container exists
    if check_container_exists "openwebui"; then
        return 0  # True, OpenWebUI is installed
    else
        return 1  # False, OpenWebUI is not installed
    fi
}

# Backup OpenWebUI data
backup_openwebui() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Check if OpenWebUI is installed
    if ! is_openwebui_installed; then
        echo -e "${RED}OpenWebUI is not installed.${NC}"
        return 1
    fi
    
    # Ensure rclone is installed
    install_rclone
    
    echo -e "${GREEN}Starting OpenWebUI backup...${NC}"
    
    # Create temporary backup directory
    local backup_dir="/tmp/openwebui_backup"
    mkdir -p "$backup_dir"
    
    # Stop OpenWebUI if it's running
    if check_container_running "openwebui"; then
        echo -e "${YELLOW}Stopping OpenWebUI for backup...${NC}"
        docker stop openwebui
    fi
    
    # Backup the volume
    echo -e "${YELLOW}Backing up OpenWebUI data...${NC}"
    docker run --rm -v open-webui:/source -v "$backup_dir:/backup" alpine tar czf /backup/openwebui_data.tar.gz -C /source .
    
    # Upload to rclone with sync and delete-before flag
    echo -e "${YELLOW}Uploading backup to cloud storage...${NC}"
    echo "Executing rclone command: rclone sync \"$backup_dir\" \"$RCLONE_REMOTE:/Backups/openwebui/\" --delete-before --progress --timeout 24h"
    rclone sync "$backup_dir" "$RCLONE_REMOTE:/Backups/openwebui/" --delete-before --progress --timeout 24h
    
    # Clean up
    rm -rf "$backup_dir"
    
    # Start OpenWebUI if it was running
    if ! check_container_running "openwebui"; then
        echo -e "${YELLOW}Restarting OpenWebUI...${NC}"
        docker start openwebui
    fi
    
    echo -e "${GREEN}OpenWebUI backup completed successfully!${NC}"
}

# Restore OpenWebUI data
restore_openwebui() {
    # Check if Docker is installed and running
    if ! check_docker; then
        echo -e "${RED}Docker is not installed or not running.${NC}"
        return 1
    fi
    
    # Ensure rclone is installed
    install_rclone
    
    echo -e "${GREEN}Starting OpenWebUI restore...${NC}"
    
    # Create temporary restore directory
    local restore_dir="/tmp/openwebui_restore"
    mkdir -p "$restore_dir"
    
    # Download latest backup from rclone with progress
    echo -e "${YELLOW}Downloading latest backup from cloud storage...${NC}"
    echo "Executing rclone command: rclone copy \"$RCLONE_REMOTE:/Backups/openwebui/openwebui_data.tar.gz\" \"$restore_dir/\" --progress"
    rclone copy "$RCLONE_REMOTE:/Backups/openwebui/openwebui_data.tar.gz" "$restore_dir/" --progress
    
    if [ ! -f "$restore_dir/openwebui_data.tar.gz" ]; then
        echo -e "${RED}No backup file found.${NC}"
        rm -rf "$restore_dir"
        return 1
    fi
    
    # Get the original port if container exists
    local port=8095
    if check_container_exists "openwebui"; then
        local container_id=$(docker ps -a -f "name=openwebui" --format "{{.ID}}")
        port=$(docker port $container_id | grep -o "[0-9]*->8080" | cut -d'-' -f1)
    fi
    
    # Stop and remove OpenWebUI container if it exists
    if check_container_exists "openwebui"; then
        echo -e "${YELLOW}Stopping and removing OpenWebUI container...${NC}"
        docker stop openwebui >/dev/null 2>&1
        docker rm openwebui >/dev/null 2>&1
    fi
    
    # Remove existing volume if it exists
    if docker volume ls | grep -q "open-webui"; then
        echo -e "${YELLOW}Removing existing OpenWebUI volume...${NC}"
        docker volume rm open-webui
    fi
    
    # Create new volume and restore data
    echo -e "${YELLOW}Restoring OpenWebUI data...${NC}"
    docker volume create open-webui
    docker run --rm -v open-webui:/target -v "$restore_dir:/backup" alpine sh -c "cd /target && tar xzf /backup/openwebui_data.tar.gz"
    
    # Clean up
    rm -rf "$restore_dir"
    
    # Start OpenWebUI with the original port
    echo -e "${YELLOW}Starting OpenWebUI...${NC}"
    docker run -d --name openwebui $DOCKER_NETWORK -p $port:8080 -v open-webui:/app/backend/data ghcr.io/open-webui/open-webui:main
    
    echo -e "${GREEN}OpenWebUI restore completed successfully!${NC}"
    echo -e "${GREEN}Access it at http://localhost:$port${NC}"
}
