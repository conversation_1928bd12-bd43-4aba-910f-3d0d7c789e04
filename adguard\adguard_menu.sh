#!/bin/bash

adguard_menu() {
    while true; do
        options=(
            "01. Install AdGuard Home"
            "02. Uninstall AdGuard Home"
            "03. Fix Port 53 Conflict"
            "04. Revert Port 53 Fix"
            "05. Backup Configuration"
            "06. Restore Configuration"
            "07. Back to Main Menu"
        )

        create_menu "AdGuard Home Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) install_adguard ;;
            2) uninstall_adguard ;;
            3) fix_adguard_port_conflict ;;
            4) revert_adguard_port_fix ;;
            5) backup_adguard_config ;;
            6) restore_adguard_config ;;
            7) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo  # Add a blank line for better readability
    done
}
