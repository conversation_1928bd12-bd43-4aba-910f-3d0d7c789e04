# Jellyfin Module

This module provides functionality for installing, configuring, and managing Jellyfin media server in Docker containers.

## Files

### jellyfin_functions.sh
Core functions for Jellyfin server management.

**Key Functions:**
- `check_container_exists()`: Checks if a Jellyfin container already exists
- `stop_and_remove_container()`: Stops and removes existing Jellyfin containers
- `create_volumes()`: Creates necessary volumes for Jellyfin
- `get_rclone_mounts()`: Gets available Rclone mounts for media storage
- `select_rclone_mounts()`: Allows selection of Rclone mounts for media
- `backup_jellyfin()`: Creates a backup of the Jellyfin configuration
- `install_jellyfin()`: Installs and configures Jellyfin server
- `restore_jellyfin_backup()`: Restores Jellyfin from a backup

### jellyfin_menu.sh
Provides menu interface for Jellyfin operations.

**Key Functions:**
- `jellyfin_menu()`: Main menu for Jellyfin operations

## Usage

The Jellyfin module provides functionality for setting up and managing a Jellyfin media server.

To access Jellyfin functionality:
1. Select "Jellyfin Menu" from the main menu
2. Choose from available options for installation, backup, or restoration

## Integration with Other Modules

The Jellyfin module integrates with:
- Docker module for container management
- Rclone module for accessing remote storage for media
- File Operations module for backup and restore operations 