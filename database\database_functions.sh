#!/bin/bash

# Color definitions
CYAN='\033[0;36m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to find next available MySQL container name
get_next_mysql_name() {
    local used_names
    used_names=$(docker ps -a --format '{{.Names}}' | grep -oP '^mysql-\K\d+$' | sort -n)
    local counter=1

    while IFS= read -r num; do
        if ((num != counter)); then
            break
        fi
        ((counter++))
    done <<< "$used_names"

    echo "mysql-${counter}"
}

# Function to check if port is available
check_mysql_port_available() {
    local port=$1

    # Check host port usage
    if ss -tuln | awk '{print $5}' | grep -q ":${port}$"; then
        return 1
    fi

    # Check Docker port mappings
    if docker ps -aq --format '{{.Ports}}' | grep -q ":${port}->"; then
        return 1
    fi

    return 0
}

# Function to find next available port starting from 3308
get_next_mysql_port() {
    local start_port=3308
    local max_port=3408
    local port

    for ((port = start_port; port <= max_port; port++)); do
        if check_mysql_port_available "$port"; then
            echo "$port"
            return
        fi
    done

    echo "$start_port" # Fallback if no ports found
}

# Function to create new MySQL container
create_mysql_container() {
    printf "${CYAN}Creating new MySQL container...${NC}\n"

    # Get container name and port
    container_name=$(get_next_mysql_name)
    mysql_port=$(get_next_mysql_port)

    printf "${GREEN}Container will be named: %s${NC}\n" "$container_name"
    printf "${GREEN}Container will use port: %s${NC}\n" "$mysql_port"

    # Get database configuration with input validation
    while true; do
        read -rp "Enter root password: " root_password
        [[ -n "$root_password" ]] && break
        printf "${RED}Root password cannot be empty!${NC}\n"
    done

    while true; do
        read -rp "Enter database name: " db_name
        [[ -n "$db_name" ]] && break
        printf "${RED}Database name cannot be empty!${NC}\n"
    done

    while true; do
        read -rp "Enter database user: " db_user
        [[ -n "$db_user" ]] && break
        printf "${RED}Database user cannot be empty!${NC}\n"
    done

    while true; do
        read -rp "Enter database password: " db_password
        [[ -n "$db_password" ]] && break
        printf "${RED}Database password cannot be empty!${NC}\n"
    done

    printf "${YELLOW}Creating MySQL container...${NC}\n"
    if docker run --name "$container_name" \
        -p "$mysql_port:3306" \
        -e MYSQL_ROOT_PASSWORD="$root_password" \
        -e MYSQL_DATABASE="$db_name" \
        -e MYSQL_USER="$db_user" \
        -e MYSQL_PASSWORD="$db_password" \
        -d mysql:latest \
        --character-set-server=utf8mb4 \
        --collation-server=utf8mb4_unicode_ci; then
        
        printf "${GREEN}MySQL container created successfully!${NC}\n"
        printf "${GREEN}Connection details:${NC}\n"
        printf "Host: localhost\n"
        printf "Port: %s\n" "$mysql_port"
        printf "Database: %s\n" "$db_name"
        printf "User: %s\n" "$db_user"
        printf "Password: %s\n" "$db_password"
        printf "Root Password: %s\n" "$root_password"
    else
        printf "${RED}Failed to create MySQL container${NC}\n"
        return 1
    fi
}

# Function to list all MySQL containers
list_mysql_containers() {
    printf "${CYAN}MySQL Containers:${NC}\n"
    docker ps -a --filter "ancestor=mysql" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# Function to remove MySQL container
remove_mysql_container() {
    local containers=($(docker ps -a --filter "ancestor=mysql" --format "{{.Names}}"))
    
    if [[ ${#containers[@]} -eq 0 ]]; then
        printf "${RED}No MySQL containers found${NC}\n"
        return
    fi
    
    printf "${CYAN}Select container to remove:${NC}\n"
    for i in "${!containers[@]}"; do
        printf "%d. %s\n" "$((i+1))" "${containers[$i]}"
    done
    printf "%d. Remove all MySQL containers\n" "$((${#containers[@]}+1))"
    printf "%d. Cancel\n" "$((${#containers[@]}+2))"
    
    read -rp "Enter choice: " choice
    
    if [[ "$choice" -eq "$((${#containers[@]}+1))" ]]; then
        read -rp "Are you sure you want to remove ALL MySQL containers? (y/N) " confirm
        if [[ "$confirm" =~ [yY] ]]; then
            printf "${YELLOW}Removing all MySQL containers...${NC}\n"
            docker rm -f "${containers[@]}" || {
                printf "${RED}Failed to remove some containers${NC}\n"
                return 1
            }
            printf "${GREEN}All MySQL containers removed${NC}\n"
        else
            printf "${YELLOW}Operation cancelled${NC}\n"
        fi
    elif [[ "$choice" -eq "$((${#containers[@]}+2))" ]]; then
        printf "${YELLOW}Operation cancelled${NC}\n"
    elif [[ "$choice" -ge 1 ]] && [[ "$choice" -le "${#containers[@]}" ]]; then
        local container="${containers[$((choice-1))]}"
        printf "${YELLOW}Removing container %s...${NC\n}" "$container"
        docker rm -f "$container" || {
            printf "${RED}Failed to remove container %s${NC}\n" "$container"
            return 1
        }
        printf "${GREEN}Container %s removed${NC}\n" "$container"
    else
        printf "${RED}Invalid choice${NC}\n"
    fi
}