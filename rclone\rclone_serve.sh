#!/bin/bash

# Function to check if a port is available
check_port() {
    local port=$1
    if ! lsof -i :$port >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to find an available port in the range 25000-26000
find_available_port() {
    for port in $(seq 25000 26000); do
        if check_port $port; then
            echo $port
            return 0
        fi
    done
    echo ""
    return 1
}

# Function to create a systemd service file for a rclone serve process
create_systemd_service() {
    local service_name=$1
    local remote_path=$2
    local port=$3
    local serve_type=$4
    local additional_flags=$5
    
    # Create a sanitized service name
    local sanitized_name=$(echo "$service_name" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g')
    
    # Create the service file
    local service_file="/etc/systemd/system/rclone-serve-${sanitized_name}.service"
    
    # Create the service file content
    cat > "$service_file" << EOF
[Unit]
Description=Rclone Serve ${serve_type} for ${remote_path}
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/bin/rclone serve ${serve_type} "${remote_path}" --addr :${port} ${additional_flags}
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd to recognize the new service
    systemctl daemon-reload
    
    # Enable and start the service
    systemctl enable "rclone-serve-${sanitized_name}"
    systemctl start "rclone-serve-${sanitized_name}"
    
    echo "Created and started systemd service: rclone-serve-${sanitized_name}"
    return 0
}

# Function to start FTP server
start_ftp_server() {
    local remote_path=$1
    local create_service=$2
    
    # Ensure rclone is installed and configured
    install_rclone
    
    local port=$(find_available_port)
    
    if [ -z "$port" ]; then
        echo "No available ports found in range 25000-26000"
        return 1
    fi
    
    echo "Starting FTP server on port $port"

    # Prompt for username and password
    echo -e "${YELLOW}Enter FTP username:${NC}"
    read -r ftp_username
    
    echo -e "${YELLOW}Enter FTP password:${NC}"
    read -rs ftp_password
    echo # Add a newline after password input

    # Using --buffer-size for better performance with large files
    # Using --dir-cache-time to reduce backend API calls
    # Adding user/pass for authentication
    local additional_flags="--buffer-size 32M --dir-cache-time 30m --user $ftp_username --pass $ftp_password"
    
    if [ "$create_service" = "true" ]; then
        # Extract a name for the service from the remote path
        local service_name=$(echo "$remote_path" | sed 's/[:/]/-/g')
        create_systemd_service "$service_name" "$remote_path" "$port" "ftp" "$additional_flags"
    else
        rclone serve ftp "$remote_path" --addr :$port $additional_flags &
        echo "FTP server started on port $port with username: $ftp_username"
    fi
}

# Function to start HTTP server
start_http_server() {
    local remote_path=$1
    local create_service=$2
    
    # Ensure rclone is installed and configured
    install_rclone
    
    local port=$(find_available_port)
    
    if [ -z "$port" ]; then
        echo "No available ports found in range 25000-26000"
        return 1
    fi
    
    echo "Starting HTTP server on port $port"

    # Prompt for username and password
    echo -e "${YELLOW}Enter HTTP username:${NC}"
    read -r http_username
    
    echo -e "${YELLOW}Enter HTTP password:${NC}"
    read -rs http_password
    echo # Add a newline after password input

    # Using --buffer-size for better performance with large files
    # Using --dir-cache-time to reduce backend API calls
    # Using --vfs-cache-mode minimal for better performance
    # Adding user/pass for authentication
    local additional_flags="--buffer-size 32M --dir-cache-time 30m --vfs-cache-mode minimal --user $http_username --pass $http_password"
    
    if [ "$create_service" = "true" ]; then
        # Extract a name for the service from the remote path
        local service_name=$(echo "$remote_path" | sed 's/[:/]/-/g')
        create_systemd_service "$service_name" "$remote_path" "$port" "http" "$additional_flags"
    else
        rclone serve http "$remote_path" --addr :$port $additional_flags &
        echo "HTTP server started on port $port with username: $http_username"
    fi
}

# Function to start NFS server
start_nfs_server() {
    local remote_path=$1
    local create_service=$2
    
    # Ensure rclone is installed and configured
    install_rclone
    
    local port=$(find_available_port)
    
    if [ -z "$port" ]; then
        echo "No available ports found in range 25000-26000"
        return 1
    fi
    
    echo "Starting NFS server on port $port"
    # Using --buffer-size for better performance with large files
    # Using --dir-cache-time to reduce backend API calls
    # Using --vfs-cache-mode writes for better write performance
    local additional_flags="--buffer-size 32M --dir-cache-time 30m --vfs-cache-mode writes"
    
    if [ "$create_service" = "true" ]; then
        # Extract a name for the service from the remote path
        local service_name=$(echo "$remote_path" | sed 's/[:/]/-/g')
        create_systemd_service "$service_name" "$remote_path" "$port" "nfs" "$additional_flags"
    else
        rclone serve nfs "$remote_path" --addr :$port $additional_flags &
        echo "NFS server started on port $port"
    fi
}

# Function to start SFTP server
start_sftp_server() {
    local remote_path=$1
    local create_service=$2
    
    # Ensure rclone is installed and configured
    install_rclone
    
    local port=$(find_available_port)
    
    if [ -z "$port" ]; then
        echo "No available ports found in range 25000-26000"
        return 1
    fi
    
    echo "Starting SFTP server on port $port"
    
    # Prompt for username and password
    echo -e "${YELLOW}Enter SFTP username:${NC}"
    read -r sftp_username
    
    echo -e "${YELLOW}Enter SFTP password:${NC}"
    read -rs sftp_password
    
    # Using --buffer-size for better performance with large files
    # Using --dir-cache-time to reduce backend API calls
    # Using --vfs-cache-mode writes for better write performance
    # Using --user and --pass for authentication
    local additional_flags="--buffer-size 32M --dir-cache-time 30m --vfs-cache-mode writes --user $sftp_username --pass $sftp_password"
    
    if [ "$create_service" = "true" ]; then
        # Extract a name for the service from the remote path
        local service_name=$(echo "$remote_path" | sed 's/[:/]/-/g')
        create_systemd_service "$service_name" "$remote_path" "$port" "sftp" "$additional_flags"
    else
        rclone serve sftp "$remote_path" --addr :$port $additional_flags &
        echo "SFTP server started on port $port with username: $sftp_username"
    fi
}

# Function to stop all rclone serve processes
stop_all_servers() {
    echo "Stopping all rclone serve processes..."
    
    # Stop all background rclone serve processes more safely
    local pids=$(pgrep -f "rclone serve" | grep -v $$) # Find PIDs, exclude current script's PID
    if [ -n "$pids" ]; then
        echo "Stopping background rclone serve processes with PIDs: $pids"
        kill $pids 2>/dev/null || true
    else
        echo "No background rclone serve processes found to stop."
    fi

    # Stop all systemd services
    for service in $(systemctl list-units --full --all | grep -i "rclone-serve-" | awk '{print $1}'); do
        if [ -n "$service" ]; then
            echo "Stopping and disabling service: $service"
            systemctl stop "$service" 2>/dev/null || true
            systemctl disable "$service" 2>/dev/null || true
        fi
    done
    
    echo "All rclone serve processes stopped"
}

# Function to stop a specific service
stop_service() {
    local service_name=$1
    
    echo "Stopping service: $service_name"
    
    # Stop and disable the service
    systemctl stop "$service_name" 2>/dev/null || true
    systemctl disable "$service_name" 2>/dev/null || true
    
    # Remove the service file
    local service_file="/etc/systemd/system/$service_name.service"
    if [ -f "$service_file" ]; then
        echo "Removing service file: $service_file"
        rm -f "$service_file"
    fi
    
    # Reload systemd to apply changes
    systemctl daemon-reload
    
    echo "Service $service_name stopped and removed"
}

# Function to stop a specific background process
stop_background_process() {
    local pid=$1
    
    echo "Stopping background process with PID: $pid"
    kill $pid 2>/dev/null || true
    echo "Background process stopped"
}

# Function to show status of all running servers
show_server_status() {
    echo "Current rclone serve processes:"
    ps aux | grep "rclone serve" | grep -v grep || true # Add || true to prevent exit on no match
    
    echo -e "\nCurrent rclone serve systemd services:"
    systemctl list-units --full --all | grep -i "rclone-serve-" || true # Add || true to prevent exit on no match
    
    # Add a blank line for better readability
    echo
}
