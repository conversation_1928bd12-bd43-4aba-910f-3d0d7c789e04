#!/bin/bash

# Original Rclone Menu function provided by user
rclone_menu() {
    local options=(
        "01. Install Rclone"
        "02. Rclone Sync - Custom Source to Destination"
        "03. Rclone Auto mount"
        "04. Rclone Utilities"
        "05. Rclone Serve Menu"
        "06. Return to Main Menu"
        "07. Exit"
    )

    while true; do
        # clear # Removed clear command
        create_menu "Rclone Menu" "${options[@]}"

        local choice
        read -rp "$(echo -e ${YELLOW}"Enter your choice (1-7): "${NC})" choice

        case $choice in
            1) install_rclone ;;
            2)
                echo -e "${YELLOW}Enter source path (e.g., remote:path/to/source):${NC}"
                read -r source_path
                echo -e "${YELLOW}Enter destination path (e.g., remote:path/to/destination):${NC}"
                read -r dest_path
                # Use printf %q to properly escape spaces and special characters
                source_path=$(printf %q "$source_path")
                dest_path=$(printf %q "$dest_path")
                rclone_sync "$source_path" "$dest_path"
                ;;
            3) auto_mount ;;
            4) rclone_utilities_menu ;;
            5) serve_menu ;; # Calls the serve_menu function below
            6) return ;;
            7) exit ;;
            *)
                echo -e "${RED}Invalid choice. Please enter a number between 1 and 7.${NC}"
                ;;
        esac

        echo # Add a blank line for better readability
    done
}

# Function to create the stop service menu
stop_service_menu() {
    while true; do
        # Get all rclone serve services (add || true to prevent exit if grep finds nothing)
        local services=($(systemctl list-units --full --all | grep -i "rclone-serve-" | awk '{print $1}' || true))

        # Get all background processes (add || true to prevent exit if grep finds nothing)
        local background_processes=($(ps aux | grep "rclone serve" | grep -v grep | awk '{print $2}' || true))

        if [ ${#services[@]} -eq 0 ] && [ ${#background_processes[@]} -eq 0 ]; then
            echo -e "${YELLOW}No rclone serve processes or services are currently running.${NC}"
            # No need for read here, the main menu loop will handle the prompt
            return
        fi

        local options=()
        local counter=1 # Initialize counter

        # Add background processes to options with numbering
        if [ ${#background_processes[@]} -gt 0 ]; then
            echo -e "${YELLOW}Background Processes:${NC}" # Display header before listing
            for pid in "${background_processes[@]}"; do
                # Use --no-headers to simplify parsing
                local process_info=$(ps -p $pid -o user,cmd --no-headers)
                options+=("$(printf "%02d. Background PID %s: %s" "$counter" "$pid" "$process_info")")
                ((counter++))
            done
        fi

        # Add systemd services to options with numbering
        if [ ${#services[@]} -gt 0 ]; then
            echo -e "${YELLOW}Systemd Services:${NC}" # Display header before listing
            for service in "${services[@]}"; do
                options+=("$(printf "%02d. Service: %s" "$counter" "$service")")
                ((counter++))
            done
        fi

        # Add static options with correct numbering
        local stop_all_num=$counter
        options+=("$(printf "%02d. Stop All Processes and Services" "$stop_all_num")")
        ((counter++))
        local back_num=$counter
        options+=("$(printf "%02d. Back to Serve Menu" "$back_num")") # Changed text for clarity

        # Display the menu using the existing function
        create_menu "Rclone Stop Service Menu" "${options[@]}"

        local choice
        # Update prompt with the correct dynamic range
        read -rp "$(echo -e ${YELLOW}"Enter your choice (1-$back_num): "${NC})" choice

        if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le $back_num ]; then
            if [ "$choice" -eq $stop_all_num ]; then
                # Stop all processes and services
                stop_all_servers
                # Prompt is handled by the main serve_menu loop now
                break # Exit stop menu loop
            elif [ "$choice" -eq $back_num ]; then
                # Back to serve menu
                break # Exit stop menu loop
            else
                # Stop the selected process or service
                local selected_option_full=${options[$((choice - 1))]}
                # Remove the leading number and dot (e.g., "01. ")
                local selected_option=$(echo "$selected_option_full" | sed 's/^[0-9]\+\. //')

                if [[ "$selected_option" == Background* ]]; then
                    # Extract PID (Format: "Background PID XXX: ...")
                    local pid=$(echo "$selected_option" | awk '{print $3}' | sed 's/://')
                    stop_background_process "$pid"
                elif [[ "$selected_option" == Service:* ]]; then
                    # Extract service name (Format: "Service: XXX")
                    local service_name=$(echo "$selected_option" | sed 's/Service: //')
                    stop_service "$service_name"
                fi
                 # Prompt is handled by the main serve_menu loop now
                 # Loop will continue and re-evaluate running processes/services
            fi
        else
            echo -e "${RED}Invalid choice. Please enter a number between 1 and $back_num.${NC}"
            # Prompt is handled by the main serve_menu loop now
        fi
        # Add a small pause for readability before the main loop prompts again
        sleep 1
    done
}

# Function to create the serve menu (Renamed from rclone_menu)
serve_menu() {
    # Ensure rclone is installed and configured
    install_rclone

    while true; do
        local choice
        # Define options array inside the loop with correct numbering
        local options=( # <<< Make this local
            "01. Start FTP Server (Temporary)"
            "02. Start HTTP Server (Temporary)"
            "03. Start NFS Server (Temporary)"
            "04. Start SFTP Server (Temporary)"
            # "05. Start WebDAV Server (Temporary)" - Removed
            "05. Start FTP Server (Persistent)"  # Renumbered
            "06. Start HTTP Server (Persistent)" # Renumbered
            "07. Start NFS Server (Persistent)"  # Renumbered
            "08. Start SFTP Server (Persistent)" # Renumbered
            # "10. Start WebDAV Server (Persistent)" - Removed
            "09. Stop All Servers"             # Renumbered
            "10. Stop Specific Service"        # Renumbered
            "11. Show Server Status"           # Renumbered
            "12. Back to Rclone Menu"          # Changed from "Back to Main Menu"
        )

        # Create the menu with updated options
        create_menu "Rclone Serve Menu" "${options[@]}" # Updated Title

        local choice
        read -rp "$(echo -e ${YELLOW}"Enter your choice (1-12): "${NC})" choice # Updated range

        case $choice in
            1) # FTP Temp
                echo -e "${YELLOW}Enter remote path (e.g., remote:path):${NC}"
                read -r remote_path
                start_ftp_server "$remote_path" "false"
                ;;
            2) # HTTP Temp
                echo -e "${YELLOW}Enter remote path (e.g., remote:path):${NC}"
                read -r remote_path
                start_http_server "$remote_path" "false"
                ;;
            3) # NFS Temp
                echo -e "${YELLOW}Enter remote path (e.g., remote:path):${NC}"
                read -r remote_path
                start_nfs_server "$remote_path" "false"
                ;;
            4) # SFTP Temp
                echo -e "${YELLOW}Enter remote path (e.g., remote:path):${NC}"
                read -r remote_path
                start_sftp_server "$remote_path" "false"
                ;;
            # Case 5 (WebDAV Temp) removed
            5) # FTP Persistent (was 6)
                echo -e "${YELLOW}Enter remote path (e.g., remote:path):${NC}"
                read -r remote_path
                start_ftp_server "$remote_path" "true"
                ;;
            6) # HTTP Persistent (was 7)
                echo -e "${YELLOW}Enter remote path (e.g., remote:path):${NC}"
                read -r remote_path
                start_http_server "$remote_path" "true"
                ;;
            7) # NFS Persistent (was 8)
                echo -e "${YELLOW}Enter remote path (e.g., remote:path):${NC}"
                read -r remote_path
                start_nfs_server "$remote_path" "true"
                ;;
            8) # SFTP Persistent (was 9)
                echo -e "${YELLOW}Enter remote path (e.g., remote:path):${NC}"
                read -r remote_path
                start_sftp_server "$remote_path" "true"
                ;;
            # Case 10 (WebDAV Persistent) removed
            9) # Stop All (was 11)
                {
                    stop_all_servers
                    echo -e "${YELLOW}All servers have been stopped successfully.${NC}"
                }
                ;;
            10) # Stop Specific (was 12)
                stop_service_menu # Calls the stop_service_menu function above
                ;;
            11) # Show Status (was 13)
                show_server_status
                ;;
            12) # Back (was 14)
                return # Returns to the caller (rclone_menu)
                ;;
            *)
                echo -e "${RED}Invalid choice. Please enter a number between 1 and 12.${NC}" # Updated range
                ;;
        esac

        # No "Press Enter" prompt needed here anymore
    done
}