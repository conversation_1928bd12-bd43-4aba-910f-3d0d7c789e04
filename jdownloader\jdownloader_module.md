# JDownloader Module

This module provides functionality for installing, configuring, and managing JDownloader download manager in Docker containers.

## Files

### jdownloader_functions.sh
Core functions for JDownloader management.

**Key Functions:**
- `get_download_path()`: Gets the download path for JDownloader
- `get_next_container_number()`: Gets the next available container number for JDownloader instances
- `get_next_available_port()`: Gets the next available port for JDownloader web interface
- `install_jdownloader()`: Installs JDownloader
- `start_jdownloader()`: Starts JDownloader container with configuration
- `remove_jdownloader()`: Removes JDownloader container and optionally its data

### jdownloader_menu.sh
Provides menu interface for JDownloader operations.

**Key Functions:**
- `jdownloader_menu()`: Main menu for JDownloader operations

## Usage

The JDownloader module provides functionality for setting up and managing a JDownloader download manager.

To access JDownloader functionality:
1. Select "JDownloader Menu" from the main menu
2. Choose from available options for installation, configuration, or removal

## Integration with Other Modules

The JDownloader module integrates with:
- Docker module for container management
- File Operations module for managing download directories 