#!/bin/bash

# Function to join or rejoin Tailscale network
join_tailscale() {
    local auth_key="$1"
    local advertise_routes="$2"
    local hostname="$3"
    local login_server="$4"

    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║    Joining Tailscale Network  ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    if [ -z "$auth_key" ]; then
        echo -e "${RED}Error: Auth key is required to join Tailscale.${NC}"
        return 1
    fi

    tailscale up --reset \
                 --auth-key "$auth_key" \
                 --advertise-routes "$advertise_routes" \
                 --accept-routes \
                 --hostname "$hostname" \
                 --login-server "$login_server"

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Successfully joined Tailscale network!${NC}"
        echo -e "${GREEN}Running 'tailscale status' to verify connection:${NC}"
        tailscale status
    else
        echo -e "${RED}Failed to join Tailscale network.${NC}"
        return 1
    fi
}

# Function to force reauthentication with Tailscale
force_reauth_tailscale() {
    local auth_key="$1"
    local advertise_routes="$2"
    local hostname="$3"
    local login_server="$4"

    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║  Forcing Tailscale Reauth     ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    tailscale up --reset \
                 --auth-key "$auth_key" \
                 --advertise-routes "$advertise_routes" \
                 --accept-routes \
                 --hostname "$hostname" \
                 --login-server "$login_server" \
                 --force-reauth

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Successfully reauthenticated with Tailscale!${NC}"
        echo -e "${GREEN}Running 'tailscale status' to verify connection:${NC}"
        tailscale status
    else
        echo -e "${RED}Failed to reauthenticate with Tailscale.${NC}"
        return 1
    fi
}

# Example usage:
# To join network:
# join_tailscale "your-auth-key" "*************/24,**********/24" "custom-hostname" "https://headscale.yourdomain.com"
# 
# To force reauth:
# force_reauth_tailscale "your-auth-key" "*************/24,**********/24" "custom-hostname" "https://headscale.yourdomain.com"
