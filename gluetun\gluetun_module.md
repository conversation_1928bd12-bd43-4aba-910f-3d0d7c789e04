# Gluetun Module

This module provides functionality for setting up and managing Gluetun, a VPN client Docker container with a focus on privacy and security. It supports multiple VPN providers including Private Internet Access (PIA), FastestVPN, and custom configurations.

## Files

### gluetun_functions.sh
Core functions for Gluetun VPN container management.

**Key Functions:**
- `install_gluetun_pia()`: Installs and configures Gluetun with PIA VPN settings
- `install_gluetun_fastestvpn()`: Installs and configures Gluetun with FastestVPN settings
- `install_gluetun_custom()`: Installs and configures Gluetun with custom VPN settings
- `remove_gluetun()`: Removes Gluetun container and its dependent containers
- `get_next_gluetun_number()`: Gets the next available number for Gluetun container naming
- `get_gluetun_port()`: Gets the port configuration for a Gluetun container

### pia_config.sh
Functions for Private Internet Access (PIA) VPN configuration with Gluetun.

**Key Functions:**
- `create_pia_generator_file()`: Creates configuration generator file for PIA
- `check_pia()`: Checks PIA VPN connection status
- `generate_pia_wireguard_file()`: Generates WireGuard configuration file for PIA

### gluetun_menu.sh
Provides menu interface for Gluetun operations.

**Menu Options:**
1. Add New Gluetun Container with PIA Config
2. Add New Gluetun Container with FastestVPN Config
3. Add New Gluetun Container with Custom Config
4. Remove Gluetun Container
5. Generate PIA Wireguard Config
6. Return to Main Menu
7. Exit

## Features

- Support for multiple VPN providers (PIA, FastestVPN, Custom)
- WireGuard protocol support
- Automatic container naming and port management
- Integration with Docker networking
- Health checks and automatic reconnection
- Customizable DNS settings
- Port forwarding capabilities
- Multiple container support with unique naming

## Usage

The Gluetun module provides functionality for setting up secure VPN tunnels in Docker:

1. Select "Gluetun Menu" from the main menu
2. Choose your preferred VPN provider configuration:
   - PIA (Private Internet Access)
   - FastestVPN
   - Custom configuration
3. Follow the interactive prompts to configure:
   - Container name (auto-generated or custom)
   - Port mappings
   - DNS settings
   - Network settings
4. The container will be created and started automatically

## Integration with Other Modules

The Gluetun module integrates with:
- Docker module for container management
- SOCKS5 Gluetun module for creating SOCKS5 proxies using Gluetun's network
- JDownloader module for secure downloads through VPN
- Other modules can use Gluetun containers as network proxies

## Security Features

- Automatic kill switch to prevent traffic leaks
- DNS leak protection
- Support for custom DNS servers
- Network isolation through Docker
- Secure WireGuard protocol support
- Health monitoring and automatic reconnection