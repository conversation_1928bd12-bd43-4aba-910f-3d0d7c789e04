utilities_menu() {
    options=(
        "01. Install All Utilities"
        "02. Remove All Utilities"
        "03. Return to Main Menu"
        "04. Exit"
    )

    while true; do
        create_menu "Utilities Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) install_all_utilities ;;
            2) remove_all_utilities ;;
            3) return ;;
            4) exit ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done 
}