#!/bin/bash

# Function to clean up the remote if possible
rclone_cleanup() {
    local path=""
    
    echo -e "${YELLOW}Enter remote path to clean up (e.g., remote:path):${NC}"
    read -r path
    
    if [[ -z "$path" ]]; then
        echo -e "${RED}No path specified. Operation canceled.${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}About to clean up remote: $path${NC}"
    echo -e "${YELLOW}This will empty the trash or delete old file versions. Not supported by all remotes.${NC}"
    
    # Ask for confirmation
    echo -e "${YELLOW}Do you want to continue? [y/N]:${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Operation canceled.${NC}"
        return 1
    fi
    
    # Run with dry-run first
    echo -e "${BLUE}Performing dry run first...${NC}"
    rclone cleanup --dry-run "$path"
    
    # Confirm again after dry run
    echo -e "${YELLOW}Ready to proceed with actual cleanup? [y/N]:${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Operation canceled.${NC}"
        return 1
    fi
    
    # Execute the command
    echo -e "${BLUE}Executing cleanup...${NC}"
    rclone cleanup "$path"
    
    echo -e "${GREEN}Cleanup operation completed.${NC}"
}

# Function to delete files in a path
rclone_delete_files() {
    local path=""
    local min_size=""
    local max_size=""
    local min_age=""
    local max_age=""
    
    echo -e "${YELLOW}Enter remote path to delete files from (e.g., remote:path):${NC}"
    read -r path
    
    if [[ -z "$path" ]]; then
        echo -e "${RED}No path specified. Operation canceled.${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}Would you like to specify filters? [y/N]:${NC}"
    read -r use_filters
    
    if [[ "$use_filters" =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Enter minimum size (e.g., 100M, leave blank to skip):${NC}"
        read -r min_size
        
        echo -e "${YELLOW}Enter maximum size (e.g., 1G, leave blank to skip):${NC}"
        read -r max_size
        
        echo -e "${YELLOW}Enter minimum age (e.g., 30d, leave blank to skip):${NC}"
        read -r min_age
        
        echo -e "${YELLOW}Enter maximum age (e.g., 7d, leave blank to skip):${NC}"
        read -r max_age
    fi
    
    # Build the command
    local cmd="rclone delete"
    
    if [[ -n "$min_size" ]]; then
        cmd+=" --min-size $min_size"
    fi
    
    if [[ -n "$max_size" ]]; then
        cmd+=" --max-size $max_size"
    fi
    
    if [[ -n "$min_age" ]]; then
        cmd+=" --min-age $min_age"
    fi
    
    if [[ -n "$max_age" ]]; then
        cmd+=" --max-age $max_age"
    fi
    
    cmd+=" \"$path\""
    
    # Ask about removing empty directories
    echo -e "${YELLOW}Remove empty directories after deletion? [y/N]:${NC}"
    read -r remove_dirs
    if [[ "$remove_dirs" =~ ^[Yy]$ ]]; then
        cmd+=" --rmdirs"
    fi
    
    # Run with dry-run first
    echo -e "${BLUE}Performing dry run first...${NC}"
    eval "rclone --dry-run ${cmd#rclone }"
    
    # Confirm again after dry run
    echo -e "${YELLOW}Ready to proceed with actual deletion? [y/N]:${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Operation canceled.${NC}"
        return 1
    fi
    
    # Execute the command
    echo -e "${BLUE}Executing delete operation...${NC}"
    eval "$cmd"
    
    echo -e "${GREEN}Delete operation completed.${NC}"
}

# Function to delete a single file
rclone_delete_file() {
    local file_path=""
    
    echo -e "${YELLOW}Enter remote file path to delete (e.g., remote:path/to/file.txt):${NC}"
    read -r file_path
    
    if [[ -z "$file_path" ]]; then
        echo -e "${RED}No file path specified. Operation canceled.${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}About to delete file: $file_path${NC}"
    echo -e "${YELLOW}This cannot be used to remove a directory and will always delete the specified file if it exists.${NC}"
    
    # Ask for confirmation
    echo -e "${YELLOW}Do you want to continue? [y/N]:${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Operation canceled.${NC}"
        return 1
    fi
    
    # Run with dry-run first
    echo -e "${BLUE}Performing dry run first...${NC}"
    rclone deletefile --dry-run "$file_path"
    
    # Confirm again after dry run
    echo -e "${YELLOW}Ready to proceed with actual file deletion? [y/N]:${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Operation canceled.${NC}"
        return 1
    fi
    
    # Execute the command
    echo -e "${BLUE}Executing file deletion...${NC}"
    rclone deletefile "$file_path"
    
    echo -e "${GREEN}File deletion completed.${NC}"
}

# Function to purge a directory and all its contents
rclone_purge_directory() {
    local path=""
    
    echo -e "${YELLOW}Enter remote directory path to purge (e.g., remote:path/to/dir):${NC}"
    read -r path
    
    if [[ -z "$path" ]]; then
        echo -e "${RED}No path specified. Operation canceled.${NC}"
        return 1
    fi
    
    # Check if the directory exists first
    echo "Executing rclone command: rclone lsd \"${path%/*}\""
    if ! rclone lsd "${path%/*}" 2>/dev/null | grep -q "${path##*/}"; then
        echo -e "${GREEN}Directory '$path' does not exist or is already removed.${NC}"
        return 0
    fi
    
    # Check if remote might be encrypted by looking for "crypt" in its configuration
    local remote_name="${path%%:*}"
    local is_encrypted=false
    
    if rclone config show "$remote_name" 2>/dev/null | grep -q "type = crypt"; then
        is_encrypted=true
        echo -e "${YELLOW}Detected encrypted remote. Using alternative method for deletion.${NC}"
    fi
    
    echo -e "${YELLOW}About to purge directory: $path${NC}"
    echo -e "${RED}WARNING: This will remove the directory and all of its contents!${NC}"
    
    # Ask for confirmation
    echo -e "${YELLOW}Do you want to continue? [y/N]:${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Operation canceled.${NC}"
        return 1
    fi
    
    # Ask if user wants to perform a dry run
    echo -e "${YELLOW}Perform a dry run first? (Not recommended for large directories) [y/N]:${NC}"
    read -r do_dry_run
    
    if [[ "$do_dry_run" =~ ^[Yy]$ ]]; then
        # Run with dry-run if requested
        echo -e "${BLUE}Performing dry run...${NC}"
        
        if [ "$is_encrypted" = true ]; then
            # For encrypted remotes, we'll use delete with recursive instead of purge
            echo "Executing rclone command: rclone delete --dry-run --min-size 0b --max-size 100T --rmdirs \"$path\" -vv"
            rclone delete --dry-run --min-size 0b --max-size 100T --rmdirs "$path" -vv
        else
            # Standard purge with timeout for normal remotes
            timeout 30s rclone purge --dry-run "$path"
            
            # Check if timeout occurred
            if [ $? -eq 124 ]; then
                echo -e "${YELLOW}Dry run timed out. This could be due to a large directory structure.${NC}"
                echo -e "${YELLOW}Would you like to try an alternative delete method? [y/N]:${NC}"
                read -r use_alt
                
                if [[ "$use_alt" =~ ^[Yy]$ ]]; then
                    is_encrypted=true
                    echo -e "${BLUE}Trying alternative delete method...${NC}"
                    echo "Executing rclone command: rclone delete --dry-run --min-size 0b --max-size 100T --rmdirs \"$path\" -vv"
                    rclone delete --dry-run --min-size 0b --max-size 100T --rmdirs "$path" -vv
                fi
            fi
        fi
        
        # Check again if directory still exists after dry run
        # Some encrypted remotes might silently complete the operation during dry run
        echo "Executing rclone command: rclone lsd \"${path%/*}\""
        if ! rclone lsd "${path%/*}" 2>/dev/null | grep -q "${path##*/}"; then
            echo -e "${GREEN}Directory has already been removed during dry run.${NC}"
            return 0
        fi
        
        # Confirm again after dry run
        echo -e "${YELLOW}Ready to proceed with actual purge? [y/N]:${NC}"
        read -r confirm
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            echo -e "${RED}Operation canceled.${NC}"
            return 1
        fi
    fi
    
    # Execute the command
    echo -e "${BLUE}Executing purge operation...${NC}"
    
    if [ "$is_encrypted" = true ]; then
        # For encrypted remotes, we'll use delete with recursive instead of purge
        echo -e "${YELLOW}Using delete with recursive for encrypted remote...${NC}"
        echo "Executing rclone command: rclone delete --min-size 0b --max-size 100T --rmdirs \"$path\" -v"
        rclone delete --min-size 0b --max-size 100T --rmdirs "$path" -v
        
        # Check if there are any remaining directories
        echo -e "${YELLOW}Checking for remaining directory structure...${NC}"
        rclone rmdirs "$path" -v
    else
        # Standard purge with a longer timeout
        timeout 60s rclone purge "$path"
        
        # Check if timeout occurred
        if [ $? -eq 124 ]; then
            echo -e "${YELLOW}Purge operation timed out. Trying alternative delete method...${NC}"
            echo "Executing rclone command: rclone delete --min-size 0b --max-size 100T --rmdirs \"$path\" -v"
            rclone delete --min-size 0b --max-size 100T --rmdirs "$path" -v
            echo "Executing rclone command: rclone rmdirs \"$path\" -v"
            rclone rmdirs "$path" -v
        fi
    fi
    
    # Verify the directory is gone (with retries)
    echo -e "${YELLOW}Verifying directory removal...${NC}"
    local retry_count=0
    local max_retries=3
    
    while [ $retry_count -lt $max_retries ]; do
        echo "Executing rclone command: rclone lsd \"${path%/*}\""
        if ! rclone lsd "${path%/*}" 2>/dev/null | grep -q "${path##*/}"; then
            echo -e "${GREEN}Purge operation completed successfully. Directory no longer exists.${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}Directory might still exist. Waiting 2 seconds for remote to update...${NC}"
        sleep 2
        ((retry_count++))
    done
    
    # If we get here, directory might still exist
    echo -e "${YELLOW}Directory appears to still exist. You may need to:${NC}"
    echo -e "${YELLOW}1. Wait a few minutes for remote changes to propagate${NC}"
    echo -e "${YELLOW}2. Try manual deletion with: rclone purge $path${NC}"
    echo -e "${YELLOW}3. Check permissions on the remote${NC}"
}

# Function to remove an empty directory
rclone_remove_dir() {
    local path=""
    
    echo -e "${YELLOW}Enter remote directory path to remove (e.g., remote:path/to/dir):${NC}"
    read -r path
    
    if [[ -z "$path" ]]; then
        echo -e "${RED}No path specified. Operation canceled.${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}About to remove directory: $path${NC}"
    echo -e "${YELLOW}This will only remove the directory if it is empty.${NC}"
    
    # Ask for confirmation
    echo -e "${YELLOW}Do you want to continue? [y/N]:${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Operation canceled.${NC}"
        return 1
    fi
    
    # Run with dry-run first
    echo -e "${BLUE}Performing dry run first...${NC}"
    echo "Executing rclone command: rclone rmdir --dry-run \"$path\""
    rclone rmdir --dry-run "$path"
    
    # Confirm again after dry run
    echo -e "${YELLOW}Ready to proceed with actual directory removal? [y/N]:${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Operation canceled.${NC}"
        return 1
    fi
    
    # Execute the command
    echo -e "${BLUE}Executing directory removal...${NC}"
    echo "Executing rclone command: rclone rmdir \"$path\""
    rclone rmdir "$path"
    
    echo -e "${GREEN}Directory removal completed.${NC}"
}

# Function to remove empty directories
rclone_remove_empty_dirs() {
    local path=""
    local leave_root=false
    
    echo -e "${YELLOW}Enter remote directory path to remove empty directories from (e.g., remote:path):${NC}"
    read -r path
    
    if [[ -z "$path" ]]; then
        echo -e "${RED}No path specified. Operation canceled.${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}Leave root directory? [Y/n]:${NC}"
    read -r leave_root_input
    if [[ "$leave_root_input" =~ ^[Nn]$ ]]; then
        leave_root=false
    else
        leave_root=true
    fi
    
    echo -e "${YELLOW}About to remove empty directories from: $path${NC}"
    
    # Build the command
    local cmd="rclone rmdirs"
    if [[ "$leave_root" == true ]]; then
        cmd+=" --leave-root"
    fi
    cmd+=" \"$path\""
    
    # Run with dry-run first
    echo -e "${BLUE}Performing dry run first...${NC}"
    eval "rclone --dry-run ${cmd#rclone }"
    
    # Ask for confirmation
    echo -e "${YELLOW}Do you want to continue? [y/N]:${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Operation canceled.${NC}"
        return 1
    fi
    
    # Execute the command
    echo -e "${BLUE}Executing empty directory removal...${NC}"
    eval "$cmd"
    
    echo -e "${GREEN}Empty directory removal completed.${NC}"
}

# Rclone utilities menu
rclone_utilities_menu() {
    local options=(
        "01. Cleanup - Empty trash or delete old versions"
        "02. Delete - Remove files in path (with filters)"
        "03. DeleteFile - Remove a single file"
        "04. Purge - Remove directory and all contents"
        "05. RmDir - Remove empty directory"
        "06. RmDirs - Remove all empty directories"
        "07. Return to Rclone Menu"
        "08. Exit"
    )

    while true; do
        create_menu "Rclone Utilities Menu" "${options[@]}"

        local choice
        read -rp "$(echo -e ${YELLOW}"Enter your choice (1-8): "${NC})" choice

        case $choice in
            1) rclone_cleanup ;;
            2) rclone_delete_files ;;
            3) rclone_delete_file ;;
            4) rclone_purge_directory ;;
            5) rclone_remove_dir ;;
            6) rclone_remove_empty_dirs ;;
            7) return ;;
            8) exit ;;
            *)
                echo -e "${RED}Invalid choice. Please enter a number between 1 and 8.${NC}"
                ;;
        esac

        echo # Add a blank line for better readability
    done
} 