#!/bin/bash

# Function to backup AdGuard Home configuration
backup_adguard_config() {
    local adguard_path="/opt/adguardhome"
    local backup_dir="/opt/adguard_backups"
    local backup_file="adguard_backup.tar.gz"
    local backup_path="$backup_dir/$backup_file"
    local rclone_dest="$RCLONE_REMOTEBackups/Adguard"

    # Check if AdGuard Home directories exist
    if [ ! -d "$adguard_path" ] || [ ! -d "$adguard_path/config" ] || [ ! -d "$adguard_path/work" ]; then
        echo "AdGuard Home installation not found at $adguard_path"
        echo "Please ensure AdGuard Home is properly installed first."
        return 1
    fi

    if ! is_adguard_running; then
        echo "AdGuard Home is not running. Cannot backup configuration."
        return 1
    fi

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Backup cannot proceed."
        return 1
    fi

    # Create backup directory if it doesn't exist
    mkdir -p "$backup_dir"

    # Use compression function from file_operations module
    echo "Creating AdGuard Home configuration backup..."
    if ! check_compression_tools; then
        echo "Required compression tools are not available."
        return 1
    fi

    # Compress the directories using the compress_items function
    if compress_items_noninteractive "$adguard_path" "config work" "$backup_path" "tar.gz"; then
        echo "Local backup created successfully."
        
        # Use rclone_sync function for cloud backup
        echo "Uploading backup to cloud storage..."
        echo "Executing rclone command: rclone copy \"$backup_path\" \"$RCLONE_REMOTE:/Backups/Adguard\" --progress"
        if rclone copy "$backup_path" "$rclone_dest" --progress; then
            echo "Backup successfully uploaded to cloud storage."
            
            # Remove local backup file but keep AdGuard config
            echo "Cleaning up local backup file..."
            rm -f "$backup_path"
            echo "Backup process completed successfully."
            return 0
        else
            echo "Failed to upload backup to cloud storage."
            rm -f "$backup_path"  # Clean up local backup on failed upload
            return 1
        fi
    else
        echo "Failed to create local backup."
        return 1
    fi
}

# Function to restore AdGuard Home configuration
restore_adguard_config() {
    local backup_dir="/opt/adguard_backups"
    local backup_file="adguard_backup.tar.gz"
    local temp_backup="$backup_dir/$backup_file"
    local rclone_source="$RCLONE_REMOTEBackups/Adguard"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # Create backup directory if it doesn't exist
    mkdir -p "$backup_dir"

    # Download the backup file
    echo "Downloading backup from cloud storage..."
    
    # First clear any existing file with the same name
    rm -f "$temp_backup"
    
    # Use direct rclone command 
    echo "Executing rclone command: rclone copy \"$RCLONE_REMOTE:/Backups/Adguard/$backup_file\" \"$backup_dir\" --progress"
    if ! rclone copy "$rclone_source/$backup_file" "$backup_dir" --progress; then
        echo "Failed to download backup from cloud storage."
        return 1
    fi

    # Ensure the file was actually downloaded
    if [ ! -f "$temp_backup" ]; then
        echo "Backup file was not downloaded correctly."
        return 1
    fi

    # Stop AdGuard Home if running
    if is_adguard_running; then
        echo "Stopping AdGuard Home..."
        docker stop adguardhome
    fi

    echo "Restoring configuration from backup..."
    if check_compression_tools; then
        if decompress_archive_noninteractive "$temp_backup" "/opt/adguardhome"; then
            echo "Configuration restored successfully."
            rm -f "$temp_backup"  # Clean up downloaded backup file
            
            # Start AdGuard Home with restored configuration
            echo "Starting AdGuard Home with restored configuration..."
            docker start adguardhome
            return 0
        else
            echo "Failed to restore configuration."
            rm -f "$temp_backup"  # Clean up on failure
            return 1
        fi
    else
        echo "Required decompression tools are not available."
        rm -f "$temp_backup"  # Clean up downloaded backup file
        return 1
    fi
}