# Rclone Module

This module provides functionality for managing Rclone, a tool for cloud storage synchronization and mounting.

## Files

### rcloneconfig.sh
Handles Rclone configuration.

**Key Functions:**
- `create_rclone_config()`: Creates and manages Rclone configuration

### rclone_functions.sh
Core Rclone utility functions.

**Key Functions:**
- `rclone_sync()`: Synchronizes files between local and remote storage
- `install_rclone()`: Installs Rclone on the system
- `mount_cleanup()`: Cleans up after Rclone mount operations

### rclone_auto_mount.sh
Handles automatic mounting of remote storage.

**Key Functions:**
- `auto_mount()`: Automatically mounts remote storage at startup or on-demand

### rclone_menu.sh
Provides menu interface for Rclone operations.

**Key Functions:**
- `rclone_menu()`: Main menu for Rclone operations

### rclone_serve.sh
Manages various Rclone serving protocols.

**Key Functions:**
- `start_ftp_server()`: Starts an FTP server for remote storage
- `start_http_server()`: Starts an HTTP server for remote storage
- `start_nfs_server()`: Starts an NFS server for remote storage
- `start_sftp_server()`: Starts an SFTP server for remote storage
- `stop_all_servers()`: Stops all running Rclone serve processes
- `show_server_status()`: Shows status of all running Rclone serve processes
- `stop_service()`: Stops a specific rclone serve systemd service
- `create_systemd_service()`: Creates a systemd service for persistent rclone serve processes

### rclone_utilities.sh
Provides file and directory management utilities for Rclone.

**Key Functions:**
- `rclone_cleanup()`: Cleans up remotes by emptying trash or deleting old file versions
- `rclone_delete_files()`: Removes files in a path, respecting filters for size and age
- `rclone_delete_file()`: Removes a single file from a remote
- `rclone_purge_directory()`: Removes a directory and all its contents
- `rclone_remove_dir()`: Removes an empty directory
- `rclone_remove_empty_dirs()`: Removes all empty directories, with option to leave root
- `rclone_utilities_menu()`: Provides a menu interface for all utilities

## Usage

The Rclone module provides functionality for cloud storage synchronization and mounting remote storage as local filesystems.

To access Rclone functionality:
1. Select "Rclone Menu" from the main menu
2. Choose from available Rclone operations including sync, mount, and configuration
3. Use the "Rclone Utilities" option to access file and directory management functions
4. Use the "Rclone Serve Menu" option to access various serving protocols

## Integration with Other Modules

The Rclone module can be used by file operation modules that need to transfer files to/from cloud storage. It provides the infrastructure for accessing remote storage as if it were local. 

## Utilities

The Rclone Utilities section provides several commands for managing files and directories on remote storage:

- **Cleanup**: Empty trash or delete old file versions (not supported by all remotes)
- **Delete**: Remove files in a path with optional filtering by size and age
- **DeleteFile**: Remove a single specific file
- **Purge**: Remove a directory and all its contents
- **RmDir**: Remove an empty directory
- **RmDirs**: Remove all empty directories in a path

Each utility includes safety features such as:
- Dry run preview before actual execution
- Confirmation prompts with clear warnings
- Detailed status information during operation

## Serving Protocols

The Rclone Serve section provides several protocols for serving remote storage:

- **FTP**: Serve remote storage over FTP protocol
- **HTTP**: Serve remote storage over HTTP protocol
- **NFS**: Serve remote storage over NFS protocol
- **SFTP**: Serve remote storage over SFTP protocol

Each protocol:
- Automatically finds an available port in the range 25000-26000
- Runs in the background
- Can be monitored and stopped through the serve menu
- Provides clear status information about running servers

### Performance Optimizations

The Rclone Serve functionality includes several performance optimizations:

- **Buffer Size**: Uses `--buffer-size 32M` to improve performance with large files by buffering data in memory
- **Directory Cache**: Uses `--dir-cache-time 30m` to reduce backend API calls by caching directory listings
- **VFS Cache Mode**: 
  - HTTP server uses `--vfs-cache-mode minimal` for better read performance
  - NFS and SFTP servers use `--vfs-cache-mode writes` for better write performance

These optimizations help balance performance with resource usage, providing faster access to remote storage while minimizing backend API calls.

### Persistent Services

The Rclone Serve functionality includes options for creating persistent services that will automatically start on system boot:

- **Temporary vs. Persistent**: Choose between temporary (stops on reboot) or persistent (starts automatically on boot) services
- **Systemd Integration**: Creates and manages systemd service files for persistent rclone serve processes
- **Service Management**: Automatically enables, starts, stops, and disables systemd services as needed
- **Status Monitoring**: Shows both temporary processes and persistent systemd services in the status display

To create a persistent service:
1. Select the "Persistent" option for the desired protocol from the serve menu
2. Enter the remote path when prompted
3. The system will create a systemd service file, enable it, and start the service
4. The service will automatically start on system boot

To stop persistent services:
1. Select "Stop All Servers" from the serve menu
2. This will stop both temporary processes and persistent systemd services 