#!/bin/bash

openweb_ui_menu() {
    while true; do
        options=(
            "1. OpenWebUI Menu"
            "2. LiteLLM Menu"
            "3. Install Both Apps"
            "4. Remove Both Apps"
            "5. Backup Both Apps"
            "6. Restore Both Apps"
            "7. Show Both Apps Status"
            "8. Back to Main Menu"
        )

        create_menu "OpenWebUI and LiteLLM Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) openwebui_submenu ;;
            2) litellm_submenu ;;
            3) install_both_apps ;;
            4) remove_both_apps ;;
            5) backup_both_apps ;;
            6) restore_both_apps ;;
            7) show_both_apps_status ;;
            8) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo
    done
}

openwebui_submenu() {
    while true; do
        options=(
            "1. Install/Run OpenWebUI"
            "2. Start OpenWebUI"
            "3. Stop OpenWebUI"
            "4. Check OpenWebUI Status"
            "5. Remove OpenWebUI"
            "6. Backup OpenWebUI"
            "7. Restore OpenWebUI"
            "8. Back to Previous Menu"
        )

        create_menu "OpenWebUI Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) install_openwebui ;;
            2) start_openwebui ;;
            3) stop_openwebui ;;
            4) check_openwebui_status ;;
            5) remove_openwebui ;;
            6) backup_openwebui ;;
            7) restore_openwebui ;;
            8) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo
    done
}

litellm_submenu() {
    while true; do
        options=(
            "1. Install/Run LiteLLM (Latest Version)"
            "2. Start LiteLLM"
            "3. Stop LiteLLM"
            "4. Restart LiteLLM"
            "5. Check LiteLLM Status"
            "6. Remove LiteLLM"
            "7. Backup LiteLLM"
            "8. Restore LiteLLM"
            "9. Back to Previous Menu"
        )

        create_menu "LiteLLM Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) install_litellm ;;
            2) start_litellm ;;
            3) stop_litellm ;;
            4) restart_litellm ;;
            5) check_litellm_status ;;
            6) remove_litellm ;;
            7) backup_litellm ;;
            8) restore_litellm ;;
            9) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo
    done
}

# Function to backup both apps
backup_both_apps() {
    echo -e "${GREEN}Starting backup of both OpenWebUI and LiteLLM...${NC}"
    
    # Ensure rclone is installed
    install_rclone
    
    # Backup OpenWebUI if installed
    if is_openwebui_installed; then
        echo -e "${YELLOW}Backing up OpenWebUI...${NC}"
        backup_openwebui
    else
        echo -e "${YELLOW}OpenWebUI is not installed, skipping...${NC}"
    fi
    
    # Backup LiteLLM if installed
    if is_litellm_installed; then
        echo -e "${YELLOW}Backing up LiteLLM...${NC}"
        backup_litellm
    else
        echo -e "${YELLOW}LiteLLM is not installed, skipping...${NC}"
    fi
    
    echo -e "${GREEN}Backup of both apps completed!${NC}"
}

# Function to restore both apps
restore_both_apps() {
    echo -e "${GREEN}Starting restore of both OpenWebUI and LiteLLM...${NC}"
    
    # Ensure rclone is installed
    install_rclone
    
    # First check and install apps if needed
    echo -e "${YELLOW}Checking if apps are installed...${NC}"
    
    # Check and install OpenWebUI if needed
    if ! is_openwebui_installed; then
        echo -e "${YELLOW}OpenWebUI is not installed. Installing first...${NC}"
        # Skip the automatic LiteLLM installation prompt by setting a temporary environment variable
        SKIP_LITELLM_PROMPT=true install_openwebui
    else
        echo -e "${GREEN}OpenWebUI is already installed.${NC}"
    fi
    
    # Check and install LiteLLM if needed
    if ! is_litellm_installed; then
        echo -e "${YELLOW}LiteLLM is not installed. Installing first...${NC}"
        install_litellm
    else
        echo -e "${GREEN}LiteLLM is already installed.${NC}"
    fi
    
    # Now check for backup files
    echo -e "${YELLOW}Checking for backup files...${NC}"
    
    local has_openwebui_backup=false
    local has_litellm_backup=false
    
    # Check for OpenWebUI backup
    echo "Executing rclone command: rclone ls \"$RCLONE_REMOTE:/Backups/openwebui/openwebui_data.tar.gz\""
    if rclone ls "$RCLONE_REMOTE:/Backups/openwebui/openwebui_data.tar.gz" >/dev/null 2>&1; then
        has_openwebui_backup=true
        echo -e "${GREEN}Found OpenWebUI backup.${NC}"
    else
        echo -e "${YELLOW}No OpenWebUI backup found.${NC}"
    fi
    
    # Check for LiteLLM backup
    echo "Executing rclone command: rclone ls \"$RCLONE_REMOTE:/Backups/litellm/litellm_backup.tar.gz\""
    if rclone ls "$RCLONE_REMOTE:/Backups/litellm/litellm_backup.tar.gz" >/dev/null 2>&1; then
        has_litellm_backup=true
        echo -e "${GREEN}Found LiteLLM backup.${NC}"
    else
        echo -e "${YELLOW}No LiteLLM backup found.${NC}"
    fi
    
    # If no backups found, return to menu
    if [ "$has_openwebui_backup" = false ] && [ "$has_litellm_backup" = false ]; then
        echo -e "${RED}No backup files found for either OpenWebUI or LiteLLM.${NC}"
        echo -e "${YELLOW}Returning to previous menu...${NC}"
        return
    fi
    
    # Restore OpenWebUI if backup exists
    if [ "$has_openwebui_backup" = true ]; then
        echo -e "${YELLOW}Restoring OpenWebUI...${NC}"
        restore_openwebui
    fi
    
    # Restore LiteLLM if backup exists
    if [ "$has_litellm_backup" = true ]; then
        echo -e "${YELLOW}Restoring LiteLLM...${NC}"
        restore_litellm
    fi
    
    echo -e "${GREEN}Restore of both apps completed!${NC}"
}

# Function to install both apps
install_both_apps() {
    echo -e "${GREEN}Starting installation of both OpenWebUI and LiteLLM...${NC}"
    
    # Install OpenWebUI first
    echo -e "${YELLOW}Installing OpenWebUI...${NC}"
    # Skip the automatic LiteLLM installation prompt by setting a temporary environment variable
    SKIP_LITELLM_PROMPT=true install_openwebui
    
    # Install LiteLLM only if it's not already installed
    if ! is_litellm_installed; then
        echo -e "${YELLOW}Installing LiteLLM...${NC}"
        install_litellm
    else
        echo -e "${GREEN}LiteLLM is already installed.${NC}"
    fi
    
    echo -e "${GREEN}Installation of both apps completed!${NC}"
}

# Function to remove both apps
remove_both_apps() {
    echo -e "${GREEN}Starting removal of both OpenWebUI and LiteLLM...${NC}"
    
    # Ask for confirmation
    read -rp "$(echo -e ${YELLOW}"Do you want to remove both OpenWebUI and LiteLLM? This will remove all containers and data. (y/n): "${NC})" choice
    if [[ ! $choice =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Operation cancelled.${NC}"
        return 0
    fi
    
    # Ask about volumes
    read -rp "$(echo -e ${YELLOW}"Do you want to remove volumes as well? This will delete all data. (y/n): "${NC})" choice_volumes
    
    # Remove OpenWebUI first
    if is_openwebui_installed; then
        echo -e "${YELLOW}Removing OpenWebUI...${NC}"
        if [[ $choice_volumes =~ ^[Yy]$ ]]; then
            docker stop openwebui >/dev/null 2>&1
            docker rm openwebui >/dev/null 2>&1
            docker volume rm open-webui >/dev/null 2>&1
        else
            docker stop openwebui >/dev/null 2>&1
            docker rm openwebui >/dev/null 2>&1
        fi
        echo -e "${GREEN}OpenWebUI removed successfully!${NC}"
    else
        echo -e "${YELLOW}OpenWebUI is not installed.${NC}"
    fi
    
    # Remove LiteLLM
    if is_litellm_installed; then
        echo -e "${YELLOW}Removing LiteLLM...${NC}"
        cd /opt/litellm
        if [[ $choice_volumes =~ ^[Yy]$ ]]; then
            docker compose down -v
        else
            docker compose down
        fi
        cd /opt
        sudo rm -rf /opt/litellm
        echo -e "${GREEN}LiteLLM removed successfully!${NC}"
    else
        echo -e "${YELLOW}LiteLLM is not installed.${NC}"
    fi
    
    echo -e "${GREEN}Removal of both apps completed!${NC}"
}

# Function to show status of both apps
show_both_apps_status() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Apps Status              ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    # Check OpenWebUI status
    echo -e "${YELLOW}OpenWebUI Status:${NC}"
    if check_container_running "openwebui"; then
        local container_id=$(docker ps -f "name=openwebui" --format "{{.ID}}")
        local port=$(docker port $container_id | grep "8080/tcp" | cut -d':' -f2 | cut -d'-' -f1)
        echo -e "${GREEN}✓ Running${NC}"
        echo -e "${GREEN}  Access at: http://localhost:$port${NC}"
    else
        if check_container_exists "openwebui"; then
            echo -e "${YELLOW}⚠ Installed but not running${NC}"
        else
            echo -e "${RED}✗ Not installed${NC}"
        fi
    fi
    
    echo
    
    # Check LiteLLM status
    echo -e "${YELLOW}LiteLLM Status:${NC}"
    if is_litellm_installed; then
        if docker compose -f /opt/litellm/docker-compose.yml ps | grep -q "Up"; then
            echo -e "${GREEN}✓ Running${NC}"
            echo -e "${GREEN}  API Access at: http://localhost:4000${NC}"
        else
            echo -e "${YELLOW}⚠ Installed but not running${NC}"
        fi
    else
        echo -e "${RED}✗ Not installed${NC}"
    fi
    
    echo
    read -rp "$(echo -e ${YELLOW}"Press Enter to continue...${NC}")"
}
