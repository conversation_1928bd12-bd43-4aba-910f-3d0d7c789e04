# AdGuard Home Module

This module provides functionality for setting up and managing AdGuard Home, a network-wide ad and tracker blocking DNS server.

## Files

### adguard_functions.sh
Core functions for AdGuard Home management.

**Key Functions:**
- `check_port()`: Checks if a specific port is available on a given protocol
- `check_all_ports()`: Checks if all required ports for AdGuard are available
- `is_adguard_running()`: Checks if AdGuard Home container is currently running
- `stop_adguard()`: Stops and removes the AdGuard Home container
- `install_adguard()`: Installs AdGuard Home with Docker, handles port conflicts, and offers restore option
- `uninstall_adguard()`: Completely removes AdGuard Home and its configurations with backup option
- `check_adguard_status()`: Checks the status of AdGuard Home service
- `view_adguard_logs()`: Views the logs from the AdGuard Home container

### adguard_backup.sh
Functions for managing AdGuard Home configuration backups.

**Key Functions:**
- `backup_adguard_config()`: Creates a compressed backup and uploads it to cloud storage (Eyunion:Backups/Adguard)
- `restore_adguard_config()`: Downloads and restores AdGuard configuration from cloud storage

### adguard_port_manager.sh
Functions for managing port conflicts with AdGuard Home, particularly port 53 used by systemd-resolved.

**Key Functions:**
- `fix_adguard_port_conflict()`: Configures systemd-resolved to free up port 53 by creating custom configuration and symlinks
- `revert_adguard_port_fix()`: Reverts systemd-resolved configuration changes to restore normal DNS operation

### adguard_menu.sh
Provides menu interface for AdGuard Home operations.

**Key Functions:**
- `adguard_menu()`: Main menu for AdGuard Home operations including:
  - Install AdGuard Home (with reinstall option)
  - Uninstall AdGuard Home (with backup option)
  - Fix Port 53 Conflict
  - Revert Port 53 Fix
  - Backup Configuration
  - Restore Configuration

## Usage

The AdGuard Home module provides functionality for setting up a DNS-based ad and tracker blocking solution.

To access AdGuard Home functionality:
1. Select "AdGuard Home Menu" from the main menu
2. Choose from installation, configuration, or management options
3. Follow the interactive prompts to configure your AdGuard Home server

## Installation Features

- Fresh installation with option to restore previous configuration
- Reinstallation option for existing installations
- Port conflict detection and resolution (especially for port 53)
- Automatic Docker installation check
- Configuration backup option during uninstallation

## Integration with Other Modules

The AdGuard Home module integrates with:
- Docker module for container management
- Rclone module for cloud backup/restore operations (using Eyunion:Backups/Adguard)
- File Operations module for compression/decompression of backups
- Network functionality in other modules may use AdGuard Home for DNS resolution

## Backup and Restore

The module supports automatic backup and restore of configurations:
- Backups are stored in cloud storage (Eyunion:Backups/Adguard)
- Automatic backup prompt before uninstallation
- Restore option available during fresh installation
- Manual backup/restore through menu options
- Backup verification with failure handling

## Port Conflict Management

The module includes comprehensive handling of port conflicts:
- Detection of port 53 usage by systemd-resolved
- Configuration of systemd-resolved to free port 53
- Backup of original configuration before changes
- Ability to revert changes and restore default DNS resolution

## Installation Directory Structure

```plaintext
/opt/adguardhome/
├── config/    # AdGuard configuration files
├── work/      # AdGuard working directory
/opt/adguard_backups/  # Temporary location for backup operations
```

## Docker Container Configuration

The AdGuard Home Docker container is configured with:
- Exposed ports: 53/tcp, 53/udp, 67/udp, 68/udp, 80/tcp, 443/tcp, 443/udp, 3000/tcp, 853/tcp, 784/udp, 853/udp, 8853/udp, 5443/tcp, 5443/udp
- Persistent volume mapping for configuration and work directories
- Automatic restart policy (unless-stopped)