get_download_path() {
    local default_path="/home/<USER>/Downloads"
    local download_path

    while true; do
        read -p "Enter download path [$default_path]: " download_path
        download_path=${download_path:-$default_path}

        if [ -d "$download_path" ]; then
            printf "%s" "$download_path"
            return 0
        else
            while true; do
                read -p "Directory '$download_path' does not exist. Press [C] to create it or [N] to enter a new path: " choice
                case "${choice:0:1}" in
                    [Cc]*)
                        mkdir -p "$download_path"
                        if [ $? -eq 0 ]; then
                            echo "Directory created successfully." >&2
                            printf "%s" "$download_path"
                            return 0
                        else
                            echo "Failed to create directory. Please check permissions and try again." >&2
                            break
                        fi
                        ;;
                    [Nn]*)
                        break
                        ;;
                    *)
                        echo "Please answer C to create or N for new path." >&2
                        ;;
                esac
            done
        fi
    done
}

get_next_container_number() {

    local prefix="jdownloader-"
    local i=1
    
    while true; do
        if ! docker ps -a --format '{{.Names}}' | grep -q "^${prefix}${i}$"; then
            echo "${prefix}${i}"
            return
        fi
        ((i++))
    done
}

get_next_available_port() {
    local start_port=5800
    
    while true; do
        if ! docker ps -a --format '{{.Ports}}' | grep -q ":${start_port}->"; then
            echo "$start_port"
            return
        fi
        ((start_port++))
    done
}

install_jdownloader() {
    # Get all running containers using the JDownloader image
    local containers=$(docker ps -a --filter ancestor=fazee6/jdownloader-2 --format '{{.Names}}')
    
    if [ -n "$containers" ]; then
        echo "Found the following JDownloader containers:"
        echo "$containers" | nl
        
        read -p "Do you want to create another JDownloader container? [Y/n]: " create_new
        if [[ -n "$create_new" && ! "${create_new:0:1}" =~ [Yy] ]]; then
            echo "Installation cancelled."
            return 0
        fi
    fi
    
    start_jdownloader
}

start_jdownloader() {
    install_docker
    
    local download_path=$(get_download_path)
    local use_vpn
    local container_name=$(get_next_container_number)
    local port
    
    # Extract number from container name
    local number=$(echo "$container_name" | grep -o '[0-9]*$')
    # Construct matching gluetun container name
    local matching_vpn="gluetun-${number}"
    
    # Ask for container name
    read -p "Enter container name [$container_name]: " container_input
    container_name=${container_input:-$container_name}
    
    # Re-extract number if user provided custom name
    number=$(echo "$container_name" | grep -o '[0-9]*$')
    matching_vpn="gluetun-${number}"
    
    # Check if the chosen name already exists
    if docker ps -a --format '{{.Names}}' | grep -q "^${container_name}$"; then
        echo "Error: A container with name '$container_name' already exists."
        read -p "Do you want to try a different name? [Y/n]: " retry
        if [[ -z "$retry" || "${retry:0:1}" =~ [Yy] ]]; then
            start_jdownloader
            return
        else
            echo "Installation cancelled."
            return 1
        fi
    fi

    # Check for existing configuration
    local config_path="/docker/appdata/$container_name"
    if [ -d "$config_path" ]; then
        echo "Found existing configuration at $config_path"
        read -p "Do you want to delete the existing configuration? [y/N]: " delete_config
        if [[ "${delete_config:0:1}" =~ [Yy] ]]; then
            echo "Removing existing configuration..."
            if ! rm -rf "$config_path" 2>/dev/null; then
                echo "Failed to remove configuration directory. Attempting with sudo..."
                if ! sudo rm -rf "$config_path" 2>/dev/null; then
                    echo "Error: Failed to remove configuration directory at $config_path"
                    echo "You may need to remove it manually using: sudo rm -rf $config_path"
                    return 1
                fi
            fi
            echo "Configuration data removed successfully."
        fi
    fi
    
    read -p "Do you want to use VPN for JDownloader? [Y/n]: " use_vpn
    if [[ -z "$use_vpn" || "${use_vpn:0:1}" =~ [Yy] ]]; then
        # Check if matching Gluetun container exists
        if docker ps -a --format '{{.Names}}' | grep -q "^${matching_vpn}$"; then
            echo "Found matching Gluetun container: $matching_vpn"
            vpn_container="$matching_vpn"
            
            # Get the port from existing Gluetun container
            port=$(get_gluetun_port "$vpn_container")
            
            # Check if the container is running
            if ! docker ps --format '{{.Names}}' | grep -q "^${vpn_container}$"; then
                echo "Starting VPN container '$vpn_container'..."
                docker start "$vpn_container"
                sleep 5
            fi
        else
            read -p "Do you want to create a matching Gluetun container ($matching_vpn)? [Y/n]: " create_vpn
            if [[ -z "$create_vpn" || "${create_vpn:0:1}" =~ [Yy] ]]; then
                # Get port before creating gluetun
                port=$(get_next_available_port)
                
                # Pass the matching name and port to install_gluetun_pia
                install_gluetun_pia "$matching_vpn" "$port"
                vpn_container="$matching_vpn"
                
                if ! docker ps -a --format '{{.Names}}' | grep -q "^${vpn_container}$"; then
                    echo "Failed to create VPN container with name '$vpn_container'. Aborting JDownloader installation."
                    return 1
                fi
            else
                echo "Aborting JDownloader installation as VPN container is required."
                return 1
            fi
        fi
        
        # For VPN setup, we don't need to expose ports on JDownloader container
        docker run -d \
            --name="$container_name" \
            --network=container:$vpn_container \
            -e VNC_LISTENING_PORT=5900 \
            -e WEB_LISTENING_PORT=5800 \
            -e DISPLAY_PORT=5800 \
            -v "/docker/appdata/$container_name":/config:rw \
            -v "$download_path":/output:rw \
            --restart unless-stopped \
            fazee6/jdownloader-2
            
        echo "JDownloader container created and accessible through VPN container on ports:"
        echo "Web UI: ${port}"
        echo "VNC: $((port+100))"
        
    else
        # For non-VPN setup, find next available port
        port=$(get_next_available_port)
        
        docker run -d \
            --name="$container_name" \
            -p ${port}:5800 \
            -p $((port+100)):5900 \
            -e VNC_LISTENING_PORT=5900 \
            -e WEB_LISTENING_PORT=5800 \
            -e DISPLAY_PORT=5800 \
            -v "/docker/appdata/$container_name":/config:rw \
            -v "$download_path":/output:rw \
            --restart unless-stopped \
            fazee6/jdownloader-2
            
        echo "JDownloader container created and accessible on ports:"
        echo "Web UI: ${port}"
        echo "VNC: $((port+100))"
    fi
}

remove_jdownloader() {
    # Get all running containers using the JDownloader image
    local containers=$(docker ps -a --filter ancestor=fazee6/jdownloader-2 --format '{{.Names}}')
    
    if [ -z "$containers" ]; then
        echo "No JDownloader containers found."
        return 0
    fi
    
    echo "Found the following JDownloader containers:"
    local i=1
    local container_array=()
    
    while IFS= read -r container; do
        echo "[$i] $container"
        container_array+=("$container")
        ((i++))
    done <<< "$containers"
    
    local selection
    while true; do
        read -p "Enter the number of the container to remove [1-${#container_array[@]}]: " selection
        if [[ "$selection" =~ ^[0-9]+$ ]] && [ "$selection" -ge 1 ] && [ "$selection" -le "${#container_array[@]}" ]; then
            break
        fi
        echo "Invalid selection. Please enter a number between 1 and ${#container_array[@]}."
    done
    
    local selected_container="${container_array[$((selection-1))]}"
    echo "Removing container: $selected_container"
    
    # Remove container and its data
    local config_path="/docker/appdata/$selected_container"
    if [ -d "$config_path" ]; then
        read -p "Do you want to remove the configuration data at $config_path? [y/N]: " remove_config
        if [[ "${remove_config:0:1}" =~ [Yy] ]]; then
            # Stop the container first to release any file locks
            docker stop "$selected_container" >/dev/null 2>&1
            
            # Try to remove the directory
            if ! rm -rf "$config_path" 2>/dev/null; then
                echo "Failed to remove configuration directory. Attempting with sudo..."
                if ! sudo rm -rf "$config_path" 2>/dev/null; then
                    echo "Warning: Failed to remove configuration directory at $config_path"
                    echo "You may need to remove it manually using: sudo rm -rf $config_path"
                else
                    echo "Configuration data removed successfully."
                fi
            else
                echo "Configuration data removed successfully."
            fi
        fi
    fi
    
    # Remove the container
    if ! docker rm -f "$selected_container" 2>/dev/null; then
        echo "Warning: Failed to remove container $selected_container"
        echo "You may need to force remove it using: docker rm -f $selected_container"
        return 1
    fi
    echo "Container $selected_container has been removed."
}