#!/bin/bash

# Define colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to log errors
log_error() {
    echo -e "${RED}ERROR: $*${NC}" >&2
}

# Function to log debug messages
log_debug() {
    # Removed debug logging
    :
}

# Define colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

install_docker() {
    # Function to check if Docker is properly installed and running
    check_docker() {
        # Check if Docker binary exists and is executable
        if ! command -v docker >/dev/null 2>&1 || ! [ -x "$(command -v docker)" ]; then
            return 1
        fi
        
        # Check if Docker daemon is running
        if ! systemctl is-active --quiet docker; then
            # Try to start Docker if it's not running
            sudo systemctl start docker >/dev/null 2>&1
            sleep 2
            if ! systemctl is-active --quiet docker; then
                return 1
            fi
        fi
        
        # Verify Docker is working by running a test command
        if ! docker version >/dev/null 2>&1; then
            return 1
        fi

        # Check if my_network network exists, if not create it
        if ! docker network inspect my_network >/dev/null 2>&1; then
            echo -e "${YELLOW}Creating my_network network...${NC}"
            if ! sudo docker network create --label com.docker.compose.network=my_network my_network >/dev/null 2>&1; then
                echo -e "${RED}Failed to create my_network network${NC}"
                return 1
            fi
            echo -e "${GREEN}my_network network created successfully${NC}"
        fi
        
        return 0
    }

    # Check if Docker is already installed and running
    if check_docker; then
        return 0
    fi

    # If we get here, Docker needs to be installed
    echo -e "${YELLOW}Installing Docker...${NC}"
    
    # Set noninteractive frontend and timeout
    export DEBIAN_FRONTEND=noninteractive
    local TIMEOUT=300  # 5 minutes timeout
    local MAX_RETRIES=3
    
    # Update package lists and install prerequisites
    echo -e "${YELLOW}Installing prerequisites...${NC}"
    if ! NEEDRESTART_MODE=a sudo apt-get install -qq -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        lsb-release \
        software-properties-common >/dev/null 2>&1; then
        echo -e "${RED}Failed to install prerequisites${NC}"
        return 1
    fi

    # Add Docker's official GPG key with retry mechanism
    echo -e "${YELLOW}Adding Docker GPG key...${NC}"
    local max_retries=3
    local retry=0
    while [ $retry -lt $max_retries ]; do
        if curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg 2>/dev/null; then
            break
        fi
        retry=$((retry + 1))
        [ $retry -lt $max_retries ] && sleep 2
    done

    if [ $retry -eq $max_retries ]; then
        echo -e "${RED}Failed to add Docker's GPG key${NC}"
        return 1
    fi

    # Add Docker repository
    echo -e "${YELLOW}Adding Docker repository...${NC}"
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | \
        sudo tee /etc/apt/sources.list.d/docker.list >/dev/null 2>&1

    # Update package list again and install Docker
    echo -e "${YELLOW}Installing Docker packages...${NC}"
    if ! sudo apt-get update -qq >/dev/null 2>&1; then
        echo -e "${RED}Failed to update package list${NC}"
        return 1
    fi

    # Set needrestart to automatic mode and install Docker
    if ! NEEDRESTART_MODE=a sudo apt-get install -qq -y docker-ce docker-ce-cli containerd.io >/dev/null 2>&1; then
        echo -e "${RED}Failed to install Docker packages${NC}"
        return 1
    fi

    # Enable and start services
    echo -e "${YELLOW}Starting Docker services...${NC}"
    sudo systemctl enable containerd.service >/dev/null 2>&1
    sudo systemctl enable docker.service >/dev/null 2>&1
    
    # Reload systemd and start services
    sudo systemctl daemon-reload >/dev/null 2>&1
    sudo systemctl start containerd.service >/dev/null 2>&1
    sudo systemctl start docker.service >/dev/null 2>&1

    # Verify installation with retries
    local retry_count=0
    while [ $retry_count -lt $MAX_RETRIES ]; do
        if command -v docker >/dev/null 2>&1 && [ -f "/usr/bin/docker" ] && systemctl is-active --quiet docker; then
            echo -e "${GREEN}Docker installed and running successfully${NC}"
            # Verify Docker is working
            if docker --version >/dev/null 2>&1; then
                # Check if my_network network exists, if not create it
                if ! docker network inspect my_network >/dev/null 2>&1; then
                    echo -e "${YELLOW}Creating my_network network...${NC}"
                    if ! sudo docker network create --label com.docker.compose.network=my_network my_network >/dev/null 2>&1; then
                        echo -e "${RED}Failed to create my_network network${NC}"
                        return 1
                    fi
                    echo -e "${GREEN}my_network network created successfully${NC}"
                fi
                return 0
            fi
        fi
        ((retry_count++))
        [ $retry_count -lt $MAX_RETRIES ] && sleep 3
        sudo systemctl restart docker.service >/dev/null 2>&1
    done

    echo -e "${RED}Docker installation failed. Please try rebooting your system.${NC}"
    return 1
}

remove_docker() {
    # Set noninteractive frontend to avoid prompts
    export DEBIAN_FRONTEND=noninteractive

    echo -e "${YELLOW}Removing Docker...${NC}"

    # Kill all running Docker processes
    pkill -9 docker >/dev/null 2>&1 || true
    pkill -9 containerd >/dev/null 2>&1 || true
    pkill -9 dockerd >/dev/null 2>&1 || true

    # Stop and kill all containers if Docker is still running
    if command -v docker >/dev/null 2>&1; then
        docker kill $(docker ps -q) >/dev/null 2>&1 || true
        docker rm -f $(docker ps -a -q) >/dev/null 2>&1 || true
    fi

    # Stop and disable all related services
    systemctl_services=("docker.service" "docker.socket" "containerd.service" "docker.mount" "snap.docker.dockerd.service")
    for service in "${systemctl_services[@]}"; do
        sudo systemctl stop $service >/dev/null 2>&1 || true
        sudo systemctl disable $service >/dev/null 2>&1 || true
        sudo rm -f /etc/systemd/system/$service >/dev/null 2>&1 || true
        sudo rm -f /usr/lib/systemd/system/$service >/dev/null 2>&1 || true
    done

    # Remove all Docker packages
    sudo apt-get remove --yes docker docker-engine docker.io containerd runc >/dev/null 2>&1 || true
    sudo apt-get purge --yes docker-ce docker-ce-cli containerd.io docker docker-engine docker.io docker-compose-plugin >/dev/null 2>&1 || true
    sudo apt-get autoremove --yes >/dev/null 2>&1 || true

    # Remove all Docker-related directories and files
    directories=(
        "/var/lib/docker"
        "/etc/docker"
        "/var/run/docker"
        "/var/run/docker.sock"
        "/usr/local/bin/docker"
        "/usr/bin/docker"
        "/usr/share/docker"
        "/usr/libexec/docker"
        "/var/lib/containerd"
        "/etc/containerd"
        "/var/log/docker"
        "/var/log/containers"
        "/etc/apparmor.d/docker"
        "/var/lib/dockershim"
        "/opt/containerd"
        "/run/docker"
        "/run/containerd"
        "/usr/local/bin/containerd"
        "/usr/bin/containerd"
        "/usr/local/bin/docker-compose"
        "/usr/bin/docker-compose"
        "/usr/local/bin/docker-credential-*"
        "/usr/bin/docker-credential-*"
        "/usr/local/bin/dockerd"
        "/usr/bin/dockerd"
        "/usr/local/bin/docker-init"
        "/usr/bin/docker-init"
        "/usr/local/bin/docker-proxy"
        "/usr/bin/docker-proxy"
        "/usr/local/bin/containerd-shim"
        "/usr/bin/containerd-shim"
        "/usr/local/bin/ctr"
        "/usr/bin/ctr"
        "/usr/local/bin/runc"
        "/usr/bin/runc"
    )

    for dir in "${directories[@]}"; do
        sudo rm -rf $dir >/dev/null 2>&1 || true
    done

    # Remove configuration files
    sudo rm -rf /etc/apt/sources.list.d/docker*.list >/dev/null 2>&1 || true
    sudo rm -rf /etc/apt/keyrings/docker*.gpg >/dev/null 2>&1 || true
    sudo rm -rf /usr/share/keyrings/docker*.gpg >/dev/null 2>&1 || true
    sudo rm -rf /etc/apt/trusted.gpg.d/docker*.gpg >/dev/null 2>&1 || true
    sudo rm -rf /root/.docker >/dev/null 2>&1 || true
    sudo rm -rf /home/<USER>/.docker >/dev/null 2>&1 || true

    # Remove Docker group
    sudo groupdel docker >/dev/null 2>&1 || true

    # Remove snap Docker if installed
    sudo snap remove docker >/dev/null 2>&1 || true

    # Clean package cache and update
    sudo apt-get clean
    sudo apt-get update -qq >/dev/null 2>&1 || true

    # Reload systemd
    sudo systemctl daemon-reload >/dev/null 2>&1

    # Additional cleanup for any remaining files
    sudo find /etc -name '*docker*' -exec rm -rf {} + >/dev/null 2>&1 || true
    sudo find /usr -name '*docker*' -exec rm -rf {} + >/dev/null 2>&1 || true
    sudo find /var -name '*docker*' -exec rm -rf {} + >/dev/null 2>&1 || true

    echo -e "${GREEN}Docker has been completely removed from your system.${NC}"
}

restart_unless_stopped() {
    # Get all running container IDs
    running_containers=$(docker ps -q)

    # Loop through each running container
    for container_id in $running_containers; do
        container_name=$(docker inspect -f '{{.Name}}' $container_id | sed 's/^\///') # Get and clean container name

        # Update the restart policy to "unless-stopped"
        docker update --restart unless-stopped "$container_id"

        echo "Updated restart policy for container: $container_name ($container_id)"
    done
}

# Function to show detailed Docker status
show_docker_status() {
    echo -e "${YELLOW}Docker Container Status:${NC}"
    echo -e "${CYAN}=================================${NC}"
    
    # Get all containers (including stopped ones)
    docker ps -a --format "table {{.ID}}\t{{.Names}}\t{{.Status}}\t{{.Image}}\t{{.Ports}}" | \
    (read -r header && echo -e "${GREEN}$header${NC}" && while read -r line; do
        if [[ $line == *"Up"* ]]; then
            echo -e "${GREEN}$line${NC}"  # Running containers in green
        else
            echo -e "${RED}$line${NC}"    # Stopped containers in red
        fi
    done)

    echo -e "\n${YELLOW}Resource Usage:${NC}"
    echo -e "${CYAN}=================================${NC}"
    docker stats --no-stream --format "table {{.Container}}\t{{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# Function to clean Docker system
clean_docker_system() {
    echo -e "${YELLOW}Starting Docker System Cleanup...${NC}"
    local start_space=$(docker system df -v | grep "Total Space" | awk '{print $3}')
    
    # Function to show progress
    show_progress() {
        echo -e "${CYAN}$1... ${GREEN}✓${NC}"
    }
    
    # Stop all running containers gracefully
    if [ "$(docker ps -q)" ]; then
        echo -e "${YELLOW}Stopping running containers...${NC}"
        docker stop $(docker ps -q) 2>/dev/null
        show_progress "Containers stopped"
    fi
    
    # Remove containers with progress tracking
    if [ "$(docker ps -aq)" ]; then
        echo -e "${YELLOW}Removing stopped containers...${NC}"
        docker container prune -f >/dev/null 2>&1
        show_progress "Containers removed"
    fi
    
    # Remove unused images with size check
    local images_size_before=$(docker system df -v | grep "Images" | awk '{print $5}')
    if [ "$(docker images -q)" ]; then
        echo -e "${YELLOW}Removing unused images...${NC}"
        docker image prune -af >/dev/null 2>&1
        show_progress "Images cleaned"
    fi
    
    # Remove unused volumes
    if [ "$(docker volume ls -qf dangling=true)" ]; then
        echo -e "${YELLOW}Removing unused volumes...${NC}"
        docker volume prune -f >/dev/null 2>&1
        show_progress "Volumes cleaned"
    fi
    
    # Remove unused networks
    if [ "$(docker network ls -qf dangling=true)" ]; then
        echo -e "${YELLOW}Removing unused networks...${NC}"
        docker network prune -f >/dev/null 2>&1
        show_progress "Networks cleaned"
    fi
    
    # Clean build cache
    echo -e "${YELLOW}Cleaning build cache...${NC}"
    docker builder prune -f >/dev/null 2>&1
    show_progress "Build cache cleaned"
    
    # Show space reclaimed
    local end_space=$(docker system df -v | grep "Total Space" | awk '{print $3}')
    local space_saved=$(echo "$start_space - $end_space" | bc 2>/dev/null)
    
    echo -e "\n${GREEN}Docker system cleanup completed!${NC}"
    echo -e "${YELLOW}Space saved: ${GREEN}$space_saved${NC}"
    
    # Show current system status
    echo -e "\n${YELLOW}Current Docker system status:${NC}"
    docker system df
}

# Function to rename a container
rename_container() {
    # Check if there are any containers
    if [ -z "$(docker ps -a -q)" ]; then
        echo -e "${YELLOW}No containers found.${NC}"
        return
    fi

    echo -e "${YELLOW}Available Containers:${NC}"
    echo -e "${CYAN}=================================${NC}"
    
    # Display containers with numbers for selection
    containers=($(docker ps -a --format '{{.ID}}'))
    i=1
    docker ps -a --format "table {{.ID}}\t{{.Names}}\t{{.Status}}\t{{.Image}}\t{{.Ports}}" | \
    (read -r header && echo -e "${GREEN}$header${NC}" && while read -r line; do
        if [[ $line == *"Up"* ]]; then
            echo -e "${GREEN}$i) $line${NC}"  # Running containers in green
        else
            echo -e "${RED}$i) $line${NC}"    # Stopped containers in red
        fi
        ((i++))
    done)

    # Get user selection
    echo -e "\n${YELLOW}Enter the number of the container to rename (or 'q' to quit):${NC}"
    read -r selection

    # Check if user wants to quit
    if [[ "$selection" == "q" ]]; then
        return
    fi

    # Validate selection
    if ! [[ "$selection" =~ ^[0-9]+$ ]] || [ "$selection" -lt 1 ] || [ "$selection" -gt "${#containers[@]}" ]; then
        echo -e "${RED}Invalid selection${NC}"
        return 1
    fi

    # Get the container ID from selection
    container_id="${containers[$selection-1]}"
    
    # Get container details
    container_info=$(docker inspect "$container_id")
    current_name=$(echo "$container_info" | jq -r '.[0].Name' | sed 's/\///')
    image=$(echo "$container_info" | jq -r '.[0].Config.Image')
    mounts=$(echo "$container_info" | jq -r '.[0].Mounts')
    ports=$(echo "$container_info" | jq -r '.[0].HostConfig.PortBindings')
    env=$(echo "$container_info" | jq -r '.[0].Config.Env[]' 2>/dev/null)
    restart_policy=$(echo "$container_info" | jq -r '.[0].HostConfig.RestartPolicy.Name')
    network_mode=$(echo "$container_info" | jq -r '.[0].HostConfig.NetworkMode')
    
    echo -e "${CYAN}Current container name: $current_name${NC}"
    echo -e "${YELLOW}Enter new name for the container:${NC}"
    read -r new_name

    # Validate container name (only allow alphanumeric characters, underscores, and hyphens)
    if ! [[ "$new_name" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        echo -e "${RED}Error: Container name can only contain letters, numbers, underscores, and hyphens${NC}"
        return 1
    fi
    
    # Check if container is running
    is_running=$(docker inspect -f '{{.State.Running}}' "$container_id")
    
    if [ "$is_running" = "true" ]; then
        echo -e "${YELLOW}Container is running. Stopping container...${NC}"
        docker stop "$container_id"
    fi
    
    # Create new container command with proper quoting
    cmd="docker create"
    
    # Add restart policy
    cmd+=" --restart=$restart_policy"
    
    # Add network mode
    if [ "$network_mode" != "default" ]; then
        cmd+=" --network=$network_mode"
    fi
    
    # Add environment variables
    while IFS= read -r env_var; do
        [ -n "$env_var" ] && cmd+=" -e '$env_var'"
    done <<< "$env"
    
    # Add port mappings
    echo "$ports" | jq -r 'to_entries[] | .key + ":" + (.value[0].HostPort // "")' | while read -r port_mapping; do
        if [ -n "$port_mapping" ] && [ "$port_mapping" != "null" ]; then
            cmd+=" -p $port_mapping"
        fi
    done
    
    # Add volume mounts
    echo "$mounts" | jq -c '.[]' | while read -r mount; do
        source=$(echo "$mount" | jq -r '.Source')
        destination=$(echo "$mount" | jq -r '.Destination')
        if [ -n "$source" ] && [ -n "$destination" ]; then
            cmd+=" -v '$source':'$destination'"
        fi
    done
    
    # Add name and image (properly quoted)
    cmd+=" --name '$new_name' '$image'"
    
    # Remove old container
    echo -e "${YELLOW}Removing old container...${NC}"
    docker rm "$container_id"
    
    # Create new container
    echo -e "${YELLOW}Creating new container with name: $new_name${NC}"
    eval "$cmd"
    
    # Start new container if original was running
    if [ "$is_running" = "true" ]; then
        echo -e "${YELLOW}Starting new container...${NC}"
        docker start "$new_name"
    fi
    
    echo -e "${GREEN}Container successfully renamed from $current_name to $new_name${NC}"
}

# Function to remove a Docker container with cleanup options
remove_container() {
    log_debug "Starting remove_container function..."
    local TIMEOUT=30  # 30 seconds timeout for operations
    local exit_code=0
    
    while true; do
        # Check if there are any containers
        if [ -z "$(docker ps -a -q)" ]; then
            echo -e "${YELLOW}No containers found.${NC}"
            return 0
        fi

        echo -e "${YELLOW}Available Containers:${NC}"
        echo -e "${CYAN}=================================${NC}"
        
        # Store container IDs and details in arrays
        declare -a container_ids=()
        declare -a container_details=()
        
        # Get all container details first
        while IFS= read -r line || [ -n "$line" ]; do
            container_ids+=("$(echo "$line" | cut -f1)")
            container_details+=("$line")
        done < <(docker ps -a --format "{{.ID}}\t{{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}" || true)
        
        log_debug "Found ${#container_ids[@]} containers"
        
        # Display containers with numbers
        printf "${GREEN}%-4s %-15s %-20s %-15s %-30s %-10s${NC}\n" "No." "Container ID" "Name" "Status" "Image" "Created"
        
        for i in "${!container_details[@]}"; do
            detail="${container_details[$i]}"
            if [[ $(echo "$detail" | cut -f3) =~ Up ]]; then
                printf "${GREEN}%-4s %s${NC}\n" "$((i+1))" "$detail"
            else
                printf "${RED}%-4s %s${NC}\n" "$((i+1))" "$detail"
            fi
        done

        echo -e "\n${YELLOW}Options:${NC}"
        echo "1-N: Remove specific container"
        echo "a: Remove all stopped containers"
        echo "r: Remove all containers (including running)"
        echo "q: Quit"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice
        log_debug "User selected: $choice"

        case $choice in
            [0-9]*)
                echo -e "${YELLOW}Selected option: $choice${NC}"
                if [ "$choice" -ge 1 ] && [ "$choice" -le "${#container_ids[@]}" ]; then
                    container_id="${container_ids[$((choice-1))]}"
                    log_debug "Selected container ID: $container_id"
                    
                    # Get container details
                    container_name=$(docker inspect --format '{{.Name}}' "$container_id" 2>/dev/null | sed 's/\///')
                    container_status=$(docker inspect --format '{{.State.Status}}' "$container_id" 2>/dev/null)
                    container_image=$(docker inspect --format '{{.Config.Image}}' "$container_id" 2>/dev/null)
                    
                    # Get associated resources
                    container_volumes=$(docker inspect --format '{{range .Mounts}}{{if .Name}}{{.Name}} {{end}}{{end}}' "$container_id" | tr ' ' '\n' | grep -v '^$' || true)
                    container_networks=$(docker inspect --format '{{range $net, $v := .NetworkSettings.Networks}}{{$net}} {{end}}' "$container_id" | tr ' ' '\n' | grep -v '^$' || true)
                    
                    echo -e "\n${CYAN}Container Details:${NC}"
                    echo -e "Name: ${GREEN}${container_name}${NC}"
                    echo -e "ID: ${GREEN}${container_id}${NC}"
                    echo -e "Image: ${GREEN}${container_image}${NC}"
                    echo -e "Status: ${GREEN}${container_status}${NC}"
                    
                    if [ -n "$container_volumes" ]; then
                        echo -e "\nAssociated Volumes:"
                        while IFS= read -r vol || [ -n "$vol" ]; do
                            [ -n "$vol" ] && echo -e "  - ${YELLOW}$vol${NC}"
                        done <<< "$container_volumes"
                    fi
                    
                    if [ -n "$container_networks" ]; then
                        echo -e "\nAssociated Networks:"
                        while IFS= read -r net || [ -n "$net" ]; do
                            [ -n "$net" ] && echo -e "  - ${YELLOW}$net${NC}"
                        done <<< "$container_networks"
                    fi
                    
                    # Check if container is running
                    if [[ "$container_status" == "running" ]]; then
                        echo -e "\n${YELLOW}Container is currently running.${NC}"
                        read -rp "$(echo -e ${YELLOW}"Do you want to stop and remove it? [y/N]: "${NC})" confirm
                        if [[ $confirm =~ ^[Yy]$ ]]; then
                            echo -e "${YELLOW}Stopping container...${NC}"
                            if ! docker stop "$container_id" >/dev/null 2>&1; then
                                log_error "Failed to stop container:"
                                docker stop "$container_id" 2>&1 | sed 's/^/    /'
                                sleep 2
                                continue
                            fi
                            echo -e "${GREEN}Container stopped successfully.${NC}"
                        else
                            echo -e "${YELLOW}Operation cancelled.${NC}"
                            sleep 2
                            continue
                        fi
                    fi
                    
                    # Confirm removal
                    read -rp "$(echo -e ${YELLOW}"Are you sure you want to remove container '${GREEN}${container_name}${YELLOW}' (${container_id})? [y/N]: "${NC})" confirm
                    if [[ ! $confirm =~ ^[Yy]$ ]]; then
                        echo -e "${YELLOW}Operation cancelled.${NC}"
                        sleep 2
                        continue
                    fi
                    
                    # Remove the container
                    echo -e "${YELLOW}Removing container...${NC}"
                    if ! docker rm -f "$container_id" >/dev/null 2>&1; then
                        log_error "Failed to remove container:"
                        docker rm -f "$container_id" 2>&1 | sed 's/^/    /'
                        sleep 2
                        continue
                    fi
                    
                    # Verify removal
                    if ! docker inspect "$container_id" >/dev/null 2>&1; then
                        echo -e "${GREEN}Container removed successfully.${NC}"
                        
                        # Cleanup options
                        echo -e "\n${CYAN}Cleanup Options:${NC}"
                        declare -a cleanup_options=()
                        declare -a cleanup_descriptions=()
                        
                        # Check if image is not used by other containers
                        if ! docker ps -a --format '{{.Image}}' | grep -q "^${container_image}$"; then
                            cleanup_options+=("image")
                            cleanup_descriptions+=("Remove unused image: $container_image")
                        fi
                        
                        # Check for volumes
                        if [ -n "$container_volumes" ]; then
                            cleanup_options+=("volumes")
                            cleanup_descriptions+=("Remove associated volumes")
                        fi
                        
                        # Check for custom networks
                        if [ -n "$container_networks" ]; then
                            cleanup_options+=("networks")
                            cleanup_descriptions+=("Remove associated networks (if not in use)")
                        fi
                        
                        if [ ${#cleanup_options[@]} -gt 0 ]; then
                            cleanup_options+=("all")
                            cleanup_descriptions+=("Remove all associated resources")
                            cleanup_options+=("none")
                            cleanup_descriptions+=("Keep all resources")
                            
                            echo -e "The following cleanup options are available:"
                            for i in "${!cleanup_options[@]}"; do
                                echo "$((i+1)). ${cleanup_descriptions[$i]}"
                            done
                            
                            read -rp "$(echo -e ${YELLOW}"Select cleanup option (1-${#cleanup_options[@]}): "${NC})" cleanup_choice
                            
                            if [[ "$cleanup_choice" =~ ^[0-9]+$ ]] && [ "$cleanup_choice" -ge 1 ] && [ "$cleanup_choice" -le "${#cleanup_options[@]}" ]; then
                                selected_option=${cleanup_options[$((cleanup_choice-1))]}
                                log_debug "Selected cleanup option: $selected_option"
                                
                                case $selected_option in
                                    "image")
                                        echo -e "${YELLOW}Removing image '${container_image}'...${NC}"
                                        if docker rmi "$container_image" >/dev/null 2>&1; then
                                            echo -e "${GREEN}Image removed successfully.${NC}"
                                        else
                                            log_error "Failed to remove image"
                                        fi
                                        ;;
                                    "volumes")
                                        echo -e "${YELLOW}Removing associated volumes...${NC}"
                                        while IFS= read -r vol || [ -n "$vol" ]; do
                                            if [ -n "$vol" ]; then
                                                if docker volume rm "$vol" >/dev/null 2>&1; then
                                                    echo -e "${GREEN}Volume '$vol' removed successfully.${NC}"
                                                else
                                                    log_error "Failed to remove volume '$vol'"
                                                fi
                                            fi
                                        done <<< "$container_volumes"
                                        ;;
                                    "networks")
                                        echo -e "${YELLOW}Removing associated networks...${NC}"
                                        while IFS= read -r net || [ -n "$net" ]; do
                                            if [ -n "$net" ] && [ "$net" != "bridge" ] && [ "$net" != "host" ] && [ "$net" != "none" ]; then
                                                if docker network inspect "$net" 2>/dev/null | grep -q '"Containers": {}'; then
                                                    if docker network rm "$net" >/dev/null 2>&1; then
                                                        echo -e "${GREEN}Network '$net' removed successfully.${NC}"
                                                    else
                                                        log_error "Failed to remove network '$net'"
                                                    fi
                                                else
                                                    echo -e "${YELLOW}Network '$net' is still in use, skipping...${NC}"
                                                fi
                                            fi
                                        done <<< "$container_networks"
                                        ;;
                                    "all")
                                        # Remove image if not used by other containers
                                        if ! docker ps -a --format '{{.Image}}' | grep -q "^${container_image}$"; then
                                            echo -e "${YELLOW}Removing image '${container_image}'...${NC}"
                                            if docker rmi "$container_image" >/dev/null 2>&1; then
                                                echo -e "${GREEN}Image removed successfully.${NC}"
                                            else
                                                log_error "Failed to remove image"
                                            fi
                                        fi
                                        
                                        # Remove volumes
                                        if [ -n "$container_volumes" ]; then
                                            echo -e "${YELLOW}Removing associated volumes...${NC}"
                                            while IFS= read -r vol || [ -n "$vol" ]; do
                                                if [ -n "$vol" ]; then
                                                    if docker volume rm "$vol" >/dev/null 2>&1; then
                                                        echo -e "${GREEN}Volume '$vol' removed successfully.${NC}"
                                                    else
                                                        log_error "Failed to remove volume '$vol'"
                                                    fi
                                                fi
                                            done <<< "$container_volumes"
                                        fi
                                        
                                        # Remove networks
                                        if [ -n "$container_networks" ]; then
                                            echo -e "${YELLOW}Removing associated networks...${NC}"
                                            while IFS= read -r net || [ -n "$net" ]; do
                                                if [ -n "$net" ] && [ "$net" != "bridge" ] && [ "$net" != "host" ] && [ "$net" != "none" ]; then
                                                    if docker network inspect "$net" 2>/dev/null | grep -q '"Containers": {}'; then
                                                        if docker network rm "$net" >/dev/null 2>&1; then
                                                            echo -e "${GREEN}Network '$net' removed successfully.${NC}"
                                                        else
                                                            log_error "Failed to remove network '$net'"
                                                        fi
                                                    else
                                                        echo -e "${YELLOW}Network '$net' is still in use, skipping...${NC}"
                                                    fi
                                                fi
                                            done <<< "$container_networks"
                                        fi
                                        ;;
                                    "none")
                                        echo -e "${YELLOW}Keeping all associated resources.${NC}"
                                        ;;
                                esac
                            fi
                        else
                            echo -e "${YELLOW}No additional resources to clean up.${NC}"
                        fi
                    else
                        log_error "Failed to verify container removal"
                    fi
                    
                    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                    read -r
                else
                    echo -e "${RED}Invalid container number.${NC}"
                    sleep 2
                fi
                ;;
            [aA])
                echo -e "${YELLOW}Removing all stopped containers...${NC}"
                if docker container prune -f >/dev/null 2>&1; then
                    echo -e "${GREEN}All stopped containers removed successfully.${NC}"
                else
                    log_error "Failed to remove some containers"
                fi
                sleep 2
                ;;
            [rR])
                read -rp "$(echo -e ${RED}"This will remove ALL containers, including running ones. Continue? [y/N]: "${NC})" confirm
                if [[ $confirm =~ ^[Yy]$ ]]; then
                    echo -e "${YELLOW}Stopping all containers...${NC}"
                    docker stop $(docker ps -q) >/dev/null 2>&1 || true
                    
                    echo -e "${YELLOW}Removing all containers...${NC}"
                    if docker rm -f $(docker ps -aq) >/dev/null 2>&1; then
                        echo -e "${GREEN}All containers removed successfully.${NC}"
                    else
                        log_error "Failed to remove some containers"
                    fi
                else
                    echo -e "${YELLOW}Operation cancelled.${NC}"
                fi
                sleep 2
                ;;
            [qQ])
                log_debug "User chose to quit"
                return 0
                ;;
            *)
                echo -e "${RED}Invalid choice.${NC}"
                sleep 2
                ;;
        esac
    done
}
