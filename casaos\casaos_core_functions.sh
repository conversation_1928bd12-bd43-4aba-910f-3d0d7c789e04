#!/bin/bash

# Add this new function at the beginning of the file
check_casaos_installed() {
    if command -v casaos-cli >/dev/null 2>&1; then
        return 0  # CasaOS is installed
    else
        return 1  # CasaOS is not installed
    fi
}

# Add this new function
install_casaos() {
    if check_casaos_installed; then
        echo -e "${YELLOW}CasaOS is already installed.${NC}"
        return 0
    fi

    echo -e "${YELLOW}Installing CasaOS...${NC}"
    curl -fsSL https://get.casaos.io | sudo bash

    install_rclone # Assuming install_rclone is defined elsewhere

    if check_casaos_installed; then
        echo -e "${GREEN}CasaOS installed successfully!${NC}"

        # Ask user if they want to restore configuration
        read -rp "$(echo -e ${YELLOW}"Would you like to restore CasaOS configuration from backup? (y/n): "${NC})" restore_choice
        if [[ $restore_choice =~ ^[Yy]$ ]]; then
            restore_casaos_config # This function is in another file, ensure it's sourced
        fi

        return 0
    else
        echo -e "${RED}Failed to install Casa<PERSON>. Please check the installation logs.${NC}"
        return 1
    fi
}

# Function to get list of installed CasaOS apps
get_casaos_apps() {
    if [ -d "/DATA/AppData/" ]; then
        ls -1 "/DATA/AppData/"
    fi
}

# Function to check if container exists (either running or stopped)
container_exists() {
    local container_name="$1"
    echo -e "${YELLOW}Checking container: $container_name${NC}"
    # Try multiple times as container might be in transition
    local max_attempts=5
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if docker ps -aq -f name="^${container_name}$" >/dev/null 2>&1; then
            echo -e "${GREEN}Container $container_name exists${NC}"
            return 0
        fi
        sleep 2
        attempt=$((attempt + 1))
    done

    echo -e "${RED}Container $container_name does not exist${NC}"
    return 1
}

# Function to ensure container is ready for operations
ensure_container_ready() {
    local container_name="$1"
    local max_attempts=5
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        echo -e "${GREEN}Container $container_name exists${NC}"
        if docker inspect "$container_name" >/dev/null 2>&1; then
            return 0
        fi
        sleep 2
        attempt=$((attempt + 1))
    done

    echo -e "${RED}Container $container_name is not ready${NC}"
    return 1
}

# Function to safely stop container
stop_container() {
    local container_name="$1"

    if ! ensure_container_ready "$container_name"; then
        echo -e "${RED}Container not ready for stopping${NC}"
        return 1
    fi

    echo -e "${YELLOW}Stopping container $container_name...${NC}"
    if docker stop "$container_name" >/dev/null 2>&1; then
        echo -e "${GREEN}Container $container_name stopped${NC}"
        return 0
    else
        echo -e "${RED}Failed to stop container $container_name${NC}"
        return 1
    fi
}

# Function to manage container
manage_container() {
    local action="$1"  # 'stop' or 'start'
    local container_name="$2"

    echo -e "${YELLOW}Managing container: $container_name, Action: $action${NC}"

    if [ "$action" = "stop" ]; then
        stop_container "$container_name"
    elif [ "$action" = "start" ]; then
        echo -e "${YELLOW}Starting container $container_name...${NC}"
        docker start "$container_name"
        # Wait briefly and verify the container is running
        sleep 2
        if docker ps -q -f name="^${container_name}$" >/dev/null 2>&1; then
            echo -e "${GREEN}Container $container_name started successfully.${NC}"
        else
            echo -e "${RED}Failed to start container $container_name. Retrying...${NC}"
            docker start "$container_name"
            sleep 2
            if docker ps -q -f name="^${container_name}$" >/dev/null 2>&1; then
                echo -e "${GREEN}Container $container_name started successfully on second attempt.${NC}"
            else
                echo -e "${RED}Failed to start container $container_name after retry.${NC}"
            fi
        fi
    fi
}

# Function to backup a single app
backup_single_app() {
    local app_name="$1"
    local timestamp="$2"

    if ! container_exists "$app_name"; then
        echo -e "${RED}Skipping $app_name: No container found${NC}"
        return 1
    fi

    echo -e "${GREEN}Backing up $app_name...${NC}"
    echo -e "${YELLOW}Stopping $app_name using CasaOS CLI...${NC}"
    casaos-cli app-management stop "$app_name"
    echo -e "${YELLOW}Waiting 10 seconds for $app_name to stop completely...${NC}"
    sleep 10

    local archive_name="${app_name}_${timestamp}.tar.gz"
    echo -e "${GREEN}Compressing $app_name...${NC}"

    # Calculate size for this app
    local total_size=$(du -sb "/DATA/AppData/$app_name" | cut -f1)

    # Create tar for this app
    cd /DATA/AppData && tar -czf - "$app_name" | \
    pv -s "$total_size" > "/tmp/$archive_name"

    echo -e "${GREEN}Uploading $app_name backup...${NC}"
    echo "Executing rclone command: rclone copy \"/tmp/$archive_name\" \"$RCLONE_REMOTE:/Backups/CasaOS/\" --progress"
    rclone copy "/tmp/$archive_name" "$RCLONE_REMOTE:/Backups/CasaOS/" \
        --progress \
        --checkers 8 \
        --transfers 4 \
        --stats 1s
    rm -f "/tmp/$archive_name"

    # Start container back
    echo -e "${YELLOW}Starting $app_name using CasaOS CLI...${NC}"
    casaos-cli app-management start "$app_name"

    echo -e "${GREEN}Backup completed for $app_name${NC}"
}

# Function to handle backup selection
casaos_backup_menu() {
    # Ensure pv is installed for progress monitoring
    manage_package "install" "pv" "pv" # Assuming manage_package is defined elsewhere

    local apps=($(get_casaos_apps))
    if [ ${#apps[@]} -eq 0 ]; then
        echo -e "${RED}No apps found in CasaOS.${NC}"
        return 1
    fi

    options=("0. Backup All Apps")
    for i in "${!apps[@]}"; do
        options+=("$((i+1)). ${apps[i]}")
    done
    options+=("$((${#apps[@]}+1)). Back")

    while true; do
        create_menu "Select Apps to Backup" "${options[@]}" # Assuming create_menu is defined elsewhere
        echo -e "${YELLOW}For multiple selections, enter numbers separated by spaces (e.g., 1 3 4)${NC}"
        read -rp "$(echo -e ${YELLOW}"Enter your choice(s): "${NC})" -a choices

        if [[ "${choices[0]}" == "$((${#apps[@]}+1))" ]]; then
            return 0
        fi

        local selected_apps=()
        local backup_all=false

        for choice in "${choices[@]}"; do
            if [[ "$choice" == "0" ]]; then
                backup_all=true
                break
            elif [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -le "${#apps[@]}" ]; then
                selected_apps+=("${apps[$((choice-1))]}")
            fi
        done

        local timestamp=$(date -d "+3 hours" +"%d%b%Y_%I:%M%p")

        if [ "$backup_all" = true ]; then
            echo -e "${GREEN}Checking all apps...${NC}"
            for app in "${apps[@]}"; do
                backup_single_app "$app" "$timestamp"
            done
        else
            if [ ${#selected_apps[@]} -eq 0 ]; then
                echo -e "${RED}No valid selections made.${NC}"
                continue
            fi

            for app in "${selected_apps[@]}"; do
                backup_single_app "$app" "$timestamp"
            done
        fi

        echo -e "${GREEN}Backup completed successfully!${NC}"
        break
    done
}