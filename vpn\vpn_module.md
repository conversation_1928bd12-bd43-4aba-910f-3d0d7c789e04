# VPN Module

This module provides comprehensive functionality for setting up and managing various VPN solutions including WireGuard, V2Ray, XRay, Shadowsocks, and Socket5.

## Files

### vpn_function.sh
Core VPN utility functions.

**Key Functions:**
- `get_public_ip()`: Gets the public IP address
- `get_valid_port()`: Validates and returns a usable port
- `validate_input()`: Validates user input for VPN configuration
- `create_qrcode_for_phone()`: Creates QR codes for mobile VPN configuration
- `show_pc_config()`: Shows configuration for PC clients
- `get_ip_subnet()`: Determines IP subnet information
- `remove_wg_config_files()`: Removes WireGuard configuration files
- `remove_xray_config_files()`: Removes XRay configuration files
- `stop_and_remove_container()`: Stops and removes VPN containers
- `remove_wireguard()`: Removes WireGuard installation
- `remove_shadowsock()`: Removes Shadowsocks installation
- `remove_xray()`: Removes XRay installation
- `remove_all_vpns()`: Removes all VPN installations
- `install_all_vpns()`: Installs all supported VPN solutions

### vpn_menu.sh
Provides a menu interface for VPN operations.

**Key Functions:**
- `vpn_menu()`: Main menu for VPN operations

### wireguard.sh
WireGuard VPN implementation.

### v2ray.sh
V2Ray VPN implementation.

### xray.sh
XRay VPN implementation.

### shadowsocks.sh
Shadowsocks VPN implementation.

### socket5.sh
Socket5 proxy implementation.

### 3x-ui.sh
3X-UI panel for XRay management.

### create_vpn_config_files.sh
Manages the creation of configuration files for various VPN implementations.

## Usage

The VPN module provides functionality for setting up secure VPN connections using various protocols and implementations.

To access VPN functionality:
1. Select "VPN Menu" from the main menu
2. Choose from available VPN implementations (WireGuard, V2Ray, XRay, etc.)
3. Follow the interactive prompts to configure your VPN

## Integration with Other Modules

The VPN module can be used by any module that requires secure communication or access to remote resources. It provides the infrastructure for secure connections through various VPN implementations. 