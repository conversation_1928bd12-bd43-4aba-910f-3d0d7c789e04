#!/bin/bash

install_socks5_proxy() {
    install_docker

    # Check if any Gluetun containers are running
    local gluetun_containers=$(docker ps --filter ancestor=qmcgaw/gluetun --format '{{.Names}}')

    local selected_gluetun_container=""

    if [ -z "$gluetun_containers" ]; then
        echo "No running Gluetun containers found."
        echo "Please install a Gluetun container first."
        # Offer options to install Gluetun
        echo "1. Install New Gluetun Container with PIA Config"
        echo "2. Install New Gluetun Container with FastestVPN Config"
        echo "3. Install New Gluetun Container with Custom Config"
        read -p "Enter your choice: " gluetun_choice

        case $gluetun_choice in
            1) install_gluetun_pia ;;
            2) install_gluetun_fastestvpn ;;
            3) install_gluetun_custom ;;
            *) echo "Invalid choice. Please install Gluetun manually and try again." ; return 1 ;;
        esac

        # After installation, check for running Gluetun containers again
        gluetun_containers=$(docker ps --filter ancestor=qmcgaw/gluetun --format '{{.Names}}')
        if [ -z "$gluetun_containers" ]; then
            echo "Gluetun installation failed or container did not start."
            return 1
        fi
        # If only one container was installed, select it automatically
        if [ "$(echo "$gluetun_containers" | wc -l)" -eq 1 ]; then
            selected_gluetun_container=$gluetun_containers
        else
             echo "Multiple Gluetun containers are now running. Please select one:"
             selected_gluetun_container=$(select_gluetun_container "$gluetun_containers")
             if [ -z "$selected_gluetun_container" ]; then
                 echo "No Gluetun container selected. Installation cancelled."
                 return 1
             fi
        fi

    else
        # If Gluetun containers are running, let the user select one or install a new one
        echo "Found running Gluetun containers:"
        local options=()
        local i=1
        while IFS= read -r container; do
            options+=("$i. $container")
            i=$((i+1))
        done <<< "$gluetun_containers"
        options+=("$i. Install a new Gluetun container") # Add the new option

        local menu_title="Select a Gluetun container or install new"
        create_menu "$menu_title" "${options[@]}" # Use the existing create_menu function

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le "$i" ]; then
            if [ "$choice" -eq "$i" ]; then
                # User chose to install a new Gluetun container
                echo "Please install a Gluetun container first."
                # Offer options to install Gluetun
                echo "1. Install New Gluetun Container with PIA Config"
                echo "2. Install New Gluetun Container with FastestVPN Config"
                echo "3. Install New Gluetun Container with Custom Config"
                read -p "Enter your choice: " gluetun_choice

                # Call Gluetun installation functions directly to allow interactive prompts
                case $gluetun_choice in
                    1) install_gluetun_pia
                       local install_status=$? # Capture the exit status of the installation function
                       ;;
                    2) install_gluetun_fastestvpn
                       local install_status=$? # Capture the exit status of the installation function
                       ;;
                    3) install_gluetun_custom
                       local install_status=$? # Capture the exit status of the installation function
                       ;;
                    *) echo "Invalid choice. Please install Gluetun manually and try again." ; return 1 ;;
                esac

                # Check if the Gluetun installation was successful
                if [ "$install_status" -ne 0 ]; then
                    echo "Gluetun installation failed."
                    return 1
                fi

                # Find the most recently created Gluetun container
                local newly_installed_container_name=$(docker ps -a --filter ancestor=qmcgaw/gluetun --format '{{.CreatedAt}} {{.Names}}' | sort -r | head -n 1 | sed -E 's/.*(gluetun-.*)/\1/')

                if [ -z "$newly_installed_container_name" ]; then
                    echo "Error: Could not determine the name of the newly installed Gluetun container."
                    return 1
                fi

                # Automatically select the newly installed container
                selected_gluetun_container="$newly_installed_container_name"
                echo "Automatically selected the newly installed Gluetun container: ${selected_gluetun_container}"

                # After installation, check for running Gluetun containers again (optional, mainly for verification)
                gluetun_containers=$(docker ps --filter ancestor=qmcgaw/gluetun --format '{{.Names}}')
                if [ -z "$gluetun_containers" ]; then
                    echo "Warning: No running Gluetun containers found after installation."
                    # Decide how to handle this - maybe return 1 or proceed with the assumption the new container is running
                    # For now, let's assume the newly_installed_container_name is correct and proceed.
                fi
            else
                # User selected an existing container
                selected_gluetun_container=$(echo "$gluetun_containers" | sed -n "${choice}p")
            fi
        else
            echo "Invalid selection. Installation cancelled."
            return 1
        fi
    fi

    # Ask user for SOCKS5 proxy port
    local default_socks5_port="9091"
    read -p "Enter SOCKS5 proxy port [$default_socks5_port]: " SOCKS_PORT
    SOCKS_PORT=${SOCKS_PORT:-$default_socks5_port}

    # Validate port
    if ! [[ "$SOCKS_PORT" =~ ^[0-9]+$ ]] || [ "$SOCKS_PORT" -lt 1 ] || [ "$SOCKS_PORT" -gt 65535 ]; then
        echo "Error: Invalid port number. Port must be between 1 and 65535."
        return 1
    fi

    # Get next available container name
    local default_name=$(get_next_socks5_number)

    # Ask user for container name
    while true; do
        read -p "Enter container name [$default_name]: " CONTAINER_NAME
        CONTAINER_NAME=${CONTAINER_NAME:-$default_name}

        # Check if the chosen name already exists
        if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
            echo "Error: A container with name '$CONTAINER_NAME' already exists."
            read -p "Do you want to try a different name? [Y/n]: " retry
            if [[ -z "$retry" || "${retry:0:1}" =~ [Yy] ]]; then
                continue
            else
                echo "Installation cancelled."
                return 1
            fi
        fi

        if [[ $CONTAINER_NAME =~ ^[a-zA-Z0-9_-]+$ ]]; then
            break
        else
            echo "Error: Container name can only contain letters, numbers, underscores, and hyphens."
            echo "Please try again."
            echo
        fi
    done

    # Run the SOCKS5 proxy container, connected to the selected Gluetun container's network
    docker run -d \
        --name ${CONTAINER_NAME} \
        -e SOCKS_PORT=${SOCKS_PORT} \
        --network=container:${selected_gluetun_container} \
        fazee6/socks5proxy

    if [ $? -eq 0 ]; then
        echo "SOCKS5 proxy container 'socks5' started successfully on port ${SOCKS_PORT} using ${selected_gluetun_container}'s network."
    else
        echo "Error: Failed to start SOCKS5 proxy container."
        return 1
    fi
}

remove_socks5_proxy() {
    # Find all SOCKS5 proxy containers
    local socks5_containers=$(docker ps -a --filter ancestor=fazee6/socks5proxy --format '{{.Names}}')

    if [ -z "$socks5_containers" ]; then
        echo "No SOCKS5 proxy containers found."
        return
    fi

    local selected_container=""

    # If there's only one container, use it directly
    if [ "$(echo "$socks5_containers" | wc -l)" -eq 1 ]; then
        selected_container=$socks5_containers
    else
        # Show numbered list of containers
        echo "Found multiple SOCKS5 proxy containers:"
        local options=()
        local i=1
        while IFS= read -r container; do
            options+=("$i. $container")
            i=$((i+1))
        done <<< "$socks5_containers"

        select choice in "${options[@]}"; do
            if [[ -n "$choice" ]]; then
                # Extract container name from the selected option
                selected_container=$(echo "$choice" | cut -d' ' -f2)
                break
            else
                echo "Invalid selection. Please try again."
            fi
        done
    fi

    if [ -z "$selected_container" ]; then
        echo "No SOCKS5 proxy container selected. Removal cancelled."
        return 1
    fi

    # Find the associated Gluetun container ID or name from the network mode
    local associated_gluetun_container_id_or_name=$(docker inspect "$selected_container" --format '{{.HostConfig.NetworkMode}}' 2>/dev/null | sed -E 's/container:(.*)/\1/')

    local associated_gluetun_container_name=""
    if [ -n "$associated_gluetun_container_id_or_name" ]; then
        # Get the container name from the ID or name
        associated_gluetun_container_name=$(docker inspect "$associated_gluetun_container_id_or_name" --format '{{.Name}}' 2>/dev/null | sed 's#/##')
    fi

    # Remove the selected SOCKS5 proxy container
    echo "Removing SOCKS5 proxy container '$selected_container'..."
    docker rm -f "$selected_container"
    local remove_status=$? # Capture exit status of removal

    if [ $remove_status -eq 0 ]; then
        echo "SOCKS5 proxy container '$selected_container' removed successfully."

        # Ask to remove the associated Gluetun container if found and name was retrieved
        if [ -n "$associated_gluetun_container_name" ]; then
            echo "The SOCKS5 proxy container was using the network of Gluetun container: $associated_gluetun_container_name"
            read -p "Do you want to remove the associated Gluetun container '$associated_gluetun_container_name' as well? [y/N]: " remove_gluetun_choice
            if [[ "$remove_gluetun_choice" =~ ^[Yy]$ ]]; then
                echo "Removing associated Gluetun container '$associated_gluetun_container_name'..."
                remove_gluetun "$associated_gluetun_container_name" # Call the remove_gluetun function
            else
                echo "Associated Gluetun container '$associated_gluetun_container_name' was not removed."
            fi
        fi
    else
        echo "Error: Failed to remove SOCKS5 proxy container '$selected_container'."
        return 1
    fi
}

# Helper function to get the next available SOCKS5 container number
get_next_socks5_number() {
    local prefix="socks5-"
    local i=1

    while true; do
        if ! docker ps -a --format '{{.Names}}' | grep -q "^${prefix}${i}$"; then
            echo "${prefix}${i}"
            return
        fi
        ((i++))
    done
}


# Helper function to select a Gluetun container
select_gluetun_container() {
    local containers="$1"
    local options=()
    local i=1
    while IFS= read -r container; do
        if [ -n "$container" ]; then # Check if container name is not empty
            options+=("$i. $container")
            i=$((i+1))
        fi
    done <<< "$containers"

    # The select command in bash returns the index of the selected item, not the value directly when used in a subshell like this.
    # We need to modify this to return the selected container name or a special value for "install new".
    # Let's return the selected container name directly. If the user selects the "Install new" option, we'll handle that in the calling function.
    local selected_index
    select choice in "${options[@]}"; do
        if [[ -n "$choice" ]]; then
            # Extract container name from the selected option
            echo "$choice" | cut -d' ' -f2
            return 0 # Indicate success
        else
            echo "Invalid selection. Please try again."
        fi
    done
    return 1 # Indicate failure
}

# Source the main functions file to get access to install_docker, install_gluetun_pia, install_gluetun_custom
source ./gluetun/gluetun_functions.sh