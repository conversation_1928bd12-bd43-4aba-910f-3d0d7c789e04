#!/bin/bash

# Function to backup Netbird configuration files
backup_netbird_config() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi
    
    local backup_dir="/opt/netbird_backups"
    local config_backup_file="netbird_config_backup.tar.gz"
    local config_backup_path="$backup_dir/$config_backup_file"
    local rclone_dest="$RCLONE_REMOTE:/Backups/Netbird"
    
    echo "Backing up Netbird configuration files..."
    
    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Backup cannot proceed."
        return 1
    fi
    
    # Create backup directory
    mkdir -p "$backup_dir"
    
    # Check compression tools
    if ! check_compression_tools; then
        echo "Required compression tools are not available."
        return 1
    fi
    
    # Create list of config files to backup
    local config_files=()
    cd "$netbird_path"
    
    # Common configuration files
    [ -f "docker-compose.yml" ] && config_files+=("docker-compose.yml")
    [ -f "management.json" ] && config_files+=("management.json")
    [ -f "turnserver.conf" ] && config_files+=("turnserver.conf")
    [ -f "setup.env" ] && config_files+=("setup.env")
    [ -f "Caddyfile" ] && config_files+=("Caddyfile")
    [ -f "zitadel.env" ] && config_files+=("zitadel.env")
    [ -f "dashboard.env" ] && config_files+=("dashboard.env")
    [ -f "relay.env" ] && config_files+=("relay.env")
    [ -f "zdb.env" ] && config_files+=("zdb.env")
    
    if [ ${#config_files[@]} -eq 0 ]; then
        echo "No configuration files found to backup."
        return 1
    fi
    
    # Create tar archive of config files
    echo "Creating configuration backup archive..."
    if tar -czf "$config_backup_path" "${config_files[@]}" 2>/dev/null; then
        echo "Configuration backup created successfully."
        
        # Upload to cloud storage
        echo "Uploading configuration backup to cloud storage..."
        if rclone copy "$config_backup_path" "$rclone_dest" --progress; then
            echo "Configuration backup uploaded successfully."
            rm -f "$config_backup_path"
            return 0
        else
            echo "Failed to upload configuration backup."
            rm -f "$config_backup_path"
            return 1
        fi
    else
        echo "Failed to create configuration backup."
        return 1
    fi
}

# Function to backup Netbird data (databases and persistent data)
backup_netbird_data() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi
    
    local backup_dir="/opt/netbird_backups"
    local data_backup_file="netbird_data_backup.tar.gz"
    local data_backup_path="$backup_dir/$data_backup_file"
    local rclone_dest="$RCLONE_REMOTE:/Backups/Netbird"
    
    echo "Backing up Netbird data..."
    
    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Backup cannot proceed."
        return 1
    fi
    
    # Create backup directory
    mkdir -p "$backup_dir"
    
    # Check compression tools
    if ! check_compression_tools; then
        echo "Required compression tools are not available."
        return 1
    fi
    
    # Stop management service to ensure consistent backup
    echo "Stopping management service for consistent backup..."
    cd "$netbird_path"
    docker compose stop management
    
    # Backup management data using docker cp
    echo "Backing up management service data..."
    local temp_data_dir="$backup_dir/netbird_data_temp"
    mkdir -p "$temp_data_dir"
    
    # Copy data from management container
    if docker compose cp -a management:/var/lib/netbird/ "$temp_data_dir/"; then
        echo "Management data copied successfully."
        
        # Create tar archive of data
        echo "Creating data backup archive..."
        cd "$backup_dir"
        if tar -czf "$data_backup_file" -C "$temp_data_dir" .; then
            echo "Data backup created successfully."
            
            # Clean up temp directory
            rm -rf "$temp_data_dir"
            
            # Upload to cloud storage
            echo "Uploading data backup to cloud storage..."
            if rclone copy "$data_backup_path" "$rclone_dest" --progress; then
                echo "Data backup uploaded successfully."
                rm -f "$data_backup_path"
                
                # Restart management service
                echo "Restarting management service..."
                cd "$netbird_path"
                docker compose start management
                return 0
            else
                echo "Failed to upload data backup."
                rm -f "$data_backup_path"
                rm -rf "$temp_data_dir"
                cd "$netbird_path"
                docker compose start management
                return 1
            fi
        else
            echo "Failed to create data backup archive."
            rm -rf "$temp_data_dir"
            cd "$netbird_path"
            docker compose start management
            return 1
        fi
    else
        echo "Failed to copy management data."
        rm -rf "$temp_data_dir"
        cd "$netbird_path"
        docker compose start management
        return 1
    fi
}

# Function to perform full Netbird backup (config + data)
backup_netbird_full() {
    echo "Starting full Netbird backup..."
    echo "==============================="
    
    local success=true
    
    # Backup configuration
    echo "Step 1: Backing up configuration files..."
    if ! backup_netbird_config; then
        echo "Configuration backup failed."
        success=false
    fi
    
    # Backup data
    echo "Step 2: Backing up data..."
    if ! backup_netbird_data; then
        echo "Data backup failed."
        success=false
    fi
    
    if $success; then
        echo "Full Netbird backup completed successfully."
        return 0
    else
        echo "Full Netbird backup completed with errors."
        return 1
    fi
}

# Function to restore Netbird configuration
restore_netbird_config() {
    local backup_dir="/opt/netbird_backups"
    local config_backup_file="netbird_config_backup.tar.gz"
    local temp_config_backup="$backup_dir/$config_backup_file"
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"
    
    echo "Restoring Netbird configuration..."
    
    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi
    
    # Create backup directory
    mkdir -p "$backup_dir"
    
    # Download configuration backup
    echo "Downloading configuration backup from cloud storage..."
    rm -f "$temp_config_backup"
    
    if ! rclone copy "$rclone_source/$config_backup_file" "$backup_dir" --progress; then
        echo "Failed to download configuration backup."
        return 1
    fi
    
    if [ ! -f "$temp_config_backup" ]; then
        echo "Configuration backup file not found after download."
        return 1
    fi
    
    # Ask for installation directory
    echo "Where would you like to restore the Netbird configuration?"
    read -rp "Enter path (default: /opt/netbird): " restore_path
    restore_path=${restore_path:-/opt/netbird}
    
    # Create restore directory
    mkdir -p "$restore_path"
    
    # Extract configuration files
    echo "Extracting configuration files..."
    cd "$restore_path"
    if tar -xzf "$temp_config_backup"; then
        echo "Configuration restored successfully to $restore_path"
        rm -f "$temp_config_backup"
        return 0
    else
        echo "Failed to extract configuration files."
        rm -f "$temp_config_backup"
        return 1
    fi
}

# Function to restore Netbird data
restore_netbird_data() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Please restore configuration first."
        return 1
    fi
    
    local backup_dir="/opt/netbird_backups"
    local data_backup_file="netbird_data_backup.tar.gz"
    local temp_data_backup="$backup_dir/$data_backup_file"
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"
    
    echo "Restoring Netbird data..."
    
    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi
    
    # Create backup directory
    mkdir -p "$backup_dir"
    
    # Download data backup
    echo "Downloading data backup from cloud storage..."
    rm -f "$temp_data_backup"
    
    if ! rclone copy "$rclone_source/$data_backup_file" "$backup_dir" --progress; then
        echo "Failed to download data backup."
        return 1
    fi
    
    if [ ! -f "$temp_data_backup" ]; then
        echo "Data backup file not found after download."
        return 1
    fi
    
    # Stop management service
    echo "Stopping management service..."
    cd "$netbird_path"
    docker compose stop management
    
    # Extract and restore data
    echo "Restoring management data..."
    local temp_restore_dir="$backup_dir/restore_temp"
    mkdir -p "$temp_restore_dir"
    
    cd "$temp_restore_dir"
    if tar -xzf "$temp_data_backup"; then
        # Copy restored data back to container
        cd "$netbird_path"
        if docker compose cp -a "$temp_restore_dir/." management:/var/lib/netbird/; then
            echo "Data restored successfully."
            rm -f "$temp_data_backup"
            rm -rf "$temp_restore_dir"
            
            # Start management service
            echo "Starting management service..."
            docker compose start management
            return 0
        else
            echo "Failed to copy restored data to container."
            rm -f "$temp_data_backup"
            rm -rf "$temp_restore_dir"
            docker compose start management
            return 1
        fi
    else
        echo "Failed to extract data backup."
        rm -f "$temp_data_backup"
        rm -rf "$temp_restore_dir"
        docker compose start management
        return 1
    fi
}

# Function to perform full Netbird restore
restore_netbird_full() {
    echo "Starting full Netbird restore..."
    echo "==============================="
    
    local success=true
    
    # Restore configuration
    echo "Step 1: Restoring configuration files..."
    if ! restore_netbird_config; then
        echo "Configuration restore failed."
        success=false
    fi
    
    # Restore data
    echo "Step 2: Restoring data..."
    if ! restore_netbird_data; then
        echo "Data restore failed."
        success=false
    fi
    
    if $success; then
        echo "Full Netbird restore completed successfully."
        echo "You may need to restart Netbird services."
        return 0
    else
        echo "Full Netbird restore completed with errors."
        return 1
    fi
}
