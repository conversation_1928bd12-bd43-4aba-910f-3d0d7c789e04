#!/bin/bash

# Function to backup Netbird configuration files
backup_netbird_config() {
    local netbird_path=$(detect_netbird_installation)
    
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi
    
    local backup_dir="/opt/netbird_backups"
    local config_backup_file="netbird_config_backup.tar.gz"
    local config_backup_path="$backup_dir/$config_backup_file"
    local rclone_dest="$RCLONE_REMOTE:/Backups/Netbird"
    
    echo "Backing up Netbird configuration files..."
    
    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Backup cannot proceed."
        return 1
    fi
    
    # Create backup directory
    mkdir -p "$backup_dir"
    
    # Check compression tools
    if ! check_compression_tools; then
        echo "Required compression tools are not available."
        return 1
    fi
    
    # Create list of config files to backup
    local config_files=()
    cd "$netbird_path"
    
    # Common configuration files
    [ -f "docker-compose.yml" ] && config_files+=("docker-compose.yml")
    [ -f "management.json" ] && config_files+=("management.json")
    [ -f "turnserver.conf" ] && config_files+=("turnserver.conf")
    [ -f "setup.env" ] && config_files+=("setup.env")
    [ -f "Caddyfile" ] && config_files+=("Caddyfile")
    [ -f "zitadel.env" ] && config_files+=("zitadel.env")
    [ -f "dashboard.env" ] && config_files+=("dashboard.env")
    [ -f "relay.env" ] && config_files+=("relay.env")
    [ -f "zdb.env" ] && config_files+=("zdb.env")
    
    if [ ${#config_files[@]} -eq 0 ]; then
        echo "No configuration files found to backup."
        return 1
    fi
    
    # Create tar archive of config files
    echo "Creating configuration backup archive..."
    if tar -czf "$config_backup_path" "${config_files[@]}" 2>/dev/null; then
        echo "Configuration backup created successfully."
        
        # Upload to cloud storage
        echo "Uploading configuration backup to cloud storage..."
        if rclone copy "$config_backup_path" "$rclone_dest" --progress; then
            echo "Configuration backup uploaded successfully."
            rm -f "$config_backup_path"
            return 0
        else
            echo "Failed to upload configuration backup."
            rm -f "$config_backup_path"
            return 1
        fi
    else
        echo "Failed to create configuration backup."
        return 1
    fi
}

# Function to backup Docker volumes
backup_netbird_volumes() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    local backup_dir="/opt/netbird_backups"
    local volumes_backup_file="netbird_volumes_backup.tar.gz"
    local volumes_backup_path="$backup_dir/$volumes_backup_file"
    local rclone_dest="$RCLONE_REMOTE:/Backups/Netbird"

    echo "Backing up Netbird Docker volumes..."

    # Create backup directory
    mkdir -p "$backup_dir"

    cd "$netbird_path"

    # Get list of volumes used by Netbird - improved detection
    local volumes=""
    local temp_volumes_dir="$backup_dir/volumes_temp"
    mkdir -p "$temp_volumes_dir"

    # Stop all services for consistent backup
    echo "Stopping all Netbird services for consistent volume backup..."
    docker compose stop

    # Get volumes from docker compose config
    volumes=$(docker compose config --volumes 2>/dev/null | tr '\n' ' ')

    # Also get volumes from running containers (with netbird prefix)
    local netbird_volumes=$(docker volume ls --format "{{.Name}}" | grep -E "netbird|caddy|postgres|zitadel" | tr '\n' ' ')
    volumes="$volumes $netbird_volumes"

    # Remove duplicates and empty entries
    volumes=$(echo "$volumes" | tr ' ' '\n' | sort -u | grep -v '^$' | tr '\n' ' ')

    echo "Found volumes to backup: $volumes"

    # Backup each volume
    echo "Backing up Docker volumes..."
    local volumes_backed_up=0

    for volume in $volumes; do
        volume=$(echo "$volume" | tr -d ' ')  # Remove any whitespace
        if [ -n "$volume" ] && docker volume inspect "$volume" >/dev/null 2>&1; then
            echo "Backing up volume: $volume"
            local volume_backup_dir="$temp_volumes_dir/$volume"
            mkdir -p "$volume_backup_dir"

            # Create a temporary container to access the volume
            if docker run --rm -v "$volume:/volume" -v "$volume_backup_dir:/backup" alpine:latest \
                sh -c "cd /volume && tar -czf /backup/volume_data.tar.gz . 2>/dev/null" 2>/dev/null; then
                echo "Successfully backed up volume: $volume"
                ((volumes_backed_up++))
            else
                echo "Warning: Failed to backup volume $volume"
            fi
        else
            echo "Volume $volume not found or inaccessible"
        fi
    done

    echo "Total volumes backed up: $volumes_backed_up"

    # Create archive of all volume backups
    if [ "$(ls -A "$temp_volumes_dir" 2>/dev/null)" ]; then
        echo "Creating volumes backup archive..."
        cd "$backup_dir"
        if tar -czf "$volumes_backup_file" -C "$temp_volumes_dir" .; then
            echo "Volumes backup created successfully."
            rm -rf "$temp_volumes_dir"

            # Upload to cloud storage
            echo "Uploading volumes backup to cloud storage..."
            if rclone copy "$volumes_backup_path" "$rclone_dest" --progress; then
                echo "Volumes backup uploaded successfully."
                rm -f "$volumes_backup_path"

                # Restart services
                echo "Restarting Netbird services..."
                cd "$netbird_path"
                docker compose up -d
                return 0
            else
                echo "Failed to upload volumes backup."
                rm -f "$volumes_backup_path"
                cd "$netbird_path"
                docker compose up -d
                return 1
            fi
        else
            echo "Failed to create volumes backup archive."
            rm -rf "$temp_volumes_dir"
            cd "$netbird_path"
            docker compose up -d
            return 1
        fi
    else
        echo "No volumes found to backup."
        rm -rf "$temp_volumes_dir"
        cd "$netbird_path"
        docker compose up -d
        return 0
    fi
}

# Function to backup Netbird data (databases and persistent data)
backup_netbird_data() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    local backup_dir="/opt/netbird_backups"
    local data_backup_file="netbird_data_backup.tar.gz"
    local data_backup_path="$backup_dir/$data_backup_file"
    local rclone_dest="$RCLONE_REMOTE:/Backups/Netbird"

    echo "Backing up Netbird application data..."

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Backup cannot proceed."
        return 1
    fi

    # Create backup directory
    mkdir -p "$backup_dir"

    # Check compression tools
    if ! check_compression_tools; then
        echo "Required compression tools are not available."
        return 1
    fi

    cd "$netbird_path"

    # Create comprehensive data backup
    local temp_data_dir="$backup_dir/netbird_data_temp"
    mkdir -p "$temp_data_dir"

    # Stop services for consistent backup
    echo "Stopping services for consistent data backup..."
    docker compose stop

    # Backup management data
    echo "Backing up management service data..."
    if docker compose cp -a management:/var/lib/netbird/ "$temp_data_dir/management_data/" 2>/dev/null; then
        echo "Management data backed up successfully."
    else
        echo "Warning: Could not backup management data (container may not exist yet)."
    fi

    # Backup Zitadel database - improved detection and backup
    echo "Backing up Zitadel database..."
    local db_container=""

    # Find database container
    if docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "zdb"; then
        db_container="zdb"
    elif docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "postgres"; then
        db_container="postgres"
    fi

    if [ -n "$db_container" ]; then
        mkdir -p "$temp_data_dir/zitadel_db"
        echo "Found database container: $db_container"

        # Start only the database container for backup
        echo "Starting database container for backup..."
        docker compose up -d "$db_container"

        # Wait for database to be ready
        echo "Waiting for database to be ready..."
        sleep 15

        # Try multiple backup methods
        local backup_success=false

        # Method 1: pg_dumpall
        echo "Attempting database backup with pg_dumpall..."
        if docker compose exec -T "$db_container" pg_dumpall -U postgres > "$temp_data_dir/zitadel_db/zitadel_backup.sql" 2>/dev/null; then
            echo "Zitadel database backed up successfully with pg_dumpall."
            backup_success=true
        else
            echo "pg_dumpall failed, trying alternative method..."

            # Method 2: pg_dump for specific databases
            echo "Attempting backup of individual databases..."
            if docker compose exec -T "$db_container" psql -U postgres -c "\l" 2>/dev/null | grep -q "zitadel"; then
                if docker compose exec -T "$db_container" pg_dump -U postgres zitadel > "$temp_data_dir/zitadel_db/zitadel_db_backup.sql" 2>/dev/null; then
                    echo "Zitadel database backed up successfully with pg_dump."
                    backup_success=true
                fi
            fi
        fi

        if ! $backup_success; then
            echo "Warning: Could not backup Zitadel database using SQL dump methods."
            echo "Attempting to backup database data directory..."

            # Method 3: Backup data directory
            if docker compose exec -T "$db_container" tar -czf /tmp/postgres_data.tar.gz /var/lib/postgresql/data 2>/dev/null; then
                docker compose cp "$db_container:/tmp/postgres_data.tar.gz" "$temp_data_dir/zitadel_db/" 2>/dev/null && {
                    echo "Database data directory backed up successfully."
                    backup_success=true
                }
            fi
        fi

        if ! $backup_success; then
            echo "Warning: All database backup methods failed."
        fi

        docker compose stop "$db_container"
    else
        echo "Warning: No database container found for backup."
    fi

    # Backup machine keys and certificates
    echo "Backing up certificates and keys..."
    if [ -d "machinekey" ]; then
        cp -r machinekey "$temp_data_dir/" 2>/dev/null || echo "Warning: Could not backup machine keys."
    fi

    # Backup any additional data directories
    for dir in "certs" "ssl" "data" "logs"; do
        if [ -d "$dir" ]; then
            cp -r "$dir" "$temp_data_dir/" 2>/dev/null
        fi
    done

    # Create tar archive of all data
    echo "Creating comprehensive data backup archive..."
    cd "$backup_dir"
    if tar -czf "$data_backup_file" -C "$temp_data_dir" .; then
        echo "Data backup created successfully."
        rm -rf "$temp_data_dir"

        # Upload to cloud storage
        echo "Uploading data backup to cloud storage..."
        if rclone copy "$data_backup_path" "$rclone_dest" --progress; then
            echo "Data backup uploaded successfully."
            rm -f "$data_backup_path"

            # Restart all services
            echo "Restarting Netbird services..."
            cd "$netbird_path"
            docker compose up -d
            return 0
        else
            echo "Failed to upload data backup."
            rm -f "$data_backup_path"
            cd "$netbird_path"
            docker compose up -d
            return 1
        fi
    else
        echo "Failed to create data backup archive."
        rm -rf "$temp_data_dir"
        cd "$netbird_path"
        docker compose up -d
        return 1
    fi
}

# Function to perform full Netbird backup (config + data + volumes)
backup_netbird_full() {
    echo "Starting COMPLETE Netbird backup..."
    echo "==================================="
    echo "This will backup:"
    echo "- Configuration files (docker-compose.yml, etc.)"
    echo "- Application data (management database, etc.)"
    echo "- Docker volumes (Zitadel DB, Caddy data, certificates)"
    echo "- Machine keys and certificates"
    echo ""

    local netbird_path=$(detect_netbird_installation)
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    local success=true
    local backup_timestamp=$(date +"%Y%m%d_%H%M%S")

    echo "Backup timestamp: $backup_timestamp"
    echo ""

    # Backup configuration
    echo "Step 1/3: Backing up configuration files..."
    if ! backup_netbird_config; then
        echo "Configuration backup failed."
        success=false
    fi
    echo ""

    # Backup application data
    echo "Step 2/3: Backing up application data and databases..."
    if ! backup_netbird_data; then
        echo "Data backup failed."
        success=false
    fi
    echo ""

    # Backup Docker volumes
    echo "Step 3/3: Backing up Docker volumes..."
    if ! backup_netbird_volumes; then
        echo "Volumes backup failed."
        success=false
    fi
    echo ""

    if $success; then
        echo "========================================="
        echo "COMPLETE Netbird backup finished successfully!"
        echo "========================================="
        echo "Backup includes:"
        echo "✓ Configuration files"
        echo "✓ Management database"
        echo "✓ Zitadel identity provider database"
        echo "✓ Docker volumes (Caddy, certificates, etc.)"
        echo "✓ Machine keys and certificates"
        echo ""
        echo "This backup can be used to restore a fully working"
        echo "Netbird installation on any compatible system."
        return 0
    else
        echo "========================================="
        echo "Netbird backup completed with ERRORS!"
        echo "========================================="
        echo "Some components may not have been backed up properly."
        echo "Check the output above for details."
        return 1
    fi
}

# Function to restore Netbird configuration
restore_netbird_config() {
    local backup_dir="/opt/netbird_backups"
    local config_backup_file="netbird_config_backup.tar.gz"
    local temp_config_backup="$backup_dir/$config_backup_file"
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"
    
    echo "Restoring Netbird configuration..."
    
    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi
    
    # Create backup directory
    mkdir -p "$backup_dir"
    
    # Download configuration backup
    echo "Downloading configuration backup from cloud storage..."
    rm -f "$temp_config_backup"
    
    if ! rclone copy "$rclone_source/$config_backup_file" "$backup_dir" --progress; then
        echo "Failed to download configuration backup."
        return 1
    fi
    
    if [ ! -f "$temp_config_backup" ]; then
        echo "Configuration backup file not found after download."
        return 1
    fi
    
    # Ask for installation directory
    echo "Where would you like to restore the Netbird configuration?"
    read -rp "Enter path (default: /opt/netbird): " restore_path
    restore_path=${restore_path:-/opt/netbird}
    
    # Create restore directory
    mkdir -p "$restore_path"
    
    # Extract configuration files
    echo "Extracting configuration files..."
    cd "$restore_path"
    if tar -xzf "$temp_config_backup"; then
        echo "Configuration restored successfully to $restore_path"
        rm -f "$temp_config_backup"
        return 0
    else
        echo "Failed to extract configuration files."
        rm -f "$temp_config_backup"
        return 1
    fi
}

# Function to restore Docker volumes
restore_netbird_volumes() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Please restore configuration first."
        return 1
    fi

    local backup_dir="/opt/netbird_backups"
    local volumes_backup_file="netbird_volumes_backup.tar.gz"
    local temp_volumes_backup="$backup_dir/$volumes_backup_file"
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    echo "Restoring Netbird Docker volumes..."

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # Create backup directory
    mkdir -p "$backup_dir"

    # Download volumes backup
    echo "Downloading volumes backup from cloud storage..."
    rm -f "$temp_volumes_backup"

    if ! rclone copy "$rclone_source/$volumes_backup_file" "$backup_dir" --progress; then
        echo "Failed to download volumes backup."
        return 1
    fi

    if [ ! -f "$temp_volumes_backup" ]; then
        echo "Volumes backup file not found after download."
        return 1
    fi

    # Stop all services
    echo "Stopping all Netbird services..."
    cd "$netbird_path"
    docker compose down

    # Extract volumes backup
    echo "Extracting volumes backup..."
    local temp_volumes_dir="$backup_dir/volumes_restore_temp"
    mkdir -p "$temp_volumes_dir"

    cd "$temp_volumes_dir"
    if tar -xzf "$temp_volumes_backup"; then
        echo "Restoring Docker volumes..."

        # Restore each volume
        for volume_dir in */; do
            if [ -d "$volume_dir" ]; then
                local volume_name=$(basename "$volume_dir")
                echo "Restoring volume: $volume_name"

                # Create volume if it doesn't exist
                docker volume create "$volume_name" >/dev/null 2>&1

                # Restore volume data
                if [ -f "$volume_dir/volume_data.tar.gz" ]; then
                    docker run --rm -v "$volume_name:/volume" -v "$(pwd)/$volume_dir:/backup" alpine:latest \
                        sh -c "cd /volume && tar -xzf /backup/volume_data.tar.gz" 2>/dev/null || {
                        echo "Warning: Failed to restore volume $volume_name"
                    }
                fi
            fi
        done

        echo "Volumes restored successfully."
        rm -f "$temp_volumes_backup"
        rm -rf "$temp_volumes_dir"

        # Start services
        echo "Starting Netbird services..."
        cd "$netbird_path"
        docker compose up -d
        return 0
    else
        echo "Failed to extract volumes backup."
        rm -f "$temp_volumes_backup"
        rm -rf "$temp_volumes_dir"
        cd "$netbird_path"
        docker compose up -d
        return 1
    fi
}

# Function to restore Netbird data
restore_netbird_data() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Please restore configuration first."
        return 1
    fi

    local backup_dir="/opt/netbird_backups"
    local data_backup_file="netbird_data_backup.tar.gz"
    local temp_data_backup="$backup_dir/$data_backup_file"
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    echo "Restoring Netbird application data..."

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # Create backup directory
    mkdir -p "$backup_dir"

    # Download data backup
    echo "Downloading data backup from cloud storage..."
    rm -f "$temp_data_backup"

    if ! rclone copy "$rclone_source/$data_backup_file" "$backup_dir" --progress; then
        echo "Failed to download data backup."
        return 1
    fi

    if [ ! -f "$temp_data_backup" ]; then
        echo "Data backup file not found after download."
        return 1
    fi

    # Stop all services for consistent restore
    echo "Stopping all services for data restoration..."
    cd "$netbird_path"
    docker compose down

    # Extract and restore data
    echo "Extracting and restoring application data..."
    local temp_restore_dir="$backup_dir/data_restore_temp"
    mkdir -p "$temp_restore_dir"

    cd "$temp_restore_dir"
    if tar -xzf "$temp_data_backup"; then
        # Restore management data
        if [ -d "management_data" ]; then
            echo "Restoring management data..."
            # Start only management container to restore data
            cd "$netbird_path"
            docker compose up -d management
            sleep 10

            # Copy management data
            if docker compose cp -a "$temp_restore_dir/management_data/." management:/var/lib/netbird/; then
                echo "Management data restored successfully."
            else
                echo "Warning: Failed to restore management data."
            fi
            docker compose stop management
        fi

        # Restore Zitadel database
        if [ -f "zitadel_db/zitadel_backup.sql" ]; then
            echo "Restoring Zitadel database..."
            cd "$netbird_path"

            # Start database container
            local db_container=$(docker compose config --services | grep -E "postgres|zdb" | head -1)
            if [ -n "$db_container" ]; then
                docker compose up -d "$db_container"
                sleep 15

                # Restore database
                if docker compose exec -T "$db_container" psql -U postgres < "$temp_restore_dir/zitadel_db/zitadel_backup.sql" >/dev/null 2>&1; then
                    echo "Zitadel database restored successfully."
                else
                    echo "Warning: Failed to restore Zitadel database."
                fi
                docker compose stop "$db_container"
            fi
        fi

        # Restore machine keys and certificates
        if [ -d "machinekey" ]; then
            echo "Restoring machine keys and certificates..."
            cd "$netbird_path"
            cp -r "$temp_restore_dir/machinekey" . 2>/dev/null || echo "Warning: Failed to restore machine keys."
        fi

        # Restore additional data directories
        cd "$netbird_path"
        for dir in "certs" "ssl" "data" "logs"; do
            if [ -d "$temp_restore_dir/$dir" ]; then
                cp -r "$temp_restore_dir/$dir" . 2>/dev/null
            fi
        done

        echo "Application data restored successfully."
        rm -f "$temp_data_backup"
        rm -rf "$temp_restore_dir"

        # Start all services
        echo "Starting all Netbird services..."
        docker compose up -d
        return 0
    else
        echo "Failed to extract data backup."
        rm -f "$temp_data_backup"
        rm -rf "$temp_restore_dir"
        cd "$netbird_path"
        docker compose up -d
        return 1
    fi
}

# Function to perform COMPLETE Netbird restore
restore_netbird_full() {
    echo "Starting COMPLETE Netbird restore..."
    echo "===================================="
    echo "This will restore:"
    echo "- Configuration files"
    echo "- Application data and databases"
    echo "- Docker volumes"
    echo "- Machine keys and certificates"
    echo ""
    echo "WARNING: This will overwrite any existing Netbird installation!"
    read -rp "Are you sure you want to continue? (y/n): " confirm_restore
    if [[ ! "$confirm_restore" =~ ^[Yy]$ ]]; then
        echo "Restore cancelled."
        return 1
    fi

    local success=true

    echo ""
    echo "Starting complete restoration process..."
    echo ""

    # Restore configuration first
    echo "Step 1/4: Restoring configuration files..."
    if ! restore_netbird_config; then
        echo "Configuration restore failed."
        success=false
    fi
    echo ""

    # Restore Docker volumes
    echo "Step 2/4: Restoring Docker volumes..."
    if ! restore_netbird_volumes; then
        echo "Volumes restore failed."
        success=false
    fi
    echo ""

    # Restore application data
    echo "Step 3/4: Restoring application data..."
    if ! restore_netbird_data; then
        echo "Data restore failed."
        success=false
    fi
    echo ""

    # Final service restart and verification
    echo "Step 4/4: Final service restart and verification..."
    local netbird_path=$(detect_netbird_installation)
    if [ -n "$netbird_path" ]; then
        cd "$netbird_path"
        echo "Restarting all services with restored data..."
        docker compose down
        sleep 5
        docker compose up -d

        # Wait for services to start
        echo "Waiting for services to start..."
        sleep 30

        # Check if services are running
        if is_netbird_running; then
            echo "Services started successfully."
        else
            echo "Warning: Some services may not have started properly."
            success=false
        fi
    fi
    echo ""

    if $success; then
        echo "================================================="
        echo "COMPLETE Netbird restore finished successfully!"
        echo "================================================="
        echo "Restored components:"
        echo "✓ Configuration files"
        echo "✓ Management database"
        echo "✓ Zitadel identity provider database"
        echo "✓ Docker volumes (Caddy, certificates, etc.)"
        echo "✓ Machine keys and certificates"
        echo ""
        echo "Your Netbird installation should now be fully"
        echo "functional and identical to the backed up state."
        echo ""

        # Show access information
        if [ -n "$netbird_path" ] && [ -f "$netbird_path/.env" ]; then
            echo "Login credentials:"
            cat "$netbird_path/.env"
        fi

        # Show domain info
        if [ -n "$netbird_path" ] && [ -f "$netbird_path/zitadel.env" ]; then
            local domain=$(grep "ZITADEL_EXTERNALDOMAIN" "$netbird_path/zitadel.env" 2>/dev/null | cut -d'=' -f2)
            if [ -n "$domain" ]; then
                echo ""
                echo "Access URL: https://$domain"
            fi
        fi

        return 0
    else
        echo "================================================="
        echo "Netbird restore completed with ERRORS!"
        echo "================================================="
        echo "Some components may not have been restored properly."
        echo "Check the output above for details."
        echo "You may need to:"
        echo "1. Check service logs: docker compose logs"
        echo "2. Restart services manually: docker compose restart"
        echo "3. Verify network connectivity and DNS settings"
        return 1
    fi
}
