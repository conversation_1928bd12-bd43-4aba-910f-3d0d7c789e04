#!/bin/bash

# ByteStash Functions
# Self-hosted code snippet storage solution with backup/restore functionality

# Global variables
BYTESTASH_CONTAINER_NAME="bytestash"
BYTESTASH_IMAGE="ghcr.io/jordan-dalby/bytestash:latest"
BYTESTASH_PORT="5000"
BYTESTASH_DATA_DIR="/opt/bytestash"
BYTESTASH_BACKUP_REMOTE="${RCLONE_REMOTE}:Backups/ByteSTash"
BYTESTASH_BACKUP_FILE="bytestash_backup_$(date +%Y%m%d_%H%M%S).tar.gz"

# Function to install ByteStash
install_bytestash() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║        Installing ByteStash          ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}Docker is not installed. Installing Docker first...${NC}"
        install_docker
    fi
    
    # Check if container already exists
    if docker ps -a --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${YELLOW}ByteStash container already exists.${NC}"
        return 0
    fi
    
    # Create data directory
    echo -e "${YELLOW}Creating data directory...${NC}"
    sudo mkdir -p "${BYTESTASH_DATA_DIR}"
    sudo chmod 755 "${BYTESTASH_DATA_DIR}"
    
    # Pull the latest image
    echo -e "${YELLOW}Pulling ByteStash image...${NC}"
    docker pull "${BYTESTASH_IMAGE}"
    
    # Get JWT secret
    echo -e "${YELLOW}Enter JWT secret (or press Enter for auto-generated):${NC}"
    read -r jwt_secret
    if [[ -z "$jwt_secret" ]]; then
        jwt_secret=$(openssl rand -hex 32)
        echo -e "${GREEN}Generated JWT secret: $jwt_secret${NC}"
    fi
    
    # Create and start container
    echo -e "${YELLOW}Creating ByteStash container...${NC}"
    docker run -d \
        --name "${BYTESTASH_CONTAINER_NAME}" \
        --restart unless-stopped \
        -p "${BYTESTASH_PORT}:5000" \
        -v "${BYTESTASH_DATA_DIR}:/data/snippets" \
        -e BASE_PATH="" \
        -e JWT_SECRET="$jwt_secret" \
        -e TOKEN_EXPIRY="24h" \
        -e ALLOW_NEW_ACCOUNTS="true" \
        -e DEBUG="false" \
        -e DISABLE_ACCOUNTS="false" \
        -e DISABLE_INTERNAL_ACCOUNTS="false" \
        "${BYTESTASH_IMAGE}"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ ByteStash installed successfully!${NC}"
        echo -e "${GREEN}✓ Access ByteStash at: http://${server_ip}:${BYTESTASH_PORT}${NC}"
        echo -e "${GREEN}✓ API Documentation: http://${server_ip}:${BYTESTASH_PORT}/api-docs${NC}"
    else
        echo -e "${RED}✗ Failed to install ByteStash${NC}"
        return 1
    fi
}

# Function to start ByteStash
start_bytestash() {
    echo -e "${YELLOW}Starting ByteStash container...${NC}"
    
    if docker ps --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${GREEN}ByteStash is already running.${NC}"
        return 0
    fi
    
    if docker start "${BYTESTASH_CONTAINER_NAME}" &> /dev/null; then
        echo -e "${GREEN}✓ ByteStash started successfully!${NC}"
        echo -e "${GREEN}✓ Access at: http://${server_ip}:${BYTESTASH_PORT}${NC}"
    else
        echo -e "${RED}✗ Failed to start ByteStash${NC}"
        return 1
    fi
}

# Function to stop ByteStash
stop_bytestash() {
    echo -e "${YELLOW}Stopping ByteStash container...${NC}"
    
    if docker stop "${BYTESTASH_CONTAINER_NAME}" &> /dev/null; then
        echo -e "${GREEN}✓ ByteStash stopped successfully!${NC}"
    else
        echo -e "${RED}✗ Failed to stop ByteStash or container not running${NC}"
        return 1
    fi
}

# Function to restart ByteStash
restart_bytestash() {
    echo -e "${YELLOW}Restarting ByteStash container...${NC}"
    
    if docker restart "${BYTESTASH_CONTAINER_NAME}" &> /dev/null; then
        echo -e "${GREEN}✓ ByteStash restarted successfully!${NC}"
        echo -e "${GREEN}✓ Access at: http://${server_ip}:${BYTESTASH_PORT}${NC}"
    else
        echo -e "${RED}✗ Failed to restart ByteStash${NC}"
        return 1
    fi
}

# Function to show ByteStash status
show_bytestash_status() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║         ByteStash Status             ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "^${BYTESTASH_CONTAINER_NAME}"; then
        echo -e "${GREEN}✓ ByteStash is running${NC}"
        echo -e "${GREEN}✓ Web Interface: http://${server_ip}:${BYTESTASH_PORT}${NC}"
        echo -e "${GREEN}✓ API Docs: http://${server_ip}:${BYTESTASH_PORT}/api-docs${NC}"
    elif docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep "^${BYTESTASH_CONTAINER_NAME}"; then
        echo -e "${YELLOW}⚠ ByteStash container exists but is not running${NC}"
    else
        echo -e "${RED}✗ ByteStash is not installed${NC}"
    fi
}

# Function to backup ByteStash data
backup_bytestash() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║       Backing up ByteStash           ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    # Check if rclone is installed
    if ! command -v rclone &> /dev/null; then
        echo -e "${RED}Rclone is not installed. Installing...${NC}"
        install_rclone
    fi
    
    # Check if data directory exists
    if [[ ! -d "${BYTESTASH_DATA_DIR}" ]]; then
        echo -e "${RED}✗ ByteStash data directory not found: ${BYTESTASH_DATA_DIR}${NC}"
        return 1
    fi
    
    # Stop ByteStash container before backup for data consistency
    local container_was_running=false
    if docker ps --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${YELLOW}Stopping ByteStash container for backup...${NC}"
        docker stop "${BYTESTASH_CONTAINER_NAME}"
        container_was_running=true
    fi
    
    # Remove existing backup from remote (single backup policy)
    echo -e "${YELLOW}Checking for existing backups...${NC}"
    local existing_backups
    existing_backups=$(rclone lsf "${BYTESTASH_BACKUP_REMOTE}/" --include "*.tar.gz" 2>/dev/null)
    if [[ -n "$existing_backups" ]]; then
        echo -e "${YELLOW}Removing existing backup(s)...${NC}"
        while IFS= read -r backup_file; do
            if [[ -n "$backup_file" ]]; then
                rclone delete "${BYTESTASH_BACKUP_REMOTE}/${backup_file}"
                echo -e "${GREEN}✓ Removed existing backup: $backup_file${NC}"
            fi
        done <<< "$existing_backups"
    fi
    
    # Create temporary backup directory
    local temp_backup_dir="/tmp/bytestash_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$temp_backup_dir"
    
    echo -e "${YELLOW}Creating compressed backup...${NC}"
    
    # Create compressed backup
    if tar -czf "${temp_backup_dir}/${BYTESTASH_BACKUP_FILE}" -C "$(dirname ${BYTESTASH_DATA_DIR})" "$(basename ${BYTESTASH_DATA_DIR})"; then
        echo -e "${GREEN}✓ Backup archive created successfully${NC}"
    else
        echo -e "${RED}✗ Failed to create backup archive${NC}"
        rm -rf "$temp_backup_dir"
        return 1
    fi
    
    # Upload to remote
    echo -e "${YELLOW}Uploading backup to remote storage...${NC}"
    if rclone_sync "${temp_backup_dir}/${BYTESTASH_BACKUP_FILE}" "${BYTESTASH_BACKUP_REMOTE}/"; then
        echo -e "${GREEN}✓ Backup uploaded successfully to ${BYTESTASH_BACKUP_REMOTE}${NC}"
    else
        echo -e "${RED}✗ Failed to upload backup${NC}"
        rm -rf "$temp_backup_dir"
        return 1
    fi
    
    # Clean up local backup
    echo -e "${YELLOW}Cleaning up local backup files...${NC}"
    rm -rf "$temp_backup_dir"
    echo -e "${GREEN}✓ Local cleanup completed${NC}"
    
    # Restart ByteStash container if it was running before backup
    if [[ "$container_was_running" == "true" ]]; then
        echo -e "${YELLOW}Restarting ByteStash container...${NC}"
        start_bytestash
        echo -e "${GREEN}✓ ByteStash container restarted${NC}"
    fi
    
    echo -e "${GREEN}✓ ByteStash backup completed successfully!${NC}"
}

# Function to restore ByteStash data
restore_bytestash() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║       Restoring ByteStash            ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    # Check if rclone is installed
    if ! command -v rclone &> /dev/null; then
        echo -e "${RED}Rclone is not installed. Installing...${NC}"
        install_rclone
    fi
    
    # List available backups
    echo -e "${YELLOW}Checking for available backups...${NC}"
    local backup_list
    backup_list=$(rclone lsf "${BYTESTASH_BACKUP_REMOTE}/" --include "*.tar.gz" 2>/dev/null)
    
    if [[ -z "$backup_list" ]]; then
        echo -e "${RED}✗ No backup files found in ${BYTESTASH_BACKUP_REMOTE}${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Available backups:${NC}"
    echo "$backup_list" | nl -w2 -s'. '
    
    # Get user selection
    echo -e "${YELLOW}Enter the number of the backup to restore:${NC}"
    read -r backup_choice
    
    local selected_backup
    selected_backup=$(echo "$backup_list" | sed -n "${backup_choice}p")
    
    if [[ -z "$selected_backup" ]]; then
        echo -e "${RED}✗ Invalid selection${NC}"
        return 1
    fi
    
    # Confirm restoration
    echo -e "${YELLOW}⚠ This will replace all current ByteStash data!${NC}"
    echo -e "${YELLOW}Selected backup: $selected_backup${NC}"
    echo -e "${YELLOW}Continue? [y/N]:${NC}"
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Restoration cancelled${NC}"
        return 1
    fi
    
    # Stop ByteStash if running
    if docker ps --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${YELLOW}Stopping ByteStash container...${NC}"
        docker stop "${BYTESTASH_CONTAINER_NAME}"
    fi
    
    # Create temporary restore directory
    local temp_restore_dir="/tmp/bytestash_restore_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$temp_restore_dir"
    
    # Download backup
    echo -e "${YELLOW}Downloading backup file...${NC}"
    if rclone_sync "${BYTESTASH_BACKUP_REMOTE}/${selected_backup}" "${temp_restore_dir}/"; then
        echo -e "${GREEN}✓ Backup downloaded successfully${NC}"
    else
        echo -e "${RED}✗ Failed to download backup${NC}"
        rm -rf "$temp_restore_dir"
        return 1
    fi
    
    # Backup current data (if exists)
    if [[ -d "${BYTESTASH_DATA_DIR}" ]]; then
        echo -e "${YELLOW}Backing up current data...${NC}"
        sudo mv "${BYTESTASH_DATA_DIR}" "${BYTESTASH_DATA_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Extract backup
    echo -e "${YELLOW}Extracting backup...${NC}"
    if sudo tar -xzf "${temp_restore_dir}/${selected_backup}" -C "$(dirname ${BYTESTASH_DATA_DIR})"; then
        echo -e "${GREEN}✓ Backup extracted successfully${NC}"
    else
        echo -e "${RED}✗ Failed to extract backup${NC}"
        rm -rf "$temp_restore_dir"
        return 1
    fi
    
    # Set proper permissions
    sudo chown -R 1000:1000 "${BYTESTASH_DATA_DIR}"
    sudo chmod -R 755 "${BYTESTASH_DATA_DIR}"
    
    # Clean up
    rm -rf "$temp_restore_dir"
    
    echo -e "${GREEN}✓ ByteStash restoration completed successfully!${NC}"
    
    # Start ByteStash container after restoration
    echo -e "${YELLOW}Starting ByteStash container...${NC}"
    start_bytestash
    echo -e "${GREEN}✓ ByteStash container started${NC}"
    echo -e "${YELLOW}ByteStash is now accessible at: http://${server_ip}:3000${NC}"
}