#!/bin/bash

headscale_menu() {
    while true; do
        options=(
            "01. Create new Headscale + DERP setup"
            "02. Delete existing setup"
            "03. Check servers status"
            "04. Backup Headscale DB"
            "05. Restore Headscale DB"
            "06. Node Management"
            "07. User Management"
            "08. Route Management"
            "09. PreAuth Key Management"
            "10. Tailscale Client Management"
            "11. Back to main menu"
            "12. Exit"
        )

        create_menu "Headscale & DERP Server Management" "${options[@]}"
        
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1|01) setup_environment ;;
            2|02) delete_headscale_server ;;
            3|03) check_servers_status ;;
            4|04) backup_headscale_db ;;
            5|05) restore_headscale_db ;;
            6|06) node_management_menu ;;
            7|07) user_management_menu ;;
            8|08) route_management_menu ;;
            9|09) preauthkey_management_menu ;;
            10) tailscale_client_menu ;;
            11) break ;;
            12) echo -e "${GREEN}Exiting...${NC}"; exit 0 ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo  # Add a blank line for better readability
    done
}

setup_environment() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║    Setting Up Environment     ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    # Check if any existing setup exists
    if check_derp_status || check_headscale_status; then
        echo -e "${YELLOW}Found existing DERP or Headscale setup${NC}"
        read -rp "$(echo -e ${YELLOW}"Do you want to delete existing setup and create new? (y/n): "${NC})" confirm
        if [[ $confirm == [yY] ]]; then
            delete_headscale_server
        else
            return 1
        fi
    fi

    # Get SSL certificate
    generate_ssl_certificate

    # Select domain from available certificates
    if ! select_domain; then
        return 1
    fi

    local cert_dir_name=$(basename "$selected_domain")
    local base_domain=$(extract_base_domain "$cert_dir_name")

    # Get subdomains (only the prefix part)
    echo -e "${CYAN}╔════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Configure Subdomains          ║${NC}"
    echo -e "${CYAN}╚════════════════════════════════════╝${NC}"
    
    read -rp "$(echo -e ${YELLOW}"Enter DERP subdomain prefix (e.g., derp): "${NC})" derp_name
    read -rp "$(echo -e ${YELLOW}"Enter Headscale subdomain prefix (e.g., headscale): "${NC})" headscale_name

    # Remove any domain part if user entered it
    derp_name=$(echo "$derp_name" | sed "s/\.${base_domain}//")
    headscale_name=$(echo "$headscale_name" | sed "s/\.${base_domain}//")

    # Construct full domain names
    DERP_SUBDOMAIN="${derp_name}"
    HEADSCALE_SUBDOMAIN="${headscale_name}"
    SELECTED_DOMAIN="$base_domain"
    CERT_DIR="$cert_dir_name"

    # Validate subdomains are different
    if [ "$derp_name" == "$headscale_name" ]; then
        echo "DERP and Headscale subdomains must be different"
        return 1
    fi

    # Setup DERP first with pre-populated values
    setup_derp_server "$DERP_SUBDOMAIN" || {
        echo "DERP server setup failed"
        return 1
    }

    # Wait for DERP to be fully running
    echo "Waiting for DERP server to start..."
    sleep 10

    # Setup Headscale with pre-populated values
    setup_headscale_server || {
        echo "Headscale setup failed"
        delete_headscale_server
        return 1
    }

    echo "Complete environment setup successful!"
}

delete_headscale_server() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║    Deleting Environment       ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    # Delete Headscale containers
    if docker ps -a --format '{{.Image}}' | grep -q "fazee6/headscale"; then
        docker ps -a --format '{{.Names}}' | grep -E ".*headscale.*" | xargs -r docker rm -f
        rm -rf /opt/headscale
        echo -e "${GREEN}Headscale server deleted${NC}"
    fi

    # Delete DERP containers
    if docker ps -a --format '{{.Image}}' | grep -q "fazee6/derp-server"; then
        docker ps -a --format '{{.Names}}' | grep -E ".*derp.*|dreper" | xargs -r docker rm -f
        echo -e "${GREEN}DERP server deleted${NC}"
    fi

    echo -e "${GREEN}Environment cleanup complete${NC}"
}

check_headscale_status() {
    docker ps --format '{{.Image}}' | grep -q "fazee6/headscale"
    return $?
}

check_derp_status() {
    docker ps --format '{{.Image}}' | grep -q "fazee6/derp-server"
    return $?
}

check_servers_status() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Environment Status       ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"
    
    if check_derp_status; then
        echo -e "${GREEN}DERP Server: Running${NC}"
        docker ps --format "DERP Container: ${GREEN}{{.Names}}${NC}" | grep "derp\|dreper"
    else
        echo -e "${RED}DERP Server: Not running${NC}"
    fi

    if check_headscale_status; then
        echo -e "${GREEN}Headscale Server: Running${NC}"
        docker ps --format "Headscale Container: ${GREEN}{{.Names}}${NC}" | grep "headscale"
    else
        echo -e "${RED}Headscale Server: Not running${NC}"
    fi
}

backup_headscale_db() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║    Backing Up Headscale DB    ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    if [ ! -d "/opt/headscale/headscale-db" ]; then
        echo -e "${RED}Error: Headscale DB directory not found!${NC}"
        return 1
    fi

    install_rclone

    echo -e "${GREEN}Starting Headscale DB backup...${NC}"
    echo "Executing rclone command: rclone sync /opt/headscale/headscale-db $RCLONE_REMOTE:/Backups/headscale-db --progress"
    if rclone sync /opt/headscale/headscale-db $RCLONE_REMOTE:/Backups/headscale-db --progress; then
        echo -e "${GREEN}Headscale DB backup completed successfully!${NC}"
    else
        echo -e "${RED}Failed to backup Headscale DB${NC}"
        return 1
    fi
}

restore_headscale_db() {
    echo -e "${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║   Restoring Headscale DB      ║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}"

    install_rclone

    # Create directory if it doesn't exist
    mkdir -p /opt/headscale/headscale-db

    echo -e "${GREEN}Starting Headscale DB restore...${NC}"
    echo "Executing rclone command: rclone sync $RCLONE_REMOTE:/Backups/headscale-db /opt/headscale/headscale-db --progress"
    if rclone sync $RCLONE_REMOTE:/Backups/headscale-db /opt/headscale/headscale-db --progress; then
        echo -e "${GREEN}Headscale DB restore completed successfully!${NC}"
    else
        echo -e "${RED}Failed to restore Headscale DB${NC}"
        return 1
    fi
}

node_management_menu() {
    while true; do
        options=(
            "1. List Nodes"
            "2. Delete Node"
            "3. Rename Node"
            "4. Back"
        )

        create_menu "Node Management" "${options[@]}"
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) list_nodes ;;
            2) delete_node ;;
            3) rename_node ;;
            4) break ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac
    done
}

user_management_menu() {
    while true; do
        options=(
            "1. List Users"
            "2. Create User"
            "3. Delete User"
            "4. Back"
        )

        create_menu "User Management" "${options[@]}"
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) list_users ;;
            2) create_user ;;
            3) delete_user ;;
            4) break ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac
    done
}

route_management_menu() {
    while true; do
        options=(
            "1. List Routes"
            "2. Enable Route"
            "3. Disable Route"
            "4. Delete Route"
            "5. Back"
        )

        create_menu "Route Management" "${options[@]}"
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) list_routes ;;
            2) manage_route "enable" ;;
            3) manage_route "disable" ;;
            4) manage_route "delete" ;;
            5) break ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac
    done
}

preauthkey_management_menu() {
    while true; do
        options=(
            "1. List PreAuth Keys"
            "2. Create PreAuth Key"
            "3. Back"
        )

        create_menu "PreAuth Key Management" "${options[@]}"
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) list_preauthkeys ;;
            2) create_preauthkey ;;
            3) break ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac
    done
}

tailscale_client_menu() {
    while true; do
        options=(
            "1. Install Tailscale Client"
            "2. Join Tailscale Network"
            "3. Force Reauthenticate Tailscale"
            "4. Back"
        )

        create_menu "Tailscale Client Management" "${options[@]}"
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1)
                echo -e "${CYAN}Installing Tailscale client...${NC}"
                if curl -fsSL https://tailscale.com/install.sh | sh; then
                    echo -e "${GREEN}Tailscale client installed successfully!${NC}"
                else
                    echo -e "${RED}Failed to install Tailscale client.${NC}"
                fi
                ;;
            2)
                read -rp "$(echo -e ${YELLOW}"Enter auth key: "${NC})" auth_key
                read -rp "$(echo -e ${YELLOW}"Enter advertise routes (comma-separated): "${NC})" advertise_routes
                read -rp "$(echo -e ${YELLOW}"Enter hostname: "${NC})" hostname
                read -rp "$(echo -e ${YELLOW}"Enter login server URL: "${NC})" login_server
                join_tailscale "$auth_key" "$advertise_routes" "$hostname" "$login_server"
                ;;
            3)
                read -rp "$(echo -e ${YELLOW}"Enter auth key: "${NC})" auth_key
                read -rp "$(echo -e ${YELLOW}"Enter advertise routes (comma-separated): "${NC})" advertise_routes
                read -rp "$(echo -e ${YELLOW}"Enter hostname: "${NC})" hostname
                read -rp "$(echo -e ${YELLOW}"Enter login server URL: "${NC})" login_server
                force_reauth_tailscale "$auth_key" "$advertise_routes" "$hostname" "$login_server"
                ;;
            4) break ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac
    done
}
