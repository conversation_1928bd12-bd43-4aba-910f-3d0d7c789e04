#!/bin/bash

# Modify the uninstall function
uninstall_casaos_app() {
    local app_name="$1"
    echo -e "${YELLOW}Uninstalling $app_name...${NC}"

    # Check if app is actually installed before trying to uninstall
    if ! is_app_installed "$app_name"; then # Assumes is_app_installed is sourced
        echo -e "${YELLOW}$app_name is not installed or already uninstalled${NC}"
        return 0
    fi

    casaos-cli app-management uninstall "$app_name"
    local uninstall_status=$?

    if [ $uninstall_status -eq 0 ]; then
        # Wait for app to be completely uninstalled
        if ! wait_for_app_uninstalled "$app_name"; then # Assumes wait_for_app_uninstalled is sourced
            echo -e "${RED}Failed to verify uninstallation of $app_name${NC}"
            return 1
        fi

        echo -e "${GREEN}Successfully uninstalled $app_name${NC}"
        return 0
    else
        echo -e "${RED}Failed to uninstall $app_name${NC}"
        return 1
    fi
}

# Modify the install_casaos_app function
install_casaos_app() {
    local app_name="$1"
    local yaml_content="$2" # This parameter might need adjustment depending on how YAML is handled now

    if is_app_installed "$app_name"; then # Assumes is_app_installed is sourced
        echo -e "${YELLOW}$app_name is already installed.${NC}"
        return 1
    fi

    # Create temporary YAML file with proper content
    local temp_yaml="/tmp/${app_name}.yaml"
    # Ensure yaml_content is correctly passed or fetched if needed
    if [ -z "$yaml_content" ]; then
         # Attempt to use the standard YAML path if content isn't provided directly
         local yaml_dir="/tmp/casaos_yaml"
         if [ -f "$yaml_dir/${app_name}.yaml" ]; then
             yaml_content=$(cat "$yaml_dir/${app_name}.yaml")
         else
             echo -e "${RED}YAML content for $app_name not provided and not found in $yaml_dir.${NC}"
             return 1
         fi
    fi
    echo "${yaml_content}" > "$temp_yaml"


    echo -e "${YELLOW}Installing $app_name...${NC}"
    casaos-cli app-management install -f "$temp_yaml"
    local install_status=$?

    rm -f "$temp_yaml"

    if [ $install_status -ne 0 ]; then
        echo -e "${RED}Installation failed for $app_name${NC}"
        return 1
    fi

    # Wait for app to appear in CasaOS app list
    if ! wait_for_app_in_list "$app_name"; then # Assumes wait_for_app_in_list is sourced
        echo -e "${RED}Installation command succeeded but app failed to appear in CasaOS list${NC}"
        return 1
    fi

    # Wait for container to be running
    if ! wait_for_container_status "$app_name" "running"; then # Assumes wait_for_container_status is sourced
        echo -e "${RED}Installation command succeeded but container failed to start${NC}"
        return 1
    fi

    echo -e "${GREEN}Successfully installed $app_name${NC}"
    return 0
}

# Function to ensure YAML files exist
ensure_yaml_files() {
    local temp_dir="/tmp/casaos_yaml"
    mkdir -p "$temp_dir"

    # Define the apps whose YAML files we need
    local required_apps=("jellyfin" "nginxproxymanager" "wordpress_with_oracle_db") # Added wordpress_with_oracle_db
    local missing_files=false

    # Check which files are missing
    for app in "${required_apps[@]}"; do
        if [ ! -f "$temp_dir/${app}.yaml" ]; then
            echo -e "${YELLOW}YAML file for $app missing. Will attempt download.${NC}"
            missing_files=true
        fi
    done

    # Download missing files
    if [ "$missing_files" = true ]; then
        echo -e "${YELLOW}Downloading required YAML files...${NC}"
        for app in "${required_apps[@]}"; do
             if [ ! -f "$temp_dir/${app}.yaml" ]; then
                 echo -e "${YELLOW}Downloading ${app}.yaml...${NC}"
                 echo "Executing rclone command: rclone copy \"$RCLONE_REMOTE:/Backups/CasaOS/apps/${app}.yaml\" \"$temp_dir/\" --progress"
                 rclone copy "$RCLONE_REMOTE:/Backups/CasaOS/apps/${app}.yaml" "$temp_dir/" \
                     --progress \
                     --checkers 4 \
                     --transfers 2 \
                     --stats 1s
             fi
        done

        # Verify download
        for app in "${required_apps[@]}"; do
            if [ ! -f "$temp_dir/${app}.yaml" ]; then
                echo -e "${RED}Failed to download ${app}.yaml${NC}"
                return 1
            fi
        done
    fi

    echo -e "${GREEN}Required YAML files are ready${NC}"
    return 0
}

# Function to show available apps menu
casaos_install_apps_menu() {
    if ! check_casaos_installed; then # Assumes check_casaos_installed is sourced
        echo -e "${RED}CasaOS is not installed.${NC}"
        read -rp "$(echo -e ${YELLOW}"Would you like to install CasaOS first? (y/n): "${NC})" choice
        if [[ $choice =~ ^[Yy]$ ]]; then
            install_casaos # Assumes install_casaos is sourced
            if ! check_casaos_installed; then
                return 1
            fi
        else
            return 1
        fi
    fi

    # Ensure YAML files are available
    if ! ensure_yaml_files; then
        return 1
    fi

    local yaml_dir="/tmp/casaos_yaml"

    while true; do
        options=(
            "0. Install All Available Apps"
            "1. Install Jellyfin"
            "2. Install Nginx Proxy Manager"
            "3. Install MCSAGURU WordPress"
            "4. Back to Main Menu" # Renumbered
        )

        create_menu "Available CasaOS Apps" "${options[@]}" # Assumes create_menu is sourced
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            0)
                echo -e "${YELLOW}Installing all available apps...${NC}"

                # Install Jellyfin
                if is_app_installed "jellyfin"; then
                    echo -e "${YELLOW}Jellyfin is already installed, skipping...${NC}"
                else
                    echo -e "${YELLOW}Installing Jellyfin...${NC}"
                    install_casaos_app "jellyfin" # Use the refactored install function
                fi

                # Install Nginx Proxy Manager
                if is_app_installed "nginxproxymanager"; then
                    echo -e "${YELLOW}Nginx Proxy Manager is already installed, skipping...${NC}"
                else
                    echo -e "${YELLOW}Installing Nginx Proxy Manager...${NC}"
                    install_casaos_app "nginxproxymanager" # Use the refactored install function
                fi

                # Install MCSAGURU WordPress
                if is_app_installed "wordpress"; then
                    echo -e "${YELLOW}MCSAGURU WordPress is already installed, skipping...${NC}"
                else
                    echo -e "${YELLOW}Installing MCSAGURU WordPress...${NC}"
                    install_casaos_app "wordpress_with_oracle_db" # Use the refactored install function
                fi
                ;;
            1)
                if is_app_installed "jellyfin"; then
                    echo -e "${YELLOW}Jellyfin is already installed, skipping...${NC}"
                    continue
                fi
                echo -e "${YELLOW}Installing Jellyfin...${NC}"
                install_casaos_app "jellyfin" # Use the refactored install function
                ;;
            2)
                if is_app_installed "nginxproxymanager"; then
                    echo -e "${YELLOW}Nginx Proxy Manager is already installed, skipping...${NC}"
                    continue
                fi
                echo -e "${YELLOW}Installing Nginx Proxy Manager...${NC}"
                install_casaos_app "nginxproxymanager" # Use the refactored install function
                ;;
            3)
                if is_app_installed "wordpress"; then
                    echo -e "${YELLOW}MCSAGURU WordPress is already installed, skipping...${NC}"
                    continue
                fi
                echo -e "${YELLOW}Installing MCSAGURU WordPress...${NC}"
                install_casaos_app "wordpress_with_oracle_db" # Use the refactored install function
                ;;
            4) return 0 ;; # Renumbered Back option
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac
    done
}

# Function to list installed CasaOS apps
list_installed_apps() {
    local apps_output=$(casaos-cli app-management list apps)
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to get list of installed apps${NC}"
        return 1
    fi
    # Skip header line and dashed line, then get only valid app names
    echo "$apps_output" | awk 'NR>2 && $1 != "-----" {print $1}'
}

# Function to handle uninstall selection
casaos_uninstall_menu() {
    if ! check_casaos_installed; then # Assumes check_casaos_installed is sourced
        echo -e "${RED}CasaOS is not installed.${NC}"
        return 1
    fi

    while true; do
        # Get list of installed apps
        local apps=($(list_installed_apps))
        if [ ${#apps[@]} -eq 0 ]; then
            echo -e "${RED}No apps installed in CasaOS.${NC}"
            sleep 2
            return 0
        fi

        options=("0. Uninstall All Apps")
        for i in "${!apps[@]}"; do
            options+=("$((i+1)). ${apps[i]}")
        done
        options+=("$((${#apps[@]}+1)). Back")

        create_menu "Select Apps to Uninstall" "${options[@]}" # Assumes create_menu is sourced
        echo -e "${YELLOW}For multiple selections, enter numbers separated by spaces (e.g., 1 3 4)${NC}"
        read -rp "$(echo -e ${YELLOW}"Enter your choice(s): "${NC})" -a choices

        if [[ "${choices[0]}" == "$((${#apps[@]}+1))" ]]; then
            return 0
        fi

        local selected_apps=()
        local uninstall_all=false

        for choice in "${choices[@]}"; do
            if [[ "$choice" == "0" ]]; then
                uninstall_all=true
                break
            elif [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -le "${#apps[@]}" ]; then
                selected_apps+=("${apps[$((choice-1))]}")
            fi
        done

        if [ "$uninstall_all" = true ]; then
            echo -e "${YELLOW}Are you sure you want to uninstall ALL apps? This cannot be undone! (y/n): ${NC}"
            read -r confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                echo -e "${GREEN}Uninstalling all apps...${NC}"
                for app in "${apps[@]}"; do
                    echo -e "${YELLOW}Uninstalling $app...${NC}"
                    uninstall_casaos_app "$app"
                done
            fi
        else
            if [ ${#selected_apps[@]} -eq 0 ]; then
                echo -e "${RED}No valid selections made.${NC}"
                continue
            fi

            for app in "${selected_apps[@]}"; do
                echo -e "${YELLOW}Are you sure you want to uninstall $app? (y/n): ${NC}"
                read -r confirm
                if [[ $confirm =~ ^[Yy]$ ]]; then
                    uninstall_casaos_app "$app"
                fi
            done
        fi

        echo -e "${GREEN}Uninstall operations completed!${NC}"
        break
    done
}

# Function to uninstall CasaOS
uninstall_casaos() {
    if ! check_casaos_installed; then # Assumes check_casaos_installed is sourced
        echo -e "${RED}CasaOS is not installed.${NC}"
        return 1
    fi

    echo -e "${RED}WARNING: This will completely remove CasaOS and all its data!${NC}"
    echo -e "${YELLOW}Are you absolutely sure you want to uninstall CasaOS? (y/n): ${NC}"
    read -r confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        # Get list of all installed apps
        local apps=($(list_installed_apps))

        if [ ${#apps[@]} -gt 0 ]; then
            echo -e "${YELLOW}Uninstalling all CasaOS apps first...${NC}"
            for app in "${apps[@]}"; do
                echo -e "${YELLOW}Uninstalling $app...${NC}"
                uninstall_casaos_app "$app"
            done
        fi

        echo -e "${YELLOW}Uninstalling CasaOS...${NC}"
        casaos-uninstall
        if ! check_casaos_installed; then
            echo -e "${GREEN}CasaOS has been successfully uninstalled.${NC}"
            return 0
        else
            echo -e "${RED}Failed to uninstall CasaOS. Please check the logs.${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}Uninstall cancelled.${NC}"
        return 0
    fi
}