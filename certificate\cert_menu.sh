#!/bin/bash

certificate_menu() {
    while true; do
        options=(
            "1. Generate SSL Certificate"
            "2. Setup Renewal Script Only"
            "3. Restore Certificate from Remote"
            "4. Backup Certificate to Remote"
            "5. Back to Main Menu"
        )

        create_menu "Certificate Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) generate_ssl_certificate ;;
            2) setup_renewal_script ;;
            3) restore_certificate_from_remote ;;
            4) backup_certificate_to_remote ;;
            5) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo  # Add a blank line for better readability
    done
}
