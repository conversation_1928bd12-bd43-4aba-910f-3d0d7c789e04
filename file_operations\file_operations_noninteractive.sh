#!/bin/bash

# Non-interactive version of compress_items
# Usage: compress_items_noninteractive base_path folder_names output_path [format]
# Example: compress_items_noninteractive /opt/adguardhome "config work" /opt/adguard_backups/backup.tar.gz [tar.gz|zip|rar]
compress_items_noninteractive() {
    local base_path="$1"
    local folder_choice="$2"
    local output_path="$3"
    local format="${4:-tar.gz}"  # Default to tar.gz if not specified
    
    base_path="${base_path%/}"
    
    # Validate parameters
    if [ -z "$base_path" ] || [ -z "$folder_choice" ] || [ -z "$output_path" ]; then
        echo "Missing required parameters for compress_items_noninteractive"
        echo "Usage: compress_items_noninteractive base_path folder_names output_path [format]"
        return 1
    fi

    if [ ! -d "$base_path" ]; then
        echo "Invalid base path: Directory does not exist: $base_path"
        return 1
    fi
    
    # Check if the specified folders exist
    local all_folders_exist=true
    if [ "$folder_choice" != "*" ]; then
        for folder in $folder_choice; do
            if [ ! -d "$base_path/$folder" ] && [ ! -f "$base_path/$folder" ]; then
                echo "Item not found: $base_path/$folder"
                all_folders_exist=false
            fi
        done
    fi
    
    if [ "$all_folders_exist" = "false" ]; then
        return 1
    fi

    # Check and install required tools first - suppress interactive prompts
    local tools=("tar" "gzip" "zip" "unzip" "rar" "unrar")
    local missing=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            if [[ "$tool" == "rar" || "$tool" == "unrar" ]]; then
                if ! command -v "rar" &> /dev/null && ! command -v "unrar" &> /dev/null; then
                    if [[ ! " ${missing[@]} " =~ " rar " ]]; then
                        missing+=("rar")
                    fi
                fi
            else
                missing+=("$tool")
            fi
        fi
    done
    
    if [ ${#missing[@]} -gt 0 ]; then
        for package in "${missing[@]}"; do
            if ! command -v "$package" &> /dev/null; then
                if command -v apt &> /dev/null; then
                    sudo apt-get update -qq && sudo apt-get install -qq -y "$package" >/dev/null 2>&1
                fi
            fi
        done
    fi

    # Extract directory from output_path
    local output_dir=$(dirname "$output_path")
    if [ ! -d "$output_dir" ]; then
        mkdir -p "$output_dir"
        if [ $? -ne 0 ]; then
            echo "Failed to create output directory: $output_dir"
            return 1
        fi
    fi

    # Create archive
    local cmd_failed=0
    local items_to_compress=""
    
    if [ "$folder_choice" = "*" ]; then
        # Compress all items in base_path
        items_to_compress=$(find "$base_path" -mindepth 1 -maxdepth 1 -printf "%f\n")
        if [ -z "$items_to_compress" ]; then
            echo "No items found directly within $base_path to compress."
            return 1
        fi
    else
        # Use the specified folders
        items_to_compress="$folder_choice"
    fi

    echo "Creating archive of: $items_to_compress from $base_path"
    echo "Output will be: $output_path"
    
    case $format in
        "tar.gz"|"tgz"|"1")  # TAR.GZ
            if ! command -v tar &> /dev/null || ! command -v gzip &> /dev/null; then
                echo "Error: 'tar' or 'gzip' command not found. Cannot create tar.gz archive."
                cmd_failed=1
            else
                # Ensure output has the right extension
                if [[ "$output_path" != *.tar.gz ]]; then
                    output_path="${output_path}.tar.gz"
                fi
                
                echo "Creating tar.gz archive: $output_path"
                # Use relative paths for items within the archive
                if [ "$folder_choice" = "*" ]; then
                    (cd "$base_path" && tar -czf "$output_path" $items_to_compress)
                else
                    (cd "$base_path" && tar -czf "$output_path" $items_to_compress)
                fi
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        "zip"|"2")  # ZIP
            if ! command -v zip &> /dev/null; then
                echo "Error: 'zip' command not found. Cannot create zip archive."
                cmd_failed=1
            else
                # Ensure output has the right extension
                if [[ "$output_path" != *.zip ]]; then
                    output_path="${output_path}.zip"
                fi
                
                echo "Creating zip archive: $output_path"
                # zip handles recursion with -r
                if [ "$folder_choice" = "*" ]; then
                    (cd "$base_path" && zip -qr "$output_path" $items_to_compress)
                else
                    (cd "$base_path" && zip -qr "$output_path" $items_to_compress)
                fi
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        "rar"|"3")  # RAR
            if ! command -v rar &> /dev/null; then
                echo "Error: 'rar' command not found. Cannot create rar archive."
                cmd_failed=1
            else
                # Ensure output has the right extension
                if [[ "$output_path" != *.rar ]]; then
                    output_path="${output_path}.rar"
                fi
                
                echo "Creating rar archive: $output_path"
                # rar handles recursion with -r
                if [ "$folder_choice" = "*" ]; then
                    (cd "$base_path" && rar a -r "$output_path" $items_to_compress >/dev/null)
                else
                    (cd "$base_path" && rar a -r "$output_path" $items_to_compress >/dev/null)
                fi
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        *)
            # Default to tar.gz for any invalid format
            if ! command -v tar &> /dev/null || ! command -v gzip &> /dev/null; then
                echo "Error: 'tar' or 'gzip' command not found. Cannot create tar.gz archive."
                cmd_failed=1
            else
                # Ensure output has the right extension
                if [[ "$output_path" != *.tar.gz ]]; then
                    output_path="${output_path}.tar.gz"
                fi
                
                echo "Creating tar.gz archive: $output_path"
                # Use relative paths for items within the archive
                if [ "$folder_choice" = "*" ]; then
                    (cd "$base_path" && tar -czf "$output_path" $items_to_compress)
                else
                    (cd "$base_path" && tar -czf "$output_path" $items_to_compress)
                fi
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
    esac

    if [ $cmd_failed -eq 0 ]; then
        echo "Successfully created archive: $output_path"
        return 0
    else
        echo "Compression failed."
        # Attempt to remove partially created archive if it exists
        [ -f "$output_path" ] && rm -f "$output_path"
        return 1
    fi
}

# Non-interactive version of decompress_archive
# Usage: decompress_archive_noninteractive archive_path dest_dir
decompress_archive_noninteractive() {
    local archive_path="$1"
    local dest_dir="$2"
    
    if [ -z "$archive_path" ] || [ -z "$dest_dir" ]; then
        echo "Missing required parameters for decompress_archive_noninteractive"
        echo "Usage: decompress_archive_noninteractive archive_path dest_dir"
        return 1
    fi
    
    if [ ! -f "$archive_path" ]; then
        echo "Invalid archive path: File does not exist: $archive_path"
        return 1
    fi
    
    if [ ! -d "$dest_dir" ]; then
        mkdir -p "$dest_dir"
        if [ $? -ne 0 ]; then
            echo "Failed to create destination directory: $dest_dir"
            return 1
        fi
    fi
    
    # Check required tools - suppress interactive prompts
    local tools=("tar" "gzip" "zip" "unzip" "rar" "unrar")
    local missing=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            if [[ "$tool" == "rar" || "$tool" == "unrar" ]]; then
                if ! command -v "rar" &> /dev/null && ! command -v "unrar" &> /dev/null; then
                    if [[ ! " ${missing[@]} " =~ " rar " ]]; then
                        missing+=("rar")
                    fi
                fi
            else
                missing+=("$tool")
            fi
        fi
    done
    
    if [ ${#missing[@]} -gt 0 ]; then
        for package in "${missing[@]}"; do
            if ! command -v "$package" &> /dev/null; then
                if command -v apt &> /dev/null; then
                    sudo apt-get update -qq && sudo apt-get install -qq -y "$package" >/dev/null 2>&1
                fi
            fi
        done
    fi
    
    local cmd_failed=0
    local archive_name=$(basename "$archive_path")
    
    echo "Decompressing: $archive_name to $dest_dir"
    
    case "$archive_name" in
        *.tar.gz|*.tgz)
            if ! command -v tar &> /dev/null || ! command -v gzip &> /dev/null; then
                echo "Error: 'tar' or 'gzip' command not found. Cannot decompress."
                cmd_failed=1
            else
                tar -xzf "$archive_path" -C "$dest_dir"
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        *.zip)
            if ! command -v unzip &> /dev/null; then
                echo "Error: 'unzip' command not found. Cannot decompress."
                cmd_failed=1
            else
                unzip -oq "$archive_path" -d "$dest_dir"
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        *.rar)
            if ! command -v unrar &> /dev/null; then
                echo "Error: 'unrar' command not found. Cannot decompress."
                cmd_failed=1
            else
                unrar x -o+ "$archive_path" "$dest_dir/" > /dev/null
                [ $? -ne 0 ] && cmd_failed=1
            fi
            ;;
        *)
            echo "Unsupported archive format: $archive_name"
            cmd_failed=1
            ;;
    esac

    if [ $cmd_failed -eq 0 ]; then
        echo "Successfully decompressed $archive_name to $dest_dir"
        return 0
    else
        echo "Failed to decompress $archive_name"
        return 1
    fi
} 