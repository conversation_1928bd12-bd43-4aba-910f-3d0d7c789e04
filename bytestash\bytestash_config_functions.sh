#!/bin/bash

# ByteStash Configuration Functions
# Configuration management functions for ByteStash

# Function to change ByteStash port
change_bytestash_port() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║         Change ByteStash Port        ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    echo -e "${GREEN}Current Port:${NC} ${BYTESTASH_PORT}"
    echo -e "${YELLOW}Enter new port (1024-65535):${NC}"
    read -r new_port
    
    # Validate port number
    if ! [[ "$new_port" =~ ^[0-9]+$ ]] || [ "$new_port" -lt 1024 ] || [ "$new_port" -gt 65535 ]; then
        echo -e "${RED}✗ Invalid port number. Please enter a number between 1024-65535${NC}"
        return 1
    fi
    
    # Check if port is already in use
    if netstat -tuln 2>/dev/null | grep -q ":$new_port "; then
        echo -e "${RED}✗ Port $new_port is already in use${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}⚠ This will recreate the ByteStash container with the new port${NC}"
    echo -e "${YELLOW}Continue? [y/N]:${NC}"
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Port change cancelled${NC}"
        return 1
    fi
    
    # Update global variable
    BYTESTASH_PORT="$new_port"
    
    # Stop and remove existing container
    if docker ps --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${YELLOW}Stopping ByteStash container...${NC}"
        docker stop "${BYTESTASH_CONTAINER_NAME}"
    fi
    
    if docker ps -a --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${YELLOW}Removing old container...${NC}"
        docker rm "${BYTESTASH_CONTAINER_NAME}"
    fi
    
    # Recreate container with new port
    echo -e "${YELLOW}Creating container with new port...${NC}"
    install_bytestash
    
    echo -e "${GREEN}✓ Port changed successfully to ${new_port}${NC}"
    echo -e "${GREEN}✓ ByteStash is now accessible at: http://${server_ip}:${new_port}${NC}"
}

# Function to change data directory
change_data_directory() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Change Data Directory           ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    echo -e "${GREEN}Current Data Directory:${NC} ${BYTESTASH_DATA_DIR}"
    echo -e "${YELLOW}Enter new data directory path:${NC}"
    read -r new_data_dir
    
    # Validate directory path
    if [[ -z "$new_data_dir" ]]; then
        echo -e "${RED}✗ Data directory path cannot be empty${NC}"
        return 1
    fi
    
    # Convert to absolute path
    new_data_dir=$(realpath "$new_data_dir" 2>/dev/null || echo "$new_data_dir")
    
    echo -e "${YELLOW}⚠ This will move ByteStash data and recreate the container${NC}"
    echo -e "${YELLOW}New directory: $new_data_dir${NC}"
    echo -e "${YELLOW}Continue? [y/N]:${NC}"
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Directory change cancelled${NC}"
        return 1
    fi
    
    # Stop container if running
    if docker ps --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${YELLOW}Stopping ByteStash container...${NC}"
        docker stop "${BYTESTASH_CONTAINER_NAME}"
    fi
    
    # Create new directory
    echo -e "${YELLOW}Creating new data directory...${NC}"
    sudo mkdir -p "$new_data_dir"
    sudo chmod 755 "$new_data_dir"
    
    # Move existing data if it exists
    if [[ -d "${BYTESTASH_DATA_DIR}" ]] && [[ "${BYTESTASH_DATA_DIR}" != "$new_data_dir" ]]; then
        echo -e "${YELLOW}Moving existing data...${NC}"
        if sudo cp -r "${BYTESTASH_DATA_DIR}"/* "$new_data_dir/" 2>/dev/null; then
            echo -e "${GREEN}✓ Data moved successfully${NC}"
        else
            echo -e "${YELLOW}⚠ No existing data to move or data already exists${NC}"
        fi
    fi
    
    # Update global variable
    BYTESTASH_DATA_DIR="$new_data_dir"
    
    # Remove old container
    if docker ps -a --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${YELLOW}Removing old container...${NC}"
        docker rm "${BYTESTASH_CONTAINER_NAME}"
    fi
    
    # Recreate container with new data directory
    echo -e "${YELLOW}Creating container with new data directory...${NC}"
    install_bytestash
    
    echo -e "${GREEN}✓ Data directory changed successfully to ${new_data_dir}${NC}"
}

# Function to manage environment variables
manage_environment_variables() {
    while true; do
        echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
        echo -e "${CYAN}║      Environment Variables           ║${NC}"
        echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
        
        local options=(
            "01. View Current Variables"
            "02. Modify JWT Secret"
            "03. Change Token Expiry"
            "04. Toggle New Accounts"
            "05. Toggle Debug Mode"
            "06. Toggle Account System"
            "07. Set Base Path"
            "08. ═══════════════════════════════════"
            "09. Apply Changes (Recreate Container)"
            "10. Return to Configuration Menu"
        )

        create_menu "Environment Variables Management" "${options[@]}"

        local choice
        read -rp "$(echo -e ${YELLOW}"Enter your choice (1-10): "${NC})" choice

        case $choice in
            1) view_environment_variables ;;
            2) modify_jwt_secret ;;
            3) change_token_expiry ;;
            4) toggle_new_accounts ;;
            5) toggle_debug_mode ;;
            6) toggle_account_system ;;
            7) set_base_path ;;
            8) echo -e "${CYAN}═══════════════════════════════════${NC}" ;;
            9) apply_environment_changes ;;
            10) return ;;
            *) echo -e "${RED}Invalid choice. Please enter a number between 1 and 10.${NC}" ;;
        esac

        echo
    done
}

# Function to view current environment variables
view_environment_variables() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Current Environment Variables   ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    if docker ps -a --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${GREEN}Container Environment Variables:${NC}"
        docker inspect "${BYTESTASH_CONTAINER_NAME}" --format '{{range .Config.Env}}{{println .}}{{end}}' | grep -E '^(BASE_PATH|JWT_SECRET|TOKEN_EXPIRY|ALLOW_NEW_ACCOUNTS|DEBUG|DISABLE_ACCOUNTS|DISABLE_INTERNAL_ACCOUNTS)' | while read -r env_var; do
            echo -e "${YELLOW}  $env_var${NC}"
        done
    else
        echo -e "${RED}✗ ByteStash container not found${NC}"
        echo -e "${YELLOW}Default Environment Variables:${NC}"
        echo -e "${YELLOW}  BASE_PATH=\"\"${NC}"
        echo -e "${YELLOW}  TOKEN_EXPIRY=\"24h\"${NC}"
        echo -e "${YELLOW}  ALLOW_NEW_ACCOUNTS=\"true\"${NC}"
        echo -e "${YELLOW}  DEBUG=\"false\"${NC}"
        echo -e "${YELLOW}  DISABLE_ACCOUNTS=\"false\"${NC}"
        echo -e "${YELLOW}  DISABLE_INTERNAL_ACCOUNTS=\"false\"${NC}"
    fi
}

# Function to modify JWT secret
modify_jwt_secret() {
    echo -e "${YELLOW}Enter new JWT secret (or press Enter to auto-generate):${NC}"
    read -r new_jwt_secret
    
    if [[ -z "$new_jwt_secret" ]]; then
        new_jwt_secret=$(openssl rand -hex 32)
        echo -e "${GREEN}Generated JWT secret: $new_jwt_secret${NC}"
    fi
    
    JWT_SECRET="$new_jwt_secret"
    echo -e "${GREEN}✓ JWT secret updated (apply changes to take effect)${NC}"
}

# Function to change token expiry
change_token_expiry() {
    echo -e "${YELLOW}Current token expiry: ${TOKEN_EXPIRY:-24h}${NC}"
    echo -e "${YELLOW}Enter new token expiry (e.g., 24h, 7d, 30m):${NC}"
    read -r new_expiry
    
    if [[ -z "$new_expiry" ]]; then
        echo -e "${RED}✗ Token expiry cannot be empty${NC}"
        return 1
    fi
    
    TOKEN_EXPIRY="$new_expiry"
    echo -e "${GREEN}✓ Token expiry updated to $new_expiry (apply changes to take effect)${NC}"
}

# Function to toggle new accounts
toggle_new_accounts() {
    local current_value=${ALLOW_NEW_ACCOUNTS:-true}
    echo -e "${YELLOW}Current setting: Allow new accounts = $current_value${NC}"
    echo -e "${YELLOW}Toggle to $([ "$current_value" = "true" ] && echo "false" || echo "true")? [y/N]:${NC}"
    read -r confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        ALLOW_NEW_ACCOUNTS=$([ "$current_value" = "true" ] && echo "false" || echo "true")
        echo -e "${GREEN}✓ Allow new accounts set to $ALLOW_NEW_ACCOUNTS (apply changes to take effect)${NC}"
    fi
}

# Function to toggle debug mode
toggle_debug_mode() {
    local current_value=${DEBUG:-false}
    echo -e "${YELLOW}Current setting: Debug mode = $current_value${NC}"
    echo -e "${YELLOW}Toggle to $([ "$current_value" = "true" ] && echo "false" || echo "true")? [y/N]:${NC}"
    read -r confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        DEBUG=$([ "$current_value" = "true" ] && echo "false" || echo "true")
        echo -e "${GREEN}✓ Debug mode set to $DEBUG (apply changes to take effect)${NC}"
    fi
}

# Function to toggle account system
toggle_account_system() {
    local current_value=${DISABLE_ACCOUNTS:-false}
    echo -e "${YELLOW}Current setting: Disable accounts = $current_value${NC}"
    echo -e "${YELLOW}Toggle to $([ "$current_value" = "true" ] && echo "false" || echo "true")? [y/N]:${NC}"
    read -r confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        DISABLE_ACCOUNTS=$([ "$current_value" = "true" ] && echo "false" || echo "true")
        echo -e "${GREEN}✓ Disable accounts set to $DISABLE_ACCOUNTS (apply changes to take effect)${NC}"
    fi
}

# Function to set base path
set_base_path() {
    echo -e "${YELLOW}Current base path: ${BASE_PATH:-\"\"}${NC}"
    echo -e "${YELLOW}Enter new base path (or press Enter for empty):${NC}"
    read -r new_base_path
    
    BASE_PATH="$new_base_path"
    echo -e "${GREEN}✓ Base path updated to \"$BASE_PATH\" (apply changes to take effect)${NC}"
}

# Function to apply environment changes
apply_environment_changes() {
    echo -e "${YELLOW}⚠ This will recreate the ByteStash container with new environment variables${NC}"
    echo -e "${YELLOW}Continue? [y/N]:${NC}"
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Changes not applied${NC}"
        return 1
    fi
    
    # Stop and remove existing container
    if docker ps --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${YELLOW}Stopping ByteStash container...${NC}"
        docker stop "${BYTESTASH_CONTAINER_NAME}"
    fi
    
    if docker ps -a --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo -e "${YELLOW}Removing old container...${NC}"
        docker rm "${BYTESTASH_CONTAINER_NAME}"
    fi
    
    # Create container with updated environment variables
    echo -e "${YELLOW}Creating container with updated environment...${NC}"
    docker run -d \
        --name "${BYTESTASH_CONTAINER_NAME}" \
        --restart unless-stopped \
        -p "${BYTESTASH_PORT}:5000" \
        -v "${BYTESTASH_DATA_DIR}:/data/snippets" \
        -e BASE_PATH="${BASE_PATH:-}" \
        -e JWT_SECRET="${JWT_SECRET:-$(openssl rand -hex 32)}" \
        -e TOKEN_EXPIRY="${TOKEN_EXPIRY:-24h}" \
        -e ALLOW_NEW_ACCOUNTS="${ALLOW_NEW_ACCOUNTS:-true}" \
        -e DEBUG="${DEBUG:-false}" \
        -e DISABLE_ACCOUNTS="${DISABLE_ACCOUNTS:-false}" \
        -e DISABLE_INTERNAL_ACCOUNTS="${DISABLE_INTERNAL_ACCOUNTS:-false}" \
        "${BYTESTASH_IMAGE}"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Environment variables applied successfully!${NC}"
        echo -e "${GREEN}✓ ByteStash is accessible at: http://${server_ip}:${BYTESTASH_PORT}${NC}"
    else
        echo -e "${RED}✗ Failed to apply environment changes${NC}"
        return 1
    fi
}

# Function to backup configuration
backup_configuration() {
    echo -e "${CYAN}╔══════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║      Backup Configuration            ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════╝${NC}"
    
    local config_backup_dir="/tmp/bytestash_config_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$config_backup_dir"
    
    # Create configuration backup file
    local config_file="$config_backup_dir/bytestash_config.txt"
    
    echo "# ByteStash Configuration Backup - $(date)" > "$config_file"
    echo "BYTESTASH_CONTAINER_NAME=$BYTESTASH_CONTAINER_NAME" >> "$config_file"
    echo "BYTESTASH_IMAGE=$BYTESTASH_IMAGE" >> "$config_file"
    echo "BYTESTASH_PORT=$BYTESTASH_PORT" >> "$config_file"
    echo "BYTESTASH_DATA_DIR=$BYTESTASH_DATA_DIR" >> "$config_file"
    echo "BYTESTASH_BACKUP_REMOTE=$BYTESTASH_BACKUP_REMOTE" >> "$config_file"
    
    # Backup container configuration if exists
    if docker ps -a --format "table {{.Names}}" | grep -q "^${BYTESTASH_CONTAINER_NAME}$"; then
        echo "" >> "$config_file"
        echo "# Container Environment Variables" >> "$config_file"
        docker inspect "${BYTESTASH_CONTAINER_NAME}" --format '{{range .Config.Env}}{{println .}}{{end}}' | grep -E '^(BASE_PATH|JWT_SECRET|TOKEN_EXPIRY|ALLOW_NEW_ACCOUNTS|DEBUG|DISABLE_ACCOUNTS|DISABLE_INTERNAL_ACCOUNTS)' >> "$config_file"
        
        echo "" >> "$config_file"
        echo "# Container Configuration" >> "$config_file"
        docker inspect "${BYTESTASH_CONTAINER_NAME}" > "$config_backup_dir/container_inspect.json"
    fi
    
    # Create compressed backup
    local backup_archive="/tmp/bytestash_config_$(date +%Y%m%d_%H%M%S).tar.gz"
    tar -czf "$backup_archive" -C "$(dirname $config_backup_dir)" "$(basename $config_backup_dir)"
    
    echo -e "${GREEN}✓ Configuration backup created: $backup_archive${NC}"
    echo -e "${GREEN}✓ Configuration file: $config_file${NC}"
    
    # Clean up temporary directory
    rm -rf "$config_backup_dir"
    
    echo -e "${YELLOW}Backup location: $backup_archive${NC}"
}