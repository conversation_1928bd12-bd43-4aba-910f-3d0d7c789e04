Enter your choice: 4
Starting COMPLETE restore...
This will restore everything to a fully working state.
Starting COMPLETE Netbird restore...
====================================
This will restore:
- Configuration files
- Application data and databases
- Docker volumes
- Machine keys and certificates

WARNING: This will overwrite any existing Netbird installation!
Are you sure you want to continue? (y/n): y

Starting complete restoration process...

Step 1/4: Restoring configuration files...
Restoring Netbird configuration...
Rclone is already installed.
Downloading configuration backup from cloud storage...
Transferred:        3.916 KiB / 3.916 KiB, 100%, 0 B/s, ETA -
Transferred:            1 / 1, 100%
Elapsed time:         2.4s
Where would you like to restore the Netbird configuration?
Enter path (default: /opt/netbird): 
Extracting configuration files...
Configuration restored successfully to /opt/netbird

Step 2/4: Restoring Docker volumes...
Restoring Netbird Docker volumes...
Rclone is already installed.
Downloading volumes backup from cloud storage...
Transferred:       37.878 MiB / 37.878 MiB, 100%, 10.667 MiB/s, ETA 0s
Transferred:            1 / 1, 100%
Elapsed time:         4.7s
Stopping all Netbird services...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
Extracting volumes backup...
Restoring Docker volumes...
Restoring volume: netbird_netbird_caddy_data
Restoring volume: netbird_netbird_management
Restoring volume: netbird_netbird_zdb_data
Restoring volume: netbird_netbird_zitadel_certs
Volumes restored successfully.
Starting Netbird services...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 56/56
 ✔ management Pulled                                                                                                                                                                                                                   22.5s 
 ✔ zitadel Pulled                                                                                                                                                                                                                       7.3s 
 ✔ dashboard Pulled                                                                                                                                                                                                                    21.3s 
 ✔ signal Pulled                                                                                                                                                                                                                        9.8s 
 ✔ relay Pulled                                                                                                                                                                                                                         9.9s 
 ✔ caddy Pulled                                                                                                                                                                                                                         5.1s 
 ✔ zdb Pulled                                                                                                                                                                                                                          22.5s 
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
[+] Running 0/1
 ⠋ Network netbird_netbird  Creating                                                                                                                                                                                                    0.1s 
WARN[0022] volume "netbird_netbird_management" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0022] volume "netbird_netbird_zitadel_certs" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
[+] Running 9/9me "netbird_netbird_zdb_data" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
 ✔ Network netbird_netbird         Created                                                                                                                                                                                              0.1s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              1.5s 
 ✔ Container netbird-coturn-1      Started                                                                                                                                                                                              1.1s 
 ✔ Container netbird-zdb-1         Healthy                                                                                                                                                                                              7.5s 
 ✔ Container netbird-caddy-1       Started                                                                                                                                                                                              2.7s 
 ✔ Container netbird-dashboard-1   Started                                                                                                                                                                                              1.6s 
 ✔ Container netbird-signal-1      Started                                                                                                                                                                                              1.4s 
 ✔ Container netbird-relay-1       Started                                                                                                                                                                                              1.8s 
 ✔ Container netbird-zitadel-1     Started                                                                                                                                                                                              7.6s 

Step 3/4: Restoring application data...
Restoring Netbird application data...
Rclone is already installed.
Downloading data backup from cloud storage...
Transferred:       38.957 MiB / 38.957 MiB, 100%, 9.003 MiB/s, ETA 0s
Transferred:            1 / 1, 100%
Elapsed time:         6.0s
Stopping all services for data restoration...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 9/9
 ✔ Container netbird-zitadel-1     Removed                                                                                                                                                                                              0.6s 
 ✔ Container netbird-caddy-1       Removed                                                                                                                                                                                              1.0s 
 ✔ Container netbird-dashboard-1   Removed                                                                                                                                                                                              4.1s 
 ✔ Container netbird-signal-1      Removed                                                                                                                                                                                              0.6s 
 ✔ Container netbird-relay-1       Removed                                                                                                                                                                                              0.6s 
 ✔ Container netbird-management-1  Removed                                                                                                                                                                                              0.0s 
 ✔ Container netbird-coturn-1      Removed                                                                                                                                                                                              2.5s 
 ✔ Container netbird-zdb-1         Removed                                                                                                                                                                                              0.3s 
 ✔ Network netbird_netbird         Removed                                                                                                                                                                                              0.2s 
Extracting and restoring application data...
Restoring management data...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 1/1
[+] Running 2/2ird_netbird  Created                                                                                                                                                                                                     0.1s 
 ✔ Network netbird_netbird         Created                                                                                                                                                                                              0.1s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              0.4s 
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Copying 1/1
 ✔ netbird-management-1 copy /opt/netbird_backups/data_restore_temp/management_data/. to netbird-management-1:/var/lib/netbird/ Copied                                                                                                  0.3s 
Management data restored successfully.
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Stopping 1/1
 ✔ Container netbird-management-1  Stopped                                                                                                                                                                                              0.0s 
No Zitadel database backup found to restore.
Restoring machine keys and certificates...
Application data restored successfully.
Starting all Netbird services...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
WARN[0000] volume "netbird_netbird_zitadel_certs" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0000] volume "netbird_netbird_zdb_data" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0000] volume "netbird_netbird_caddy_data" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0000] volume "netbird_netbird_management" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
[+] Running 8/8
 ✔ Container netbird-coturn-1      Started                                                                                                                                                                                              1.1s 
 ✔ Container netbird-signal-1      Started                                                                                                                                                                                              1.8s 
 ✔ Container netbird-caddy-1       Started                                                                                                                                                                                              2.7s 
 ✔ Container netbird-dashboard-1   Started                                                                                                                                                                                              1.9s 
 ✔ Container netbird-relay-1       Started                                                                                                                                                                                              2.0s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              1.7s 
 ✔ Container netbird-zdb-1         Healthy                                                                                                                                                                                              7.3s 
 ✔ Container netbird-zitadel-1     Started                                                                                                                                                                                              7.6s 

Step 4/4: Final service restart and verification...
Restarting all services with restored data...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 9/9
 ✔ Container netbird-relay-1       Removed                                                                                                                                                                                              0.6s 
 ✔ Container netbird-signal-1      Removed                                                                                                                                                                                              0.7s 
 ✔ Container netbird-zitadel-1     Removed                                                                                                                                                                                              0.3s 
 ✔ Container netbird-caddy-1       Removed                                                                                                                                                                                              1.1s 
 ✔ Container netbird-management-1  Removed                                                                                                                                                                                              0.0s 
 ✔ Container netbird-dashboard-1   Removed                                                                                                                                                                                              3.6s 
 ✔ Container netbird-coturn-1      Removed                                                                                                                                                                                              3.8s 
 ✔ Container netbird-zdb-1         Removed                                                                                                                                                                                              0.5s 
 ✔ Network netbird_netbird         Removed                                                                                                                                                                                              0.2s 
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 0/1
 ⠋ Network netbird_netbird  Creating                                                                                                                                                                                                    0.1s 
WARN[0000] volume "netbird_netbird_caddy_data" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0000] volume "netbird_netbird_management" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
[+] Running 9/9me "netbird_netbird_zitadel_certs" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
 ✔ Network netbird_netbird         Created                                                                                                                                                                                              0.1s 
 ✔ Container netbird-relay-1       Started                                                                                                                                                                                              1.9s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              2.4s 
 ✔ Container netbird-coturn-1      Started                                                                                                                                                                                              1.1s 
 ✔ Container netbird-zdb-1         Healthy                                                                                                                                                                                              7.9s 
 ✔ Container netbird-caddy-1       Started                                                                                                                                                                                              2.7s 
 ✔ Container netbird-dashboard-1   Started                                                                                                                                                                                              1.0s 
 ✔ Container netbird-signal-1      Started                                                                                                                                                                                              2.4s 
 ✔ Container netbird-zitadel-1     Started                                                                                                                                                                                              8.1s 
Waiting for services to start...
Warning: Some services may not have started properly.

=================================================
Netbird restore completed with ERRORS!
=================================================
Some components may not have been restored properly.
Check the output above for details.
You may need to:
1. Check service logs: docker compose logs
2. Restart services manually: docker compose restart
3. Verify network connectivity and DNS settings
COMPLETE restore failed.


root@remote:~# docker ps
CONTAINER ID   IMAGE                             COMMAND                  CREATED              STATUS                    PORTS                                                                                                                                                                          NAMES
db4697628528   ghcr.io/zitadel/zitadel:v2.64.1   "/app/zitadel start-…"   About a minute ago   Up 51 seconds                                                                                                                                                                                            netbird-zitadel-1
6adb73a44912   coturn/coturn                     "docker-entrypoint.s…"   About a minute ago   Up 59 seconds                                                                                                                                                                                            netbird-coturn-1
032ab5c4e3dd   netbirdio/dashboard:latest        "/usr/bin/supervisor…"   About a minute ago   Up 59 seconds             80/tcp, 443/tcp                                                                                                                                                                netbird-dashboard-1
77acd288d790   netbirdio/management:latest       "/go/bin/netbird-mgm…"   About a minute ago   Up 38 seconds                                                                                                                                                                                            netbird-management-1
04b28b43f325   netbirdio/relay:latest            "/go/bin/netbird-rel…"   About a minute ago   Up 59 seconds                                                                                                                                                                                            netbird-relay-1
ed53fd9e7125   postgres:16-alpine                "docker-entrypoint.s…"   About a minute ago   Up 59 seconds (healthy)   5432/tcp                                                                                                                                                                       netbird-zdb-1
14225ddc9799   caddy                             "caddy run --config …"   About a minute ago   Up 59 seconds             0.0.0.0:80->80/tcp, [::]:80->80/tcp, 0.0.0.0:443->443/tcp, [::]:443->443/tcp, 0.0.0.0:8080->8080/tcp, [::]:8080->8080/tcp, 0.0.0.0:443->443/udp, [::]:443->443/udp, 2019/tcp   netbird-caddy-1
c918a8c7b827   netbirdio/signal:latest           "/go/bin/netbird-sig…"   About a minute ago   Up 59 seconds                                                                                                                                                                                            netbird-signal-1
root@remote:~# 