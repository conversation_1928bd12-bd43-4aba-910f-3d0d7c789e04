Starting COMPLETE restore...
This will restore everything to a fully working state.
Starting COMPLETE Netbird restore...
====================================
This will restore:
- Configuration files
- Application data and databases
- Docker volumes
- Machine keys and certificates

WARNING: This will overwrite any existing Netbird installation!
Are you sure you want to continue? (y/n): y

Starting complete restoration process...

Step 1/4: Restoring configuration files...
Restoring Netbird configuration...
Rclone is already installed.
Downloading configuration backup from cloud storage...
Transferred:        3.916 KiB / 3.916 KiB, 100%, 0 B/s, ETA -
Transferred:            1 / 1, 100%
Elapsed time:         2.8s
Where would you like to restore the Netbird configuration?
Enter path (default: /opt/netbird): 
Extracting configuration files...
Configuration restored successfully to /opt/netbird

Step 2/4: Restoring Docker volumes...
Restoring Netbird Docker volumes...
Rclone is already installed.
Downloading volumes backup from cloud storage...
Transferred:       37.878 MiB / 37.878 MiB, 100%, 12.010 MiB/s, ETA 0s
Transferred:            1 / 1, 100%
Elapsed time:         6.2s
Stopping all Netbird services...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
Extracting volumes backup...
Restoring Docker volumes...
Restoring volume: netbird_netbird_caddy_data
Restoring volume: netbird_netbird_management
Restoring volume: netbird_netbird_zdb_data
Restoring volume: netbird_netbird_zitadel_certs
Volumes restored successfully.
Starting Netbird services...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 56/56
 ✔ management Pulled                                                                                                                                                                                                                   19.0s 
 ✔ caddy Pulled                                                                                                                                                                                                                        11.1s 
 ✔ dashboard Pulled                                                                                                                                                                                                                    17.1s 
 ✔ zitadel Pulled                                                                                                                                                                                                                       6.0s 
 ✔ signal Pulled                                                                                                                                                                                                                       13.4s 
 ✔ relay Pulled                                                                                                                                                                                                                         6.3s 
 ✔ zdb Pulled                                                                                                                                                                                                                          22.4s 
                                                                                                                                                                                                                                             
[+] Running 0/1
 ⠋ Network netbird_netbird  Creating                                                                                                                                                                                                    0.0s 
WARN[0022] volume "netbird_netbird_zitadel_certs" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0022] volume "netbird_netbird_zdb_data" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
[+] Running 9/9me "netbird_netbird_caddy_data" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
 ✔ Network netbird_netbird         Created                                                                                                                                                                                              0.1s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              2.4s 
 ✔ Container netbird-zdb-1         Healthy                                                                                                                                                                                              6.5s 
 ✔ Container netbird-coturn-1      Started                                                                                                                                                                                              1.3s 
 ✔ Container netbird-dashboard-1   Started                                                                                                                                                                                              2.3s 
 ✔ Container netbird-caddy-1       Started                                                                                                                                                                                              3.4s 
 ✔ Container netbird-signal-1      Started                                                                                                                                                                                              2.1s 
 ✔ Container netbird-relay-1       Started                                                                                                                                                                                              2.4s 
 ✔ Container netbird-zitadel-1     Started                                                                                                                                                                                              7.0s 

Step 3/4: Restoring application data...
Restoring Netbird application data...
Rclone is already installed.
Downloading data backup from cloud storage...
Transferred:       38.957 MiB / 38.957 MiB, 100%, 15.501 MiB/s, ETA 0s
Transferred:            1 / 1, 100%
Elapsed time:         4.7s
Stopping all services for data restoration...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 9/9
 ✔ Container netbird-signal-1      Removed                                                                                                                                                                                              0.6s 
 ✔ Container netbird-management-1  Removed                                                                                                                                                                                              0.1s 
 ✔ Container netbird-coturn-1      Removed                                                                                                                                                                                              4.6s 
 ✔ Container netbird-zitadel-1     Removed                                                                                                                                                                                              0.7s 
 ✔ Container netbird-caddy-1       Removed                                                                                                                                                                                              1.0s 
 ✔ Container netbird-dashboard-1   Removed                                                                                                                                                                                              3.4s 
 ✔ Container netbird-relay-1       Removed                                                                                                                                                                                              0.5s 
 ✔ Container netbird-zdb-1         Removed                                                                                                                                                                                              0.3s 
 ✔ Network netbird_netbird         Removed                                                                                                                                                                                              0.3s 
Extracting and restoring application data...
Restoring management data...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
WARN[0000] volume "netbird_netbird_management" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
[+] Running 2/2
 ✔ Network netbird_netbird         Created                                                                                                                                                                                              0.1s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              0.6s 
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Copying 1/1
 ✔ netbird-management-1 copy /opt/netbird_backups/data_restore_temp/management_data/. to netbird-management-1:/var/lib/netbird/ Copied                                                                                                  0.3s 
Management data restored successfully.
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Stopping 1/1
 ✔ Container netbird-management-1  Stopped                                                                                                                                                                                              0.0s 
No Zitadel database backup found to restore.
Restoring machine keys and certificates...
Application data restored successfully.
Starting all Netbird services...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
WARN[0000] volume "netbird_netbird_management" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0000] volume "netbird_netbird_zitadel_certs" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0000] volume "netbird_netbird_zdb_data" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0000] volume "netbird_netbird_caddy_data" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
[+] Running 8/8
 ✔ Container netbird-caddy-1       Started                                                                                                                                                                                              3.0s 
 ✔ Container netbird-dashboard-1   Started                                                                                                                                                                                              2.3s 
 ✔ Container netbird-signal-1      Started                                                                                                                                                                                              2.0s 
 ✔ Container netbird-relay-1       Started                                                                                                                                                                                              2.5s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              1.9s 
 ✔ Container netbird-coturn-1      Started                                                                                                                                                                                              1.0s 
 ✔ Container netbird-zdb-1         Healthy                                                                                                                                                                                              7.6s 
 ✔ Container netbird-zitadel-1     Started                                                                                                                                                                                              7.8s 

Step 4/4: Final service restart and verification...
Restarting all services with restored data...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 9/9
 ✔ Container netbird-relay-1       Removed                                                                                                                                                                                              0.6s 
 ✔ Container netbird-management-1  Removed                                                                                                                                                                                              0.0s 
 ✔ Container netbird-signal-1      Removed                                                                                                                                                                                              0.6s 
 ✔ Container netbird-dashboard-1   Removed                                                                                                                                                                                              3.5s 
 ✔ Container netbird-caddy-1       Removed                                                                                                                                                                                              1.0s 
 ✔ Container netbird-zitadel-1     Removed                                                                                                                                                                                              0.6s 
 ✔ Container netbird-coturn-1      Removed                                                                                                                                                                                              3.6s 
 ✔ Container netbird-zdb-1         Removed                                                                                                                                                                                              0.3s 
 ✔ Network netbird_netbird         Removed                                                                                                                                                                                              0.3s 
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
WARN[0000] volume "netbird_netbird_zitadel_certs" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0000] volume "netbird_netbird_zdb_data" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0000] volume "netbird_netbird_caddy_data" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
WARN[0000] volume "netbird_netbird_management" already exists but was not created by Docker Compose. Use `external: true` to use an existing volume 
[+] Running 9/9
 ✔ Network netbird_netbird         Created                                                                                                                                                                                              0.1s 
 ✔ Container netbird-zdb-1         Healthy                                                                                                                                                                                              7.5s 
 ✔ Container netbird-coturn-1      Started                                                                                                                                                                                              1.0s 
 ✔ Container netbird-signal-1      Started                                                                                                                                                                                              2.0s 
 ✔ Container netbird-relay-1       Started                                                                                                                                                                                              1.8s 
 ✔ Container netbird-caddy-1       Started                                                                                                                                                                                              2.4s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              2.0s 
 ✔ Container netbird-dashboard-1   Started                                                                                                                                                                                              2.2s 
 ✔ Container netbird-zitadel-1     Started                                                                                                                                                                                              7.8s 
Waiting for services to start...
Warning: Some services may not have started properly.

=================================================
Netbird restore completed with ERRORS!
=================================================
Some components may not have been restored properly.
Check the output above for details.
You may need to:
1. Check service logs: docker compose logs
2. Restart services manually: docker compose restart
3. Verify network connectivity and DNS settings
COMPLETE restore failed.

╔════════════════════════════════════════════════════╗
║ Netbird Restore Menu                                 ║
╠════════════════════════════════════════════════════╣
║ 01. Restore Configuration Only                       ║
║ 02. Restore Data Only                                ║
║ 03. Restore Docker Volumes Only                      ║
║ 04. COMPLETE Restore (Config + Data + Volumes)       ║
║ 05. List Available Backups                           ║
║ 06. Back to Netbird Menu                             ║
╚════════════════════════════════════════════════════╝
Enter your choice: ^C
root@remote:~/bash60# docker ps
CONTAINER ID   IMAGE                             COMMAND                  CREATED              STATUS                        PORTS                                                                                                                                                                          NAMES
4c1105a1a5f4   ghcr.io/zitadel/zitadel:v2.64.1   "/app/zitadel start-…"   About a minute ago   Up About a minute                                                                                                                                                                                            netbird-zitadel-1
5481779560e9   caddy                             "caddy run --config …"   About a minute ago   Up About a minute             0.0.0.0:80->80/tcp, [::]:80->80/tcp, 0.0.0.0:443->443/tcp, [::]:443->443/tcp, 0.0.0.0:8080->8080/tcp, [::]:8080->8080/tcp, 0.0.0.0:443->443/udp, [::]:443->443/udp, 2019/tcp   netbird-caddy-1
dbb7331fc5a8   coturn/coturn                     "docker-entrypoint.s…"   About a minute ago   Up About a minute                                                                                                                                                                                            netbird-coturn-1
3bfff1dbc23c   postgres:16-alpine                "docker-entrypoint.s…"   About a minute ago   Up About a minute (healthy)   5432/tcp                                                                                                                                                                       netbird-zdb-1
8c614195f17f   netbirdio/dashboard:latest        "/usr/bin/supervisor…"   About a minute ago   Up About a minute             80/tcp, 443/tcp                                                                                                                                                                netbird-dashboard-1
7517097ef247   netbirdio/signal:latest           "/go/bin/netbird-sig…"   About a minute ago   Up About a minute                                                                                                                                                                                            netbird-signal-1
0c3461865ea1   netbirdio/management:latest       "/go/bin/netbird-mgm…"   About a minute ago   Up 48 seconds                                                                                                                                                                                                netbird-management-1
9c55408023e5   netbirdio/relay:latest            "/go/bin/netbird-rel…"   About a minute ago   Up About a minute                                                                                                                                                                                            netbird-relay-1
root@remote:~/bash60# ^C