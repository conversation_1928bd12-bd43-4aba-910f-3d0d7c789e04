few issues i did full bacaup and full restore check this "╔═══════════════════════════════════════════════════╗
║ Netbird Backup Menu                                 ║
╠═══════════════════════════════════════════════════╣
║ 01. Backup Configuration Only                       ║
║ 02. Backup Data Only                                ║
║ 03. Backup Docker Volumes Only                      ║
║ 04. COMPLETE Backup (Config + Data + Volumes)       ║
║ 05. View Backup Status                              ║
║ 06. Back to Netbird Menu                            ║
╚═══════════════════════════════════════════════════╝
Enter your choice: 4
Starting COMPLETE backup...
This will backup everything needed for full restoration.
Starting COMPLETE Netbird backup...
===================================
This will backup:
- Configuration files (docker-compose.yml, etc.)
- Application data (management database, etc.)
- Docker volumes (Zitadel DB, Caddy data, certificates)
- Machine keys and certificates

Backup timestamp: 20250610_124718

Step 1/3: Backing up configuration files...
Backing up Netbird configuration files...
Rclone is already installed.
Creating configuration backup archive...
Configuration backup created successfully.
Uploading configuration backup to cloud storage...
Transferred:        3.963 KiB / 3.963 KiB, 100%, 579 B/s, ETA 0s
Transferred:            1 / 1, 100%
Elapsed time:         9.1s
Configuration backup uploaded successfully.

Step 2/3: Backing up application data and databases...
Backing up Netbird application data...
Rclone is already installed.
Stopping services for consistent data backup...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Stopping 8/8
 ✔ Container netbird-dashboard-1   Stopped                                                                                                                                                                                              3.8s 
 ✔ Container netbird-signal-1      Stopped                                                                                                                                                                                              0.9s 
 ✔ Container netbird-coturn-1      Stopped                                                                                                                                                                                              2.3s 
 ✔ Container netbird-zitadel-1     Stopped                                                                                                                                                                                              0.9s 
 ✔ Container netbird-caddy-1       Stopped                                                                                                                                                                                              1.5s 
 ✔ Container netbird-management-1  Stopped                                                                                                                                                                                              0.7s 
 ✔ Container netbird-relay-1       Stopped                                                                                                                                                                                              0.9s 
 ✔ Container netbird-zdb-1         Stopped                                                                                                                                                                                              0.4s 
Backing up management service data...
Management data backed up successfully.
Backing up Zitadel database...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
Backing up certificates and keys...
Creating comprehensive data backup archive...
Data backup created successfully.
Uploading data backup to cloud storage...
Transferred:       29.785 MiB / 29.785 MiB, 100%, 5.957 MiB/s, ETA 0s
Transferred:            1 / 1, 100%
Elapsed time:         7.4s
Data backup uploaded successfully.
Restarting Netbird services...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 8/8
 ✔ Container netbird-coturn-1      Started                                                                                                                                                                                              1.2s 
 ✔ Container netbird-signal-1      Started                                                                                                                                                                                              1.9s 
 ✔ Container netbird-caddy-1       Started                                                                                                                                                                                              2.6s 
 ✔ Container netbird-relay-1       Started                                                                                                                                                                                              0.8s 
 ✔ Container netbird-zdb-1         Healthy                                                                                                                                                                                              7.6s 
 ✔ Container netbird-dashboard-1   Started                                                                                                                                                                                              2.1s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              2.0s 
 ✔ Container netbird-zitadel-1     Started                                                                                                                                                                                              7.9s 

Step 3/3: Backing up Docker volumes...
Backing up Netbird Docker volumes...
Stopping all Netbird services for consistent volume backup...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Stopping 8/8
 ✔ Container netbird-zitadel-1     Stopped                                                                                                                                                                                              0.8s 
 ✔ Container netbird-caddy-1       Stopped                                                                                                                                                                                              1.3s 
 ✔ Container netbird-dashboard-1   Stopped                                                                                                                                                                                              4.0s 
 ✔ Container netbird-signal-1      Stopped                                                                                                                                                                                              0.8s 
 ✔ Container netbird-relay-1       Stopped                                                                                                                                                                                              0.7s 
 ✔ Container netbird-management-1  Stopped                                                                                                                                                                                              1.5s 
 ✔ Container netbird-coturn-1      Stopped                                                                                                                                                                                              3.2s 
 ✔ Container netbird-zdb-1         Stopped                                                                                                                                                                                              0.5s 
Backing up Docker volumes...
No volumes found to backup.
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 8/8
 ✔ Container netbird-coturn-1      Started                                                                                                                                                                                              0.9s 
 ✔ Container netbird-zdb-1         Healthy                                                                                                                                                                                              7.6s 
 ✔ Container netbird-zitadel-1     Started                                                                                                                                                                                              7.9s 
 ✔ Container netbird-caddy-1       Started                                                                                                                                                                                              2.5s 
 ✔ Container netbird-dashboard-1   Started                                                                                                                                                                                              1.6s 
 ✔ Container netbird-signal-1      Started                                                                                                                                                                                              1.0s 
 ✔ Container netbird-relay-1       Started                                                                                                                                                                                              2.1s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              1.3s 

=========================================
COMPLETE Netbird backup finished successfully!
=========================================
Backup includes:
✓ Configuration files
✓ Management database
✓ Zitadel identity provider database
✓ Docker volumes (Caddy, certificates, etc.)
✓ Machine keys and certificates

This backup can be used to restore a fully working
Netbird installation on any compatible system.
COMPLETE backup completed successfully." and here are restore logs "╔════════════════════════════════════════════════════╗
║ Netbird Restore Menu                                 ║
╠════════════════════════════════════════════════════╣
║ 01. Restore Configuration Only                       ║
║ 02. Restore Data Only                                ║
║ 03. Restore Docker Volumes Only                      ║
║ 04. COMPLETE Restore (Config + Data + Volumes)       ║
║ 05. List Available Backups                           ║
║ 06. Back to Netbird Menu                             ║
╚════════════════════════════════════════════════════╝
Enter your choice: 4
Starting COMPLETE restore...
This will restore everything to a fully working state.
Starting COMPLETE Netbird restore...
====================================
This will restore:
- Configuration files
- Application data and databases
- Docker volumes
- Machine keys and certificates

WARNING: This will overwrite any existing Netbird installation!
Are you sure you want to continue? (y/n): y

Starting complete restoration process...

Step 1/4: Restoring configuration files...
Restoring Netbird configuration...
Rclone is already installed.
Downloading configuration backup from cloud storage...
Transferred:        3.916 KiB / 3.916 KiB, 100%, 0 B/s, ETA -
Transferred:            1 / 1, 100%
Elapsed time:         2.3s
Where would you like to restore the Netbird configuration?
Enter path (default: /opt/netbird): 
Extracting configuration files...
Configuration restored successfully to /opt/netbird

Step 2/4: Restoring Docker volumes...
Restoring Netbird Docker volumes...
Rclone is already installed.
Downloading volumes backup from cloud storage...
2025-06-10 12:49:45 ERROR : Encrypted drive 'Eyunion:/Backups/Netbird/netbird_volumes_backup.tar.gz': error reading source root directory: directory not found
2025-06-10 12:49:45 ERROR : Attempt 1/3 failed with 1 errors and: directory not found
2025-06-10 12:49:45 ERROR : Encrypted drive 'Eyunion:/Backups/Netbird/netbird_volumes_backup.tar.gz': error reading source root directory: directory not found
2025-06-10 12:49:45 ERROR : Attempt 2/3 failed with 1 errors and: directory not found
2025-06-10 12:49:47 ERROR : Encrypted drive 'Eyunion:/Backups/Netbird/netbird_volumes_backup.tar.gz': error reading source root directory: directory not found
2025-06-10 12:49:47 ERROR : Attempt 3/3 failed with 1 errors and: directory not found
Transferred:              0 B / 0 B, -, 0 B/s, ETA -
Errors:                 1 (retrying may help)
Elapsed time:         5.0s
2025/06/10 12:49:47 Failed to copy: directory not found
Failed to download volumes backup.
Volumes restore failed.

Step 3/4: Restoring application data...
Restoring Netbird application data...
Rclone is already installed.
Downloading data backup from cloud storage...
Transferred:       29.778 MiB / 29.778 MiB, 100%, 13.491 MiB/s, ETA 0s
Transferred:            1 / 1, 100%
Elapsed time:         4.0s
Stopping all services for data restoration...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
Extracting and restoring application data...
Restoring management data...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 4/4
 ✔ management Pulled                                                                                                                                                                                                                    7.4s 
   ✔ f90c8eb4724c Pull complete                                                                                                                                                                                                         3.7s 
   ✔ ac07b4cba2e4 Pull complete                                                                                                                                                                                                         4.8s 
   ✔ 0ea2a0e4e4ad Pull complete                                                                                                                                                                                                         5.7s 
[+] Running 3/3
 ✔ Network netbird_netbird              Created                                                                                                                                                                                         0.1s 
 ✔ Volume "netbird_netbird_management"  Created                                                                                                                                                                                         0.0s 
 ✔ Container netbird-management-1       Started                                                                                                                                                                                         0.5s 
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Copying 1/1
 ✔ netbird-management-1 copy /opt/netbird_backups/data_restore_temp/management_data/. to netbird-management-1:/var/lib/netbird/ Copied                                                                                                  0.3s 
Management data restored successfully.
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Stopping 1/1
 ✔ Container netbird-management-1  Stopped                                                                                                                                                                                              0.0s 
Application data restored successfully.
Starting all Netbird services...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 52/52
 ✔ caddy Pulled                                                                                                                                                                                                                         5.4s 
 ✔ dashboard Pulled                                                                                                                                                                                                                    19.4s 
 ✔ signal Pulled                                                                                                                                                                                                                        7.4s 
 ✔ relay Pulled                                                                                                                                                                                                                         7.4s 
 ✔ zitadel Pulled                                                                                                                                                                                                                       6.8s 
 ✔ zdb Pulled                                                                                                                                                                                                                          19.9s 
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
[+] Running 11/11
 ✔ Volume "netbird_netbird_zitadel_certs"  Created                                                                                                                                                                                      0.0s 
 ✔ Volume "netbird_netbird_zdb_data"       Created                                                                                                                                                                                      0.0s 
 ✔ Volume "netbird_netbird_caddy_data"     Created                                                                                                                                                                                      0.0s 
 ✔ Container netbird-caddy-1               Started                                                                                                                                                                                      2.8s 
 ✔ Container netbird-dashboard-1           Started                                                                                                                                                                                      2.0s 
 ✔ Container netbird-signal-1              Started                                                                                                                                                                                      1.7s 
 ✔ Container netbird-relay-1               Started                                                                                                                                                                                      1.6s 
 ✔ Container netbird-management-1          Started                                                                                                                                                                                      2.2s 
 ✔ Container netbird-coturn-1              Started                                                                                                                                                                                      1.4s 
 ✔ Container netbird-zdb-1                 Healthy                                                                                                                                                                                     12.5s 
 ✔ Container netbird-zitadel-1             Started                                                                                                                                                                                     12.6s 

Step 4/4: Final service restart and verification...
Restarting all services with restored data...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 9/9
 ✔ Container netbird-zitadel-1     Removed                                                                                                                                                                                              0.5s 
 ✔ Container netbird-caddy-1       Removed                                                                                                                                                                                              1.0s 
 ✔ Container netbird-dashboard-1   Removed                                                                                                                                                                                              4.2s 
 ✔ Container netbird-signal-1      Removed                                                                                                                                                                                              0.7s 
 ✔ Container netbird-relay-1       Removed                                                                                                                                                                                              0.7s 
 ✔ Container netbird-management-1  Removed                                                                                                                                                                                              0.0s 
 ✔ Container netbird-coturn-1      Removed                                                                                                                                                                                              3.7s 
 ✔ Container netbird-zdb-1         Removed                                                                                                                                                                                              0.4s 
 ✔ Network netbird_netbird         Removed                                                                                                                                                                                              0.3s 
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 9/9
 ✔ Network netbird_netbird         Created                                                                                                                                                                                              0.1s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              2.0s 
 ✔ Container netbird-signal-1      Started                                                                                                                                                                                              1.7s 
 ✔ Container netbird-relay-1       Started                                                                                                                                                                                              1.8s 
 ✔ Container netbird-coturn-1      Started                                                                                                                                                                                              1.1s 
 ✔ Container netbird-dashboard-1   Started                                                                                                                                                                                              1.9s 
 ✔ Container netbird-caddy-1       Started                                                                                                                                                                                              2.3s 
 ✔ Container netbird-zdb-1         Healthy                                                                                                                                                                                              7.2s 
 ✔ Container netbird-zitadel-1     Started                                                                                                                                                                                              7.4s 
Waiting for services to start...
Warning: Some services may not have started properly.

=================================================
Netbird restore completed with ERRORS!
=================================================
Some components may not have been restored properly.
Check the output above for details.
You may need to:
1. Check service logs: docker compose logs
2. Restart services manually: docker compose restart
3. Verify network connectivity and DNS settings
COMPLETE restore failed.
" and here is docker logs after restore "root@remote:~/bash60# docker ps
CONTAINER ID   IMAGE                             COMMAND                  CREATED         STATUS                          PORTS                                                                                                                                                                          NAMES
83608c52e9d5   ghcr.io/zitadel/zitadel:v2.64.1   "/app/zitadel start-…"   3 minutes ago   Restarting (1) 35 seconds ago                                                                                                                                                                                  netbird-zitadel-1
1c924834bd14   caddy                             "caddy run --config …"   3 minutes ago   Up 3 minutes                    0.0.0.0:80->80/tcp, [::]:80->80/tcp, 0.0.0.0:443->443/tcp, [::]:443->443/tcp, 0.0.0.0:8080->8080/tcp, [::]:8080->8080/tcp, 0.0.0.0:443->443/udp, [::]:443->443/udp, 2019/tcp   netbird-caddy-1
7a951cf1feba   netbirdio/signal:latest           "/go/bin/netbird-sig…"   3 minutes ago   Up 3 minutes                                                                                                                                                                                                   netbird-signal-1
60afedf1ad12   netbirdio/dashboard:latest        "/usr/bin/supervisor…"   3 minutes ago   Up 3 minutes                    80/tcp, 443/tcp                                                                                                                                                                netbird-dashboard-1
45c9f3b40196   netbirdio/relay:latest            "/go/bin/netbird-rel…"   3 minutes ago   Up 3 minutes                                                                                                                                                                                                   netbird-relay-1
a18e5264b7db   coturn/coturn                     "docker-entrypoint.s…"   3 minutes ago   Up 3 minutes                                                                                                                                                                                                   netbird-coturn-1
96f0801eb03f   netbirdio/management:latest       "/go/bin/netbird-mgm…"   3 minutes ago   Restarting (1) 34 seconds ago                                                                                                                                                                                  netbird-management-1
c97c0f4ee61e   postgres:16-alpine                "docker-entrypoint.s…"   3 minutes ago   Up 3 minutes (healthy)          5432/tcp                                                                                                                                                                       netbird-zdb-1
root@remote:~/bash60# docker logs netbird-management-1
2025-06-10T12:50:57Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": EOF
2025-06-10T12:50:58Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": remote error: tls: internal error
2025-06-10T12:50:59Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": remote error: tls: internal error
2025-06-10T12:51:00Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": remote error: tls: internal error
2025-06-10T12:51:01Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": remote error: tls: internal error
2025-06-10T12:51:04Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": remote error: tls: internal error
2025-06-10T12:51:08Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": remote error: tls: internal error
2025-06-10T12:51:15Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": remote error: tls: internal error
2025-06-10T12:51:29Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": remote error: tls: internal error
2025-06-10T12:51:55Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": remote error: tls: internal error
2025-06-10T12:52:47Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": remote error: tls: internal error
2025-06-10T12:53:47Z INFO [context: SYSTEM] management/cmd/management.go:510: loading OIDC configuration from the provided IDP configuration endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration
Error: failed reading provided config file: /etc/netbird/management.json: failed fetching OIDC configuration from endpoint https://netbird.freeconvert4u.com/.well-known/openid-configuration Get "https://netbird.freeconvert4u.com/.well-known/openid-configuration": remote error: tls: internal error
root@remote:~/bash60# "