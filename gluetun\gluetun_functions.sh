install_gluetun_pia(){

  install_docker

  # Get all running containers using the Gluetun image
  local containers=$(docker ps -a --filter ancestor=fazee6/gluetun --format '{{.Names}}')
  
  if [ -n "$containers" ]; then
      echo "Found the following Gluetun containers:"
      echo "$containers" | nl
      
      read -p "Do you want to create another Gluetun container? [Y/n]: " create_new
      if [[ -n "$create_new" && ! "${create_new:0:1}" =~ [Yy] ]]; then
          echo "Installation cancelled."
          return 0
      fi
  fi

  # Clean up any existing PIA config files before generating new one
  if [ -d "/root/pia-wg" ]; then
      echo "Cleaning up old PIA config files..."
      find /root/pia-wg -type f -name "PIA-*.conf" -delete
  fi
  
  # Generate PIA WireGuard file
  generate_pia_wireguard_file
  
  # Get the newly generated config file
  conf_file=$(find /root/pia-wg -maxdepth 1 -name "PIA-*.conf" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -f2- -d" ")

  if [ -n "$conf_file" ]; then
      echo "Using configuration file: ${conf_file}"
      # Initialize variables
      ADDRESS=""
      PRIVATE_KEY=""
      DNS=""
      WIREGUARD_PUBLIC_KEY=""
      ENDPOINT_IP=""
      ENDPOINT_PORT=""

      while IFS= read -r line; do
          if [[ $line == "Address ="* ]]; then
              ADDRESS=$(echo $line | cut -d' ' -f3)
          elif [[ $line == "PrivateKey ="* ]]; then
              PRIVATE_KEY=$(echo $line | cut -d' ' -f3)
          elif [[ $line == "DNS ="* ]]; then
              DNS=$(echo $line | cut -d' ' -f3)
              # Fallback to common DNS servers if PIA DNS is not available
              if [ -z "$DNS" ] || [ "$DNS" = "********43" ]; then
                  # Use Cloudflare's DNS as fallback
                  DNS="*******"
                  echo "Warning: Using Cloudflare DNS (*******) as fallback since PIA DNS was not available"
              fi
          elif [[ $line == "PublicKey ="* ]]; then
              WIREGUARD_PUBLIC_KEY=$(echo $line | cut -d' ' -f3)
          elif [[ $line == "Endpoint ="* ]]; then
              ENDPOINT_IP=$(echo $line | cut -d' ' -f3 | cut -d':' -f1)
              ENDPOINT_PORT=$(echo $line | cut -d' ' -f3 | cut -d':' -f2)
          fi
      done < "$conf_file"

      # Validate that all required variables are set
      if [ -z "$ADDRESS" ] || [ -z "$PRIVATE_KEY" ] || [ -z "$DNS" ] || \
         [ -z "$WIREGUARD_PUBLIC_KEY" ] || [ -z "$ENDPOINT_IP" ] || [ -z "$ENDPOINT_PORT" ]; then
          echo "Error: WireGuard configuration file is missing required fields"
          echo "Please ensure the configuration file contains all necessary fields:"
          echo "- Address"
          echo "- PrivateKey"
          echo "- DNS"
          echo "- PublicKey"
          echo "- Endpoint"
          return 1
      fi

      # Get next available container name
      local default_name=$(get_next_gluetun_number)
      
      # Ask user for container name
      while true; do
          read -p "Enter container name [$default_name]: " CONTAINER_NAME
          CONTAINER_NAME=${CONTAINER_NAME:-$default_name}
          
          # Check if the chosen name already exists
          if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
              echo "Error: A container with name '$CONTAINER_NAME' already exists."
              read -p "Do you want to try a different name? [Y/n]: " retry
              if [[ -z "$retry" || "${retry:0:1}" =~ [Yy] ]]; then
                  continue
              else
                  echo "Installation cancelled."
                  return 1
              fi
          fi
          
          if [[ $CONTAINER_NAME =~ ^[a-zA-Z0-9_-]+$ ]]; then
              break
          else
              echo "Error: Container name can only contain letters, numbers, underscores, and hyphens."
              echo "Please try again."
              echo
          fi
      done

      # Port validation function
      validate_ports() {
          local ports=$1
          # Remove trailing spaces
          ports=$(echo "$ports" | sed 's/[[:space:]]*$//')
          
          # Check if input is empty
          if [ -z "$ports" ]; then
              return 1
          fi
          
          # Check each port
          for port in $ports; do
              # Check if port is a number and within valid range (1-65535)
              if ! [[ "$port" =~ ^[0-9]+$ ]] || [ "$port" -lt 1 ] || [ "$port" -gt 65535 ]; then
                  return 1
              fi
          done
          return 0
      }

      # Port input loop
      while true; do
          # Extract number from container name for default port calculation
          local number=$(echo "$CONTAINER_NAME" | grep -o '[0-9]*$')
          local default_port=$((5800 + number - 1))
          
          echo "Enter port(s) to expose (1 or more ports separated by spaces)"
          echo "Example: 5800 8080 9090"
          echo "Press Enter for default port ($default_port)"
          read -p "> " USER_PORTS
          
          # If empty, use default
          if [ -z "$USER_PORTS" ]; then
              PORTS="$default_port"
              PORT_MAPPINGS="-p ${default_port}:${default_port}/tcp"
              break
          fi

          # Validate ports
          if validate_ports "$USER_PORTS"; then
              PORTS=$USER_PORTS
              PORT_MAPPINGS=""
              for PORT in $PORTS; do
                  # Map both TCP and UDP for each port
                  if [ -z "$PORT_MAPPINGS" ]; then
                      PORT_MAPPINGS="-p ${PORT}:${PORT}/tcp -p ${PORT}:${PORT}/udp"
                  else
                      PORT_MAPPINGS="${PORT_MAPPINGS} -p ${PORT}:${PORT}/tcp -p ${PORT}:${PORT}/udp"
                  fi
              done
              break
          else
              echo "Error: Invalid port number(s). Ports must be between 1 and 65535."
              echo "Please try again."
              echo
          fi
      done

      # Instead of creating docker-compose.yml, run docker command directly
      docker run -d \
        --name=${CONTAINER_NAME} \
        --cap-add=NET_ADMIN \
        --device=/dev/net/tun \
        --dns ******* \
        --dns ******* \
        -e VPN_SERVICE_PROVIDER=custom \
        -e VPN_TYPE=wireguard \
        -e WIREGUARD_ENDPOINT_IP=${ENDPOINT_IP} \
        -e WIREGUARD_ENDPOINT_PORT=${ENDPOINT_PORT} \
        -e WIREGUARD_PUBLIC_KEY=${WIREGUARD_PUBLIC_KEY} \
        -e WIREGUARD_PRIVATE_KEY=${PRIVATE_KEY} \
        -e WIREGUARD_ADDRESSES=${ADDRESS}/32 \
        -e DNS=${DNS} \
        -e DOT=off \
        -e DNS_KEEP_NAMESERVER=false \
        -e FIREWALL_VPN_INPUT_PORTS="$(echo ${PORTS} | tr ' ' ',')" \
        -e HEALTH_TARGET_ADDRESS=******* \
        -e HEALTH_VPN_DURATION_INITIAL=30s \
        -e UPDATER_PERIOD=24h \
        -e TZ=$(cat /etc/timezone) \
        --restart unless-stopped \
        ${PORT_MAPPINGS} \
        $DOCKER_NETWORK \
        qmcgaw/gluetun:latest

      # Wait for container to initialize
      echo "Waiting for container to initialize..."
      sleep 5

      # Simple status check after container creation
      if docker ps | grep -q "${CONTAINER_NAME}"; then
          echo "Gluetun container '${CONTAINER_NAME}' started successfully with ports:"
          echo "Web UI: ${PORTS}"
          # When multiple ports are specified, only show VNC port for the first one
          first_port=$(echo $PORTS | awk '{print $1}')
          echo "VNC: $((first_port+100))"
          echo "Using DNS server: ${DNS}"
      else
          echo "Error: Container failed to start"
          return 1
      fi
  else
      echo "No PIA WireGuard configuration file found in /root/pia-wg/"
  fi
}

install_gluetun_fastestvpn() {
  echo "Installing Gluetun with FastestVPN configuration..."
  
  # Install Docker if not already installed
  install_docker

  # Get all running containers using the Gluetun image
  local containers=$(docker ps -a --filter ancestor=fazee6/gluetun --format '{{.Names}}')
  
  if [ -n "$containers" ]; then
      echo "Found the following Gluetun containers:"
      echo "$containers" | nl
      
      read -p "Do you want to create another Gluetun container? [Y/n]: " create_new
      if [[ -n "$create_new" && ! "${create_new:0:1}" =~ [Yy] ]]; then
          echo "Installation cancelled."
          return 0
      fi
  fi

  # Check if jq is installed for JSON parsing
  if ! command -v jq &> /dev/null; then
      echo "Installing jq for JSON parsing..."
      DEBIAN_FRONTEND=noninteractive sudo apt-get update >/dev/null 2>&1
      DEBIAN_FRONTEND=noninteractive sudo apt-get install -y jq >/dev/null 2>&1
  fi

  # Path to the FastestVPN server list
  local server_list_file="$(dirname "${BASH_SOURCE[0]}")/fastestvpn_server_list.json"
  
  if [ ! -f "$server_list_file" ]; then
      echo "Error: FastestVPN server list file not found at $server_list_file"
      echo "Current directory: $(pwd)"
      echo "Script location: ${BASH_SOURCE[0]}"
      return 1
  fi

  echo "Found server list file at: $server_list_file"
  echo "Checking file contents..."
  if ! jq . "$server_list_file" >/dev/null 2>&1; then
      echo "Error: Invalid JSON in server list file"
      return 1
  fi

  echo "Processing server list..."
  # Extract ALL country-city combinations from the JSON file
  echo "Available FastestVPN Servers:"
  
  # Read the server list with error handling
  local json_output
  json_output=$(jq -r '.[] | .country + ":" + .city + "|" + .ip_address + "|" + .domain' "$server_list_file" 2>&1)
  if [ $? -ne 0 ]; then
      echo "Error processing JSON file: $json_output"
      return 1
  fi

  local raw_servers=()
  while IFS= read -r line; do
      raw_servers+=("$line")
  done <<< "$json_output"

  if [ ${#raw_servers[@]} -eq 0 ]; then
      echo "Error: No servers found in the server list"
      return 1
  fi

  echo "Found ${#raw_servers[@]} servers"
  
  # Simple array to store servers
  local server_list=()
  
  # Process and store servers
  for server in "${raw_servers[@]}"; do
      if [ -n "$server" ]; then
          server_list+=("$server")
      fi
  done
  
  # Check if we have servers to display
  if [ ${#server_list[@]} -eq 0 ]; then
      echo "Error: No valid servers found"
      return 1
  fi
  
  # Display servers in a simple format
  echo
  echo "Available FastestVPN Servers:"
  echo "----------------------------"
  local counter=1
  
  for server in "${server_list[@]}"; do
      IFS='|' read -r location ip domain <<< "$server"
      echo "$counter) $location - $ip"
      ((counter++))
  done
  echo "----------------------------"
  echo
  
  # Get user selection
  while true; do
      read -p "Select a server (1-${#server_list[@]}): " server_choice
      
      if [[ "$server_choice" =~ ^[0-9]+$ ]] && [ "$server_choice" -ge 1 ] && [ "$server_choice" -le "${#server_list[@]}" ]; then
          selected_server="${server_list[$((server_choice-1))]}"
          IFS='|' read -r selected_country_city selected_server_ip selected_domain <<< "$selected_server"
          IFS=':' read -r selected_country selected_city <<< "$selected_country_city"
          echo "Selected: $selected_country_city ($selected_server_ip)"
          break
      else
          echo "Invalid selection. Please enter a number between 1 and ${#server_list[@]}."
      fi
  done
  
  echo "Selected server: $selected_country_city ($selected_server_ip)"

  # FastestVPN WireGuard configuration (predefined)
  local WIREGUARD_PRIVATE_KEY="yOLPOnN+pzly6VygYbjUnecHBc4jLcJRQcZSv5eWZmk="
  local WIREGUARD_ADDRESSES="*************/32"
  local WIREGUARD_DNS="********"
  local WIREGUARD_PUBLIC_KEY="658QxufMbjOTmB61Z7f+c7Rjg7oqWLnepTalqBERjF0="
  local WIREGUARD_ENDPOINT_PORT="51820"
  
  # Use the selected server IP as the endpoint
  local WIREGUARD_ENDPOINT_IP="$selected_server_ip"

  # Get next available container name
  local default_name=$(get_next_gluetun_number)
  
  # Ask user for container name
  while true; do
      read -p "Enter container name [$default_name]: " CONTAINER_NAME
      CONTAINER_NAME=${CONTAINER_NAME:-$default_name}
      
      # Check if the chosen name already exists
      if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
          echo "Error: A container with name '$CONTAINER_NAME' already exists."
          read -p "Do you want to try a different name? [Y/n]: " retry
          if [[ -z "$retry" || "${retry:0:1}" =~ [Yy] ]]; then
              continue
          else
              echo "Installation cancelled."
              return 1
          fi
      fi
      
      if [[ $CONTAINER_NAME =~ ^[a-zA-Z0-9_-]+$ ]]; then
          break
      else
          echo "Error: Container name can only contain letters, numbers, underscores, and hyphens."
          echo "Please try again."
          echo
      fi
  done

  # Port validation function
  validate_ports() {
      local ports=$1
      # Remove trailing spaces
      ports=$(echo "$ports" | sed 's/[[:space:]]*$//')
      
      # Check if input is empty
      if [ -z "$ports" ]; then
          return 1
      fi
      
      # Check each port
      for port in $ports; do
          # Check if port is a number and within valid range (1-65535)
          if ! [[ "$port" =~ ^[0-9]+$ ]] || [ "$port" -lt 1 ] || [ "$port" -gt 65535 ]; then
              return 1
          fi
      done
      return 0
  }

  # Port input loop
  while true; do
      # Extract number from container name for default port calculation
      local number=$(echo "$CONTAINER_NAME" | grep -o '[0-9]*$')
      local default_port=$((5800 + number - 1))
      
      echo "Enter port(s) to expose (1 or more ports separated by spaces)"
      echo "Example: 5800 8080 9090"
      echo "Press Enter for default port ($default_port)"
      read -p "> " USER_PORTS
      
      # If empty, use default
      if [ -z "$USER_PORTS" ]; then
          PORTS="$default_port"
          PORT_MAPPINGS="-p ${default_port}:${default_port}/tcp"
          break
      fi

      # Validate ports
      if validate_ports "$USER_PORTS"; then
          PORTS=$USER_PORTS
          PORT_MAPPINGS=""
          for PORT in $PORTS; do
              # Map both TCP and UDP for each port
              if [ -z "$PORT_MAPPINGS" ]; then
                  PORT_MAPPINGS="-p ${PORT}:${PORT}/tcp -p ${PORT}:${PORT}/udp"
              else
                  PORT_MAPPINGS="${PORT_MAPPINGS} -p ${PORT}:${PORT}/tcp -p ${PORT}:${PORT}/udp"
              fi
          done
          break
      else
          echo "Error: Invalid port number(s). Ports must be between 1 and 65535."
          echo "Please try again."
          echo
      fi
  done

  echo "Using FastestVPN configuration:"
  echo "Country: $selected_country"
  echo "City: $selected_city"
  echo "Endpoint: ${WIREGUARD_ENDPOINT_IP}:${WIREGUARD_ENDPOINT_PORT}"
  echo "Address: ${WIREGUARD_ADDRESSES}"
  echo "DNS: ${WIREGUARD_DNS}"
  echo "Container: $CONTAINER_NAME"
  echo "Ports: $PORTS"
  echo
  
  read -p "Do you want to proceed with the installation? [Y/n]: " confirm
  if [[ -n "$confirm" && ! "${confirm:0:1}" =~ [Yy] ]]; then
      echo "Installation cancelled."
      return 0
  fi

  # Create and start the Gluetun container with FastestVPN configuration
  echo "Creating Gluetun container with FastestVPN configuration..."
  docker run -d \
    --name="$CONTAINER_NAME" \
    --cap-add=NET_ADMIN \
    --device /dev/net/tun:/dev/net/tun \
    -e VPN_SERVICE_PROVIDER=custom \
    -e VPN_TYPE=wireguard \
    -e WIREGUARD_PRIVATE_KEY="$WIREGUARD_PRIVATE_KEY" \
    -e WIREGUARD_ADDRESSES="$WIREGUARD_ADDRESSES" \
    -e WIREGUARD_DNS="$WIREGUARD_DNS" \
    -e WIREGUARD_PUBLIC_KEY="$WIREGUARD_PUBLIC_KEY" \
    -e WIREGUARD_ENDPOINT_IP="$WIREGUARD_ENDPOINT_IP" \
    -e WIREGUARD_ENDPOINT_PORT="$WIREGUARD_ENDPOINT_PORT" \
    -e TZ=UTC \
    $PORT_MAPPINGS \
    --restart unless-stopped \
    fazee6/gluetun

  if [ $? -eq 0 ]; then
      echo "✓ Gluetun container '$CONTAINER_NAME' created successfully!"
      echo "✓ FastestVPN server: $selected_country_city ($selected_server_ip)"
      echo "✓ Exposed ports: $PORTS"
      echo
      echo "Container is starting up. You can check its status with:"
      echo "docker logs $CONTAINER_NAME"
  else
      echo "✗ Failed to create Gluetun container"
      return 1
  fi
}

install_gluetun_custom(){

  install_docker

  # Get all running containers using the Gluetun image
  local containers=$(docker ps -a --filter ancestor=fazee6/gluetun --format '{{.Names}}')
  
  if [ -n "$containers" ]; then
      echo "Found the following Gluetun containers:"
      echo "$containers" | nl
      
      read -p "Do you want to create another Gluetun container? [Y/n]: " create_new
      if [[ -n "$create_new" && ! "${create_new:0:1}" =~ [Yy] ]]; then
          echo "Installation cancelled."
          return 0
      fi
  fi

  # Get next available container name
  local default_name=$(get_next_gluetun_number)
  
  # Ask user for container name
  while true; do
      read -p "Enter container name [$default_name]: " CONTAINER_NAME
      CONTAINER_NAME=${CONTAINER_NAME:-$default_name}
      
      # Check if the chosen name already exists
      if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
          echo "Error: A container with name '$CONTAINER_NAME' already exists."
          read -p "Do you want to try a different name? [Y/n]: " retry
          if [[ -z "$retry" || "${retry:0:1}" =~ [Yy] ]]; then
              continue
          else
              echo "Installation cancelled."
              return 1
          fi
      fi
      
      if [[ $CONTAINER_NAME =~ ^[a-zA-Z0-9_-]+$ ]]; then
          break
      else
          echo "Error: Container name can only contain letters, numbers, underscores, and hyphens."
          echo "Please try again."
          echo
      fi
  done

  # Port validation function
  validate_ports() {
      local ports=$1
      # Remove trailing spaces
      ports=$(echo "$ports" | sed 's/[[:space:]]*$//')
      
      # Check if input is empty
      if [ -z "$ports" ]; then
          return 1
      fi
      
      # Check each port
      for port in $ports; do
          # Check if port is a number and within valid range (1-65535)
          if ! [[ "$port" =~ ^[0-9]+$ ]] || [ "$port" -lt 1 ] || [ "$port" -gt 65535 ]; then
              return 1
          fi
      done
      return 0
  }

  # Port input loop
  while true; do
      # Extract number from container name for default port calculation
      local number=$(echo "$CONTAINER_NAME" | grep -o '[0-9]*$')
      local default_port=$((5800 + number - 1))
      
      echo "Enter port(s) to expose (1 or more ports separated by spaces)"
      echo "Example: 5800 8080 9090"
      echo "Press Enter for default port ($default_port)"
      read -p "> " USER_PORTS
      
      # If empty, use default
      if [ -z "$USER_PORTS" ]; then
          PORTS="$default_port"
          PORT_MAPPINGS="-p ${default_port}:${default_port}/tcp"
          break
      fi

      # Validate ports
      if validate_ports "$USER_PORTS"; then
          PORTS=$USER_PORTS
          PORT_MAPPINGS=""
          for PORT in $PORTS; do
              # Map both TCP and UDP for each port
              if [ -z "$PORT_MAPPINGS" ]; then
                  PORT_MAPPINGS="-p ${PORT}:${PORT}/tcp -p ${PORT}:${PORT}/udp"
              else
                  PORT_MAPPINGS="${PORT_MAPPINGS} -p ${PORT}:${PORT}/tcp -p ${PORT}:${PORT}/udp"
              fi
          done
          break
      else
          echo "Error: Invalid port number(s). Ports must be between 1 and 65535."
          echo "Please try again."
          echo
      fi
  done

  # Prompt for custom config values
  echo "Enter custom WireGuard configuration details:"
  # Set VPN_SERVICE_PROVIDER to custom by default
  VPN_SERVICE_PROVIDER="custom"
  echo "VPN Service Provider: custom (set by default)"
  # Set VPN_TYPE to wireguard by default
  VPN_TYPE="wireguard"
  echo "VPN Type: wireguard (set by default)"
  read -p "Enter VPN Endpoint IP: " VPN_ENDPOINT_IP
  read -p "Enter VPN Endpoint Port: " VPN_ENDPOINT_PORT
  read -p "Enter WireGuard Public Key: " WIREGUARD_PUBLIC_KEY
  read -p "Enter WireGuard Private Key: " WIREGUARD_PRIVATE_KEY
  read -p "Enter WireGuard Addresses (e.g., ********/32): " WIREGUARD_ADDRESSES
  read -p "Enter DNS server(s) (space separated, e.g., ******* *******): " DNS_SERVERS

  # Instead of creating docker-compose.yml, run docker command directly
  docker run -d \
    --name=${CONTAINER_NAME} \
    --cap-add=NET_ADMIN \
    --device=/dev/net/tun \
    --dns ******* \
    --dns ******* \
    -e VPN_SERVICE_PROVIDER=${VPN_SERVICE_PROVIDER} \
    -e VPN_TYPE=${VPN_TYPE} \
    -e WIREGUARD_ENDPOINT_IP=${VPN_ENDPOINT_IP} \
    -e WIREGUARD_ENDPOINT_PORT=${VPN_ENDPOINT_PORT} \
    -e WIREGUARD_PUBLIC_KEY=${WIREGUARD_PUBLIC_KEY} \
    -e WIREGUARD_PRIVATE_KEY=${WIREGUARD_PRIVATE_KEY} \
    -e WIREGUARD_ADDRESSES=${WIREGUARD_ADDRESSES} \
    -e DNS="$(echo ${DNS_SERVERS} | tr ' ' ',')" \
    -e DOT=off \
    -e DNS_KEEP_NAMESERVER=false \
    -e FIREWALL_VPN_INPUT_PORTS="$(echo ${PORTS} | tr ' ' ',')" \
    -e HEALTH_TARGET_ADDRESS=******* \
    -e HEALTH_VPN_DURATION_INITIAL=30s \
    -e UPDATER_PERIOD=24h \
    -e TZ=$(cat /etc/timezone) \
    --restart unless-stopped \
    ${PORT_MAPPINGS} \
    $DOCKER_NETWORK \
    qmcgaw/gluetun:latest

  # Wait for container to initialize
  echo "Waiting for container to initialize..."
  sleep 5

  # Simple status check after container creation
  if docker ps | grep -q "${CONTAINER_NAME}"; then
      echo "Gluetun container '${CONTAINER_NAME}' started successfully with ports:"
      echo "Web UI: ${PORTS}"
      # When multiple ports are specified, only show VNC port for the first one
      first_port=$(echo $PORTS | awk '{print $1}')
      echo "VNC: $((first_port+100))"
      echo "Using DNS server(s): ${DNS_SERVERS}"
  else
      echo "Error: Container failed to start"
      return 1
  fi
}

remove_gluetun(){
local container_to_remove="$1" # Capture the optional container name argument
    # Find all containers using the gluetun image (any version)
    gluetun_containers=$(docker ps -a --format '{{.Names}}' | while read container; do
        if docker inspect "$container" | grep -q '"Image": "qmcgaw/gluetun'; then
            echo "$container"
        fi
    done)

    if [ -z "$gluetun_containers" ]; then
        echo "No Gluetun containers found."
        return
    fi

    # Check if a specific container name was provided as an argument and if it exists
    if [ -n "$container_to_remove" ] && echo "$gluetun_containers" | grep -q "^${container_to_remove}$"; then
        selected_container="$container_to_remove"
        echo "Removing specified Gluetun container: '$selected_container'"
    else
        # If no specific container was provided or found, proceed with interactive selection
        # If there's only one container, use it directly
        if [ "$(echo "$gluetun_containers" | wc -l)" -eq 1 ]; then
            selected_container=$gluetun_containers
        else
            # Show numbered list of containers
            echo "Found multiple Gluetun containers:"
            i=1
            while IFS= read -r container; do
                echo "$i) $container"
                i=$((i+1))
            done <<< "$gluetun_containers"

            # Ask user to select a container
            while true; do
                read -p "Enter the number of the container to remove (1-$((i-1))): " selection
                if [[ "$selection" =~ ^[0-9]+$ ]] && [ "$selection" -ge 1 ] && [ "$selection" -lt "$i" ]; then
                    selected_container=$(echo "$gluetun_containers" | sed -n "${selection}p")
                    break
                else
                    echo "Invalid selection. Please try again."
                fi
            done
        fi
    fi

    # Get selected container ID
    gluetun_id=$(docker inspect --format '{{.Id}}' "$selected_container")

    # Find containers using selected gluetun's network
    dependent_containers=$(docker ps -a --format '{{.Names}}' | while read container; do
        if docker inspect "$container" | grep -q "NetworkMode.*container:.*$gluetun_id"; then
            echo "$container"
        fi
    done)

    if [ -n "$dependent_containers" ]; then
        echo -e "\nWARNING: The following containers are using $selected_container's network:"
        echo "$dependent_containers"
        echo
        
        while true; do
            read -p "Do you want to remove these containers as well? (y/n): " yn
            case $yn in
                [Yy]*)
                    echo "Removing dependent containers..."
                    for container in $dependent_containers; do
                        if [ "$container" != "$selected_container" ]; then
                            echo "Removing container: $container"
                            docker rm -f "$container"
                        fi
                    done
                    break
                    ;;
                [Nn]*)
                    echo "Cannot remove Gluetun while other containers are using its network."
                    echo "Please remove the dependent containers first."
                    return
                    ;;
                *)
                    echo "Please answer y or n."
                    ;;
            esac
        done
    fi

    # Remove the selected gluetun container
    echo "Removing Gluetun container '$selected_container'..."
    docker rm -f "$selected_container"

    echo "Gluetun and all dependent containers have been removed successfully."
}

get_next_gluetun_number() {
    local prefix="gluetun-"
    local i=1
    
    while true; do
        if ! docker ps -a --format '{{.Names}}' | grep -q "^${prefix}${i}$"; then
            echo "${prefix}${i}"
            return
        fi
        ((i++))
    done
}

get_gluetun_port() {
    local container_name="$1"
    # Get port mapping from docker container
    local port_mapping=$(docker port "$container_name" 2>/dev/null | grep -E '[0-9]+->5800/tcp' | cut -d ':' -f2 | cut -d'-' -f1)
    
    if [ -n "$port_mapping" ]; then
        echo "$port_mapping"
    else
        # If container doesn't exist or no port mapping found, calculate next available port
        local prefix="gluetun-"
        local number=$(echo "$container_name" | sed "s/${prefix}//")
        if [[ "$number" =~ ^[0-9]+$ ]]; then
            echo $((5800 + number - 1))
        else
            echo "5800"
        fi
    fi
}
