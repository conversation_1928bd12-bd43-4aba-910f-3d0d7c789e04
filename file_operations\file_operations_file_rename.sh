#!/bin/bash

# File Renaming Functions
# Contains functions for bulk renaming files.

# Function to bulk rename files
bulk_rename_files() {
    while true; do
        echo -e "${YELLOW}Enter the path to work in:${NC}"
        read -r work_path
        work_path="${work_path%/}"

        if ! validate_path "$work_path"; then
            read -p "Press Enter to try again..."
            continue
        fi

        if [ ! -d "$work_path" ]; then
            echo -e "${RED}Invalid path: Directory does not exist${NC}"
            read -p "Press Enter to try again..."
            continue
        fi
        break
    done

    echo -e "${YELLOW}Search recursively in subdirectories? (y/n):${NC}"
    read -r recursive

    # Show available extensions
    echo -e "${GREEN}Available extensions in this path:${NC}"
    extensions=$(get_unique_extensions "$work_path" "$recursive")
    if [ -z "$extensions" ]; then
        echo -e "${YELLOW}No files with extensions found${NC}"
        read -p "Press Enter to continue..."
        return
    fi
    echo "$extensions"

    echo -e "${YELLOW}Enter file extension from above list (without dot, or press Enter for all files):${NC}"
    read -r ext_filter

    echo -e "${YELLOW}Choose rename operation:${NC}"
    echo "1. Add sequence number"
    echo "2. Remove pattern"
    read -r operation_choice

    case $operation_choice in
        1)  # Sequence number functionality
            if [ -n "$ext_filter" ]; then
                echo -e "${YELLOW}Enter additional search pattern (press Enter for all *.$ext_filter files):${NC}"
                read -r additional_pattern
                if [ -n "$additional_pattern" ]; then
                    search_pattern="$additional_pattern*.$ext_filter"
                else
                    search_pattern="*.$ext_filter"
                fi
            else
                echo -e "${YELLOW}Enter search pattern:${NC}"
                read -r search_pattern
            fi

            echo -e "${YELLOW}Enter replacement pattern (use # for number sequence):${NC}"
            read -r replacement

            # Find and display files that will be renamed
            echo -e "${GREEN}Preview of rename operations:${NC}"
            echo -e "Current Name -> New Name"
            echo -e "--------------------------------"

            count=1
            files_to_rename=()
            new_names=()

            if [ "$recursive" = "y" ]; then
                found_files=$(find "$work_path" -type f -name "$search_pattern")
            else
                found_files=$(find "$work_path" -maxdepth 1 -type f -name "$search_pattern")
            fi

            if [ -z "$found_files" ]; then
                echo -e "${YELLOW}No files found matching the pattern${NC}"
                read -p "Press Enter to continue..."
                return
            fi

            while IFS= read -r file; do
                new_name="$(dirname "$file")/$(echo "$replacement" | sed "s/#/$count/g")"
                if [ -e "$new_name" ]; then
                    echo -e "${RED}$file -> $new_name (Error: Target exists)${NC}"
                else
                    echo -e "${GREEN}$file -> $new_name${NC}"
                    files_to_rename+=("$file")
                    new_names+=("$new_name")
                fi
                ((count++))
            done <<< "$found_files"

            # Ask for confirmation
            echo -e "\n${YELLOW}Total files to rename: ${#files_to_rename[@]}${NC}"
            echo -e "${YELLOW}Proceed with rename? (yes/no):${NC}"
            read -r confirm

            if [ "$confirm" = "yes" ]; then
                for i in "${!files_to_rename[@]}"; do
                    mv "${files_to_rename[$i]}" "${new_names[$i]}"
                    echo -e "${GREEN}Renamed: ${files_to_rename[$i]} -> ${new_names[$i]}${NC}"
                done
                echo -e "${GREEN}Rename operations completed${NC}"
            else
                echo -e "${BLUE}Operation cancelled${NC}"
            fi
            ;;

        2)  # Pattern removal functionality
            if [ -n "$ext_filter" ]; then
                echo -e "${YELLOW}Enter additional search pattern (press Enter for all *.$ext_filter files):${NC}"
                read -r additional_pattern
                if [ -n "$additional_pattern" ]; then
                    search_pattern="$additional_pattern*.$ext_filter"
                else
                    search_pattern="*.$ext_filter"
                fi
            else
                echo -e "${YELLOW}Enter search pattern:${NC}"
                read -r search_pattern
            fi

            echo -e "${YELLOW}Enter the pattern to remove from filenames:${NC}"
            read -r remove_pattern

            # Find and display files that will be renamed
            echo -e "${GREEN}Preview of rename operations:${NC}"
            echo -e "Current Name -> New Name"
            echo -e "--------------------------------"

            files_to_rename=()
            new_names=()

            if [ "$recursive" = "y" ]; then
                found_files=$(find "$work_path" -type f -name "$search_pattern")
            else
                found_files=$(find "$work_path" -maxdepth 1 -type f -name "$search_pattern")
            fi

            if [ -z "$found_files" ]; then
                echo -e "${YELLOW}No files found matching the pattern${NC}"
                read -p "Press Enter to continue..."
                return
            fi

            while IFS= read -r file; do
                dir_path=$(dirname "$file")
                base_name=$(basename "$file")
                new_name="$dir_path/$(echo "$base_name" | sed "s/$remove_pattern//g")"

                if [ "$file" = "$new_name" ]; then
                    echo -e "${YELLOW}$file -> (No change needed)${NC}"
                elif [ -e "$new_name" ]; then
                    echo -e "${RED}$file -> $new_name (Error: Target exists)${NC}"
                else
                    echo -e "${GREEN}$file -> $new_name${NC}"
                    files_to_rename+=("$file")
                    new_names+=("$new_name")
                fi
            done <<< "$found_files"

            # Ask for confirmation
            echo -e "\n${YELLOW}Total files to rename: ${#files_to_rename[@]}${NC}"
            echo -e "${YELLOW}Proceed with rename? (yes/no):${NC}"
            read -r confirm

            if [ "$confirm" = "yes" ]; then
                for i in "${!files_to_rename[@]}"; do
                    mv "${files_to_rename[$i]}" "${new_names[$i]}"
                    echo -e "${GREEN}Renamed: ${files_to_rename[$i]} -> ${new_names[$i]}${NC}"
                done
                echo -e "${GREEN}Rename operations completed${NC}"
            else
                echo -e "${BLUE}Operation cancelled${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
    read -p "Press Enter to continue..."
}