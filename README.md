# Project Documentation

This documentation provides an overview of all modules in the project. Each module has its own detailed documentation file with information about its functionality, available functions, and integration points.

## Project Overview

This is a comprehensive suite of Bash scripts that provide functionality for various system administration, container management, file operations, networking, and security tasks. The project is organized into modular components, each handling specific functionality.

## Available Modules

### Core Infrastructure
- [Docker Module](docker/docker_module.md) - Docker container and image management
- [Utilities Module](utilities/utilities_module.md) - Core utility functions and package management
- [File Operations Module](file_operations/file_operations_module.md) - File and folder management, compression, and permissions

### Storage and Synchronization
- [Rclone Module](rclone/rclone_module.md) - Cloud storage synchronization and mounting

### Security
- [VPN Module](vpn/vpn_module.md) - VPN setup and management (WireGuard, V2Ray, XRay, etc.)
- [Encrypt Module](encrypt/encrypt_module.md) - File and folder encryption

### Media and Download Management
- [<PERSON><PERSON><PERSON>dule](jellyfin/jellyfin_module.md) - Media server management
- [JDownloader Module](jdownloader/jdownloader_module.md) - Download management

### Networking and Access
- [Headscale Module](headscale/headscale_module.md) - Headscale VPN management
- [Gluetun Module](gluetun/gluetun_module.md) - VPN container management
- [SOCKS5 Gluetun Module](socket5-gluetun/README.md) - Manage SOCKS5 proxy containers linked to Gluetun
- [File Server Module](file_server/file_server_module.md) - File server setup and management
- [Certificate Module](certificate/certificate_module.md) - SSL certificate management
- [AdGuard Module](adguard/adguard_module.md) - Ad blocking and DNS management

### User Interfaces and Management
- [CasaOS Module](casaos/casaos_module.md) - CasaOS management
- [OpenWeb UI Module](openweb_ui/openweb_ui_module.md) - Web UI management

### Development Tools
- [ByteStash Module](bytestash/bytestash_module.md) - Self-hosted code snippet storage and management

### Database
- [Database Module](database/database_module.md) - Database setup and management

## Common Functions Reference

Here's a quick reference of common functions across modules that may be useful when developing new functionality:

### Docker Functions
Docker functionality is provided by the [Docker Module](docker/docker_module.md). Use these functions for container management:
- `install_docker()` - Installs and configures Docker
- `pull_image_with_retry()` - Pulls Docker images with retry logic
- `rename_container()` - Renames Docker containers
- `remove_container()` - Removes Docker containers

### File Management
File operations are provided by the [File Operations Module](file_operations/file_operations_module.md):
- `validate_path()` - Validates file/folder paths
- `bulk_rename_files()` - Batch rename files
- `compress_items()` - Compress files/folders
- `decompress_archive()` - Decompress archives

### VPN Functions
VPN functionality is provided by the [VPN Module](vpn/vpn_module.md):
- `get_public_ip()` - Gets the current public IP
- `validate_input()` - Validates user input for VPN configuration

### Utility Functions
Core utilities are provided by the [Utilities Module](utilities/utilities_module.md):
- `log_message()` - Logs messages with appropriate formatting
- `handle_error()` - Error handling and reporting
- `manage_package()` - Package installation and management

## Documentation Structure

All module documentation is located within each module's respective directory for easy reference. Each module has its own markdown file that describes:
- Available functions and their purposes
- Usage instructions
- Integration points with other modules

## Adding New Modules

When adding a new module to the project, please follow these guidelines:

1. **Check Existing Functionality**: Before implementing new functionality, check the existing modules to avoid duplication. This documentation provides an overview of each module's capabilities.

2. **Module Structure**: Create a new directory for your module with descriptive shell script files.

3. **Documentation**: Create a markdown file documenting your module's functionality within the module's directory, following the same structure as existing module documentation.

4. **Integration**: Update this main documentation file to reference your new module.

5. **Function Naming**: Use clear, descriptive function names that reflect their purpose.

6. **Menu Integration**: Add your module to the main menu in main.sh.

## Usage

The project provides a menu-driven interface for accessing all functionality. Run the main script:

```bash
./main.sh
```

This will display the main menu, from which you can access all module functionality. 

## Conclusion

This documentation serves as a comprehensive guide to understanding and extending the project's modular architecture. By following the modular approach and properly documenting new components, we can ensure that:

1. **Code remains maintainable** - Each module has a clear, defined responsibility
2. **Duplication is avoided** - Existing functionality can be easily discovered and reused
3. **Integration is simplified** - Clear interface points between modules make integration straightforward
4. **Onboarding is accelerated** - New contributors can quickly understand the project's structure

When adding new functionality, always check the documentation first to see if a suitable module already exists or can be extended, rather than creating duplicate implementations. This practice promotes code reuse and maintains a clean, efficient codebase.