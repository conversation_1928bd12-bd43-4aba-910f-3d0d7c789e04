# ByteStash Module Documentation

## Overview

The ByteStash module provides a complete solution for managing ByteStash, a self-hosted code snippet storage application. This module includes Docker container management, automated backup/restore functionality using rclone, and comprehensive configuration options.

## Features

### Core Functionality
- **Container Management**: Install, start, stop, restart, and update ByteStash containers
- **Backup & Restore**: Automated backup to remote storage with compression and cleanup
- **Status Monitoring**: Real-time status checking and log viewing
- **Configuration Management**: Easy configuration of ports, directories, and environment variables
- **Scheduled Backups**: Automated backup scheduling with cron integration

### Backup System
- **Remote Storage**: Uses rclone to backup to `Eyunion:Backups/ByteSTash`
- **Compression**: Backups are compressed using tar.gz for efficiency
- **Single Backup Policy**: New backups replace previous ones to save space
- **Local Cleanup**: Automatic cleanup of temporary backup files
- **Restore Options**: Interactive restore with backup selection

## Available Functions

### Container Management Functions

#### `install_bytestash()`
- Installs ByteStash Docker container
- Creates data directory with proper permissions
- Configures environment variables including JWT secret
- Sets up container with restart policy

#### `start_bytestash()`
- Starts the ByteStash container
- Checks if already running to prevent conflicts
- Displays access URLs upon successful start

#### `stop_bytestash()`
- Gracefully stops the ByteStash container
- Provides status feedback

#### `restart_bytestash()`
- Restarts the ByteStash container
- Combines stop and start operations

#### `show_bytestash_status()`
- Displays current container status
- Shows running state, ports, and access URLs
- Provides quick health check information

### Backup & Restore Functions

#### `backup_bytestash()`
- Creates compressed backup of ByteStash data
- Uploads backup to remote storage using rclone
- Performs local cleanup after successful upload
- Includes error handling and status reporting

#### `restore_bytestash()`
- Lists available backups from remote storage
- Interactive backup selection
- Downloads and extracts selected backup
- Stops container during restoration
- Sets proper file permissions after restore

### Menu Functions

#### `bytestash_menu()`
- Main interactive menu for ByteStash management
- Stylish Unicode-based interface
- Organized sections for different functionalities

#### `bytestash_container_management()`
- Submenu for advanced container operations
- Container details, resource usage, export options
- Network information and troubleshooting

#### `bytestash_configuration()`
- Configuration management submenu
- Port changes, directory management
- Environment variable configuration

### Utility Functions

#### `view_bytestash_logs()`
- Real-time log viewing with tail functionality
- Displays last 50 lines by default
- Interactive log following

#### `update_bytestash()`
- Updates ByteStash to latest version
- Pulls new Docker image
- Recreates container with preserved data

#### `schedule_bytestash_backup()`
- Sets up automated backup scheduling
- Multiple schedule options (daily, weekly, monthly, custom)
- Cron integration with proper script creation

## Configuration

### Global Variables
- `BYTESTASH_CONTAINER_NAME`: Container name (default: "bytestash")
- `BYTESTASH_IMAGE`: Docker image (default: "ghcr.io/jordan-dalby/bytestash:latest")
- `BYTESTASH_PORT`: Web interface port (default: "5000")
- `BYTESTASH_DATA_DIR`: Data storage directory (default: "/opt/bytestash")
- `BYTESTASH_BACKUP_REMOTE`: Remote backup location (default: "${RCLONE_REMOTE}:Backups/ByteSTash")

### Environment Variables
- `BASE_PATH`: Application base path
- `JWT_SECRET`: JWT signing secret (auto-generated if not provided)
- `TOKEN_EXPIRY`: Token expiration time (default: "24h")
- `ALLOW_NEW_ACCOUNTS`: Allow new account creation (default: "true")
- `DEBUG`: Debug mode (default: "false")
- `DISABLE_ACCOUNTS`: Disable account system (default: "false")
- `DISABLE_INTERNAL_ACCOUNTS`: Disable internal accounts (default: "false")

## Dependencies

### Required Modules
- **Docker Module**: For container management functions
- **Rclone Module**: For backup/restore functionality
- **File Operations Module**: For compression and file handling
- **Utilities Module**: For package management and logging

### External Dependencies
- Docker (installed automatically if missing)
- Rclone (installed automatically if missing)
- tar (for compression)
- openssl (for JWT secret generation)

## Usage Examples

### Basic Installation
```bash
# Access ByteStash menu from main menu
./main.sh
# Select "ByteStash Menu"
# Choose "Install ByteStash"
```

### Manual Backup
```bash
# From ByteStash menu
# Select "Backup ByteStash Data"
# Backup will be created and uploaded automatically
```

### Scheduled Backup Setup
```bash
# From ByteStash menu
# Select "Schedule Auto Backup"
# Choose frequency (daily, weekly, monthly, or custom)
```

### Restore from Backup
```bash
# From ByteStash menu
# Select "Restore ByteStash Data"
# Choose from available backups
# Confirm restoration
```

## Access Information

### Web Interface
- **URL**: `http://{server_ip}:5000` (default, where {server_ip} is your server's IP)
- **API Documentation**: `http://{server_ip}:5000/api-docs`
- **Default Credentials**: Create account on first access

### Container Access
```bash
# View logs
docker logs bytestash

# Access container shell
docker exec -it bytestash /bin/sh
```

## Backup Strategy

### Backup Process
1. Create compressed archive of data directory
2. Upload to remote storage using rclone
3. Replace previous backup (single backup policy)
4. Clean up local temporary files
5. Verify upload success

### Restore Process
1. List available backups from remote
2. User selects backup to restore
3. Stop ByteStash container
4. Download selected backup
5. Extract to data directory
6. Set proper permissions
7. Clean up temporary files

## Troubleshooting

### Common Issues

#### Container Won't Start
- Check if port 5000 is already in use
- Verify data directory permissions
- Check Docker daemon status

#### Backup Fails
- Verify rclone configuration
- Check remote storage connectivity
- Ensure sufficient disk space

#### Restore Fails
- Verify backup file integrity
- Check file permissions
- Ensure container is stopped during restore

### Log Analysis
```bash
# View container logs
docker logs bytestash

# Follow logs in real-time
docker logs -f bytestash

# Check last 100 lines
docker logs --tail 100 bytestash
```

## Security Considerations

### JWT Secret
- Auto-generated 32-byte random secret
- Stored in container environment
- Required for session management

### File Permissions
- Data directory owned by user 1000:1000
- Proper permissions set during installation and restore
- Backup files cleaned up after operations

### Network Security
- Accessible via server IP address
- Consider reverse proxy for external access
- Use HTTPS in production environments

## Integration Points

### Main Menu Integration
The ByteStash module integrates with the main menu system:
```bash
# Add to main.sh menu options
"XX. ByteStash Menu"

# Add to main.sh case statement
XX) bytestash_menu ;;
```

### Rclone Integration
- Uses global `RCLONE_REMOTE` variable
- Leverages existing rclone functions
- Backup location: `${RCLONE_REMOTE}:Backups/ByteSTash`

### Docker Integration
- Uses existing Docker installation functions
- Follows project Docker network configuration
- Integrates with container management patterns

## Future Enhancements

### Planned Features
- Multi-backup retention policy
- Backup encryption options
- Database backup integration
- Health monitoring and alerts
- Backup verification and integrity checks
- Custom backup schedules with multiple frequencies

### Potential Integrations
- Integration with monitoring systems
- Webhook notifications for backup status
- Integration with other backup solutions
- API integration for automated management

## Module Files

- `bytestash_functions.sh`: Core functionality and container management
- `bytestash_menu.sh`: Interactive menu system
- `bytestash_module.md`: This documentation file

## Version History

### v1.0.0 (Initial Release)
- Basic container management
- Backup and restore functionality
- Interactive menu system
- Scheduled backup support
- Configuration management
- Integration with existing project modules