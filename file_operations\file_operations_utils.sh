#!/bin/bash

# Utility Functions for File Operations
# Contains color definitions, validation functions, path handling, etc.

# Color definitions for better presentation
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to validate path (no invalid characters)
validate_path() {
    local path="$1"
    # Check for invalid characters commonly problematic in paths
    if [[ "$path" =~ [\<\>\:\"\\\|\?\*] ]]; then
        echo -e "${RED}Error: Path contains invalid characters${NC}"
        return 1
    fi
    # Check for control characters
    if [[ "$path" =~ [[:cntrl:]] ]]; then
        echo -e "${RED}Error: Path contains control characters${NC}"
        return 1
    fi
    return 0
}

# Function to validate filename pattern
validate_pattern() {
    local pattern="$1"
    # Check for basic invalid characters
    if [[ "$pattern" =~ [\<\>\:\"\\\|] ]]; then
        echo -e "${RED}Error: Pattern contains invalid characters${NC}"
        return 1
    fi
    # Check for control characters
    if [[ "$pattern" =~ [[:cntrl:]] ]]; then
        echo -e "${RED}Error: Pattern contains control characters${NC}"
        return 1
    fi
    return 0
}

# Function to validate user input is not empty or just whitespace
validate_input() {
    local input="$1"
    local field="$2"
    if [[ -z "${input// /}" ]]; then
        echo -e "${RED}Error: $field cannot be empty${NC}"
        return 1
    fi
    return 0
}

# Function to sanitize path (handle spaces and special characters)
sanitize_path() {
    local path="$1"
    # Remove leading/trailing spaces and escape spaces in the middle
    echo "$path" | sed 's/^ *//;s/ *$//' | sed 's/ /\\ /g'
}

# Function to handle paths with spaces and special characters
handle_path() {
    local path="$1"
    # Remove trailing slash and spaces
    path="${path%/}"
    path="${path%"${path##*[![:space:]]}"}"
    path="${path#"${path%%[![:space:]]*}"}"
    # Return the path with proper quoting if it contains spaces
    if [[ "$path" == *[[:space:]]* ]]; then
        echo "\"$path\""
    else
        echo "$path"
    fi
}

# Function to get unique extensions in a path
get_unique_extensions() {
    local path="$1"
    local recursive="$2"

    if [ "$recursive" = "y" ]; then
        find "$path" -type f -name "*.*" | sed -n 's/.*\.\([^.]*\)$/\1/p' | sort -u
    else
        find "$path" -maxdepth 1 -type f -name "*.*" | sed -n 's/.*\.\([^.]*\)$/\1/p' | sort -u
    fi
}

# Function to validate numeric input
validate_numeric() {
    local input="$1"
    local max="$2"
    if ! [[ "$input" =~ ^[0-9]+$ ]] || [ "$input" -lt 1 ] || [ "$input" -gt "$max" ]; then
        echo -e "${RED}Error: Please enter a number between 1 and $max${NC}"
        return 1
    fi
    return 0
}

# Function to validate extension
validate_extension() {
    local ext="$1"
    # Check if extension contains only alphanumeric characters, dots, and hyphens
    if [[ ! "$ext" =~ ^[A-Za-z0-9._-]*$ ]]; then
        echo -e "${RED}Error: Invalid extension format${NC}"
        return 1
    fi
    return 0
}