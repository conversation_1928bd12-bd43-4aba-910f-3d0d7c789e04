#!/bin/bash


casaos_menu() {
    while true; do
        options=(
            "1. Install CasaOS"
            "2. Install CasaOS Apps"
            "3. Backup CasaOS Apps"
            "4. Restore CasaOS Apps"
            "5. Backup CasaOS Config"
            "6. Restore CasaOS Config"
            "7. Uninstall CasaOS Apps"
            "8. Uninstall CasaOS"
            "9. Back to Main Menu"
        )

        create_menu "CasaOS Menu" "${options[@]}" # Assuming create_menu is defined elsewhere
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) install_casaos ;;
            2) casaos_install_apps_menu ;;
            3) casaos_backup_menu ;;
            4) casaos_restore_menu ;;
            5) backup_casaos_config ;;
            6) restore_casaos_config ;;
            7) casaos_uninstall_menu ;;
            8) uninstall_casaos ;;
            9) return 0 ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac
    done
}
