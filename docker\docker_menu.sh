#!/bin/bash

# Define colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Source the docker functions
DIR="${BASH_SOURCE%/*}"
if [[ ! -d "$DIR" ]]; then DIR="$PWD"; fi
. "$DIR/docker_functions.sh"

# Function to create a menu with a title
create_menu() {
    local title=$1
    shift
    local options=("$@")
    
    echo -e "\n${CYAN}╔═══════════════════════════════╗${NC}"
    echo -e "${CYAN}║${NC}    ${YELLOW}$title${NC}       ${CYAN}║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════╝${NC}\n"
    
    for opt in "${options[@]}"; do
        echo -e "  ${YELLOW}$opt${NC}"
    done
    echo
}

docker_menu() {
    while true; do
        clear  # Clear screen before showing Docker Menu
        options=(
            "01. Install Docker"
            "02. Remove Docker"
            "03. Show Docker Status"
            "04. Clean Docker System"
            "05. Container Management >"
            "06. Image Operations >"
            "07. Return to Main Menu"
            "08. Exit"
        )

        create_menu "Docker Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1|01) 
                clear
                install_docker 
                read -n 1 -s -r -p "Press any key to continue..."
                ;;
            2|02) 
                clear
                remove_docker 
                read -n 1 -s -r -p "Press any key to continue..."
                ;;
            3|03)
                clear
                show_docker_status
                read -n 1 -s -r -p "Press any key to continue..."
                ;;
            4|04)
                clear
                clean_docker_system
                read -n 1 -s -r -p "Press any key to continue..."
                ;;
            5|05)
                clear
                docker_container_menu
                ;;
            6|06)
                clear
                docker_image_menu
                ;;
            7|07)
                clear
                return 0
                ;;
            8|08)
                clear
                exit 0
                ;;
            *)
                echo -e "${RED}Invalid choice. Please try again.${NC}"
                sleep 1
                ;;
        esac
    done
}

# Container Management Menu
docker_container_menu() {
    while true; do
        clear
        echo -e "${YELLOW}Container Management:${NC}"
        options=(
            "1. Show Container Status"
            "2. Restart Unless Stopped All Containers"
            "3. Rename Container"
            "4. Remove Container"
            "5. Return to Docker Menu"
        )

        create_menu "Container Management" "${options[@]}"
        
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1)
                clear
                show_docker_status
                read -n 1 -s -r -p "Press any key to continue..."
                ;;
            2)
                clear
                restart_unless_stopped
                read -n 1 -s -r -p "Press any key to continue..."
                ;;
            3)
                clear
                rename_container
                read -n 1 -s -r -p "Press any key to continue..."
                ;;
            4)
                clear
                remove_container
                read -n 1 -s -r -p "Press any key to continue..."
                ;;
            5)
                return 0
                ;;
            *)
                echo -e "${RED}Invalid choice. Please try again.${NC}"
                sleep 1
                ;;
        esac
    done
}

# Docker Image Operations Menu
docker_image_menu() {
    check_docker
    docker_login

    while true; do
        clear
        options=(
            "1. Pull and Push Custom Image"
            "2. Update Favorite Images"
            "3. Return to Docker Menu"
        )

        create_menu "Image Operations" "${options[@]}"
        
        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1)
                clear
                handle_custom_image
                ;;
            2)
                clear
                update_favorite_images
                ;;
            3)
                return 0
                ;;
            *)
                echo -e "${RED}Invalid choice. Please try again.${NC}"
                sleep 1
                ;;
        esac
    done
}

# Function to handle custom image operations
handle_custom_image() {
    local username="fazee6"

    if ! verify_push_access "$username"; then
        echo -e "${RED}Please fix the access issues and try again${NC}"
        read -n 1 -s -r -p "Press any key to continue..."
        return
    fi

    echo -e "${YELLOW}Enter the image name (e.g., jlesage/jdownloader-2 or nginx):${NC}"
    read image_name

    version=""
    pulled_image=""
    full_image_path="$image_name"
    arm64_pushed=false
    amd64_pushed=false
    last_version=""
    arm64_digest=""
    amd64_digest=""

    echo -e "${YELLOW}Checking for ARM64 version...${NC}"
    if pull_arm64_image "$full_image_path"; then
        push_image "$pulled_image" "arm64" "$version" "$username"
        arm64_pushed=true
        last_version=$version
        arm64_digest=$PUSHED_DIGEST
    else
        echo -e "${YELLOW}Skipping ARM64 version${NC}"
    fi

    version=""
    pulled_image=""

    echo -e "${YELLOW}Checking for AMD64 version...${NC}"
    if pull_amd64_image "$full_image_path"; then
        push_image "$pulled_image" "amd64" "$version" "$username"
        amd64_pushed=true
        last_version=$version
        amd64_digest=$PUSHED_DIGEST
    else
        echo -e "${YELLOW}Skipping AMD64 version${NC}"
    fi

    if [ "$arm64_pushed" = true ] && [ "$amd64_pushed" = true ]; then
        image_name=$(echo "$full_image_path" | awk -F'/' '{print $NF}')
        export DOCKER_CLI_EXPERIMENTAL=enabled
        echo -e "${YELLOW}Creating manifest with digests:${NC}"
        echo -e "ARM64: $arm64_digest"
        echo -e "AMD64: $amd64_digest"
        create_manifest "$image_name" "$username" "$last_version" "$arm64_digest" "$amd64_digest"
    else
        echo -e "${RED}Skipping manifest creation due to missing components${NC}"
        [ -z "$arm64_digest" ] && echo -e "${RED}No ARM64 digest available${NC}"
        [ -z "$amd64_digest" ] && echo -e "${RED}No AMD64 digest available${NC}"
        [ "$arm64_pushed" != true ] && echo -e "${RED}ARM64 version not pushed${NC}"
        [ "$amd64_pushed" != true ] && echo -e "${RED}AMD64 version not pushed${NC}"
    fi

    read -n 1 -s -r -p "Press any key to continue..."
}

# Update the push_image function to check for existing images
push_image() {
    local pulled_image="$1"
    local arch="$2"
    local version="$3"
    local username="$4"
    
    # Extract image name without registry/username
    local base_image_name=$(echo "$pulled_image" | awk -F'/' '{print $NF}')
    local target_tag="latest-${version}-${arch}"
    
    # Check if image already exists
    if check_image_exists "$username" "$base_image_name" "$target_tag"; then
        echo -e "${YELLOW}Image $username/$base_image_name:$target_tag already exists, skipping push${NC}"
        PUSHED_DIGEST=$(docker manifest inspect "$username/$base_image_name:$target_tag" | jq -r '.Descriptor.digest')
        return 0
    fi
    
    # Continue with existing push logic if image doesn't exist
    local target_image="$username/$base_image_name:$target_tag"
    echo "Tagging image as $target_image"
    docker tag "$pulled_image" "$target_image"
    
    echo "Pushing image $target_image"
    if docker push "$target_image"; then
        PUSHED_DIGEST=$(docker inspect --format='{{index .RepoDigests 0}}' "$target_image" | cut -d'@' -f2)
        echo -e "${GREEN}Successfully pushed $target_image${NC}"
        return 0
    else
        echo -e "${RED}Failed to push $target_image${NC}"
        return 1
    fi
}
