# Headscale Module

This module provides functionality for setting up and managing Headscale, a self-hosted, open-source implementation of the Tailscale control server, along with DERP server configuration and Tailscale client integration.

## Files

### headscale_functions.sh
Core functions for Headscale server management.

**Key Functions:**
- `ensure_expect_installed()`: Ensures expect utility is installed
- `list_nodes()`: Lists connected nodes
- `delete_node()`: Deletes a node from Headscale
- `rename_node()`: Renames a Headscale node
- `list_users()`: Lists Headscale users
- `create_user()`: Creates a new Headscale user
- `delete_user()`: Deletes a Headscale user
- `list_routes()`: Lists available routes
- `manage_route()`: Manages Headscale routes
- `list_preauthkeys()`: Lists pre-authentication keys
- `create_preauthkey()`: Creates a pre-authentication key

### headscale_derp_server.sh
Functions for setting up and managing DERP server alongside Headscale.

**Key Functions:**
- `select_domain()`: Selects a domain for the DERP server
- `extract_base_domain()`: Extracts the base domain
- `setup_derp_server()`: Sets up a DERP server
- `setup_headscale_server()`: Sets up a Headscale server
- `create_headscale_derp_config()`: Creates DERP configuration for Headscale
- `create_headscale_config()`: Creates Headscale configuration
- `check_headscale_status()`: Checks the status of Headscale server
- `delete_headscale_server()`: Deletes the Headscale server
- `backup_headscale_db()`: Backs up the Headscale database
- `restore_headscale_db()`: Restores the Headscale database from backup

### tailscale_client.sh
Functions for managing Tailscale clients connecting to Headscale.

**Key Functions:**
- `join_tailscale()`: Joins a Tailscale network using Headscale as the control server
- `force_reauth_tailscale()`: Forces reauthorization of a Tailscale client

### headscale_menu.sh
Provides menu interfaces for all Headscale operations.

**Key Functions:**
- `headscale_menu()`: Main menu for Headscale operations
- `setup_environment()`: Sets up the environment for Headscale
- `delete_headscale_server()`: Menu option to delete Headscale server
- `check_headscale_status()`: Checks Headscale server status
- `check_derp_status()`: Checks DERP server status
- `check_servers_status()`: Checks both Headscale and DERP server status
- `backup_headscale_db()`: Backs up Headscale database
- `restore_headscale_db()`: Restores Headscale database
- `node_management_menu()`: Menu for node management
- `user_management_menu()`: Menu for user management
- `route_management_menu()`: Menu for route management
- `preauthkey_management_menu()`: Menu for pre-authentication key management
- `tailscale_client_menu()`: Menu for Tailscale client operations

## Usage

The Headscale module provides comprehensive functionality for creating and managing a private, self-hosted VPN network using Headscale and Tailscale.

To access Headscale functionality:
1. Select "Headscale Menu" from the main menu
2. Choose from setup, management, backup, or client operations
3. Follow the interactive prompts to configure your Headscale environment

## Integration with Other Modules

The Headscale module integrates with:
- Docker module for container management
- Certificate module for SSL certificate management
- VPN module for networking functionality 