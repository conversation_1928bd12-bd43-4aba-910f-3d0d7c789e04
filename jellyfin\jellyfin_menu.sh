jellyfin_menu() {
    options=(
        "01. Install Jellyfin Fresh"
        "02. Backup Jellyfin"
        "03. Restore Jellyfin"
        "04. Return to Main Menu"
        "05. Exit"
    )

    while true; do
        create_menu "Jellyfin Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1) install_jellyfin ;;
             2) backup_jellyfin ;;
             3) restore_jellyfin_backup ;;
             4) return ;;
             5) exit ;;
             *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo # Add a blank line for better readability
    done
}